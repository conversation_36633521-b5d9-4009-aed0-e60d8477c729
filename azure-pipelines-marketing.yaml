# Docker
# Build a Docker image
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

#trigger: none

trigger:
  branches:
    include:
      - development
  paths:
    include:
      - 'projects/marketing/*'
      - 'package.json'
      - 'package-lock.json'
      - 'projects/shared/*'
      - 'angular.json'
      - 'tsconfig.json'

#schedules:
#  - cron: "0 */6 * * *"  # Run at 00:00, 06:00, 12:00, 18:00 UTC every day
#    displayName: "Every 6 hours deployment"
#    branches:
#      include:
#        - development

variables:
  - group: 'GIT-CREDENTIALS' # Variable group containing pat-token and username
  - name: AZURE_ACR_IMAGE_REPO
    value: $(acr-repo)
  - name: helmChartPath
    value: './marketing/helm'
  - name: helmReleaseName
    value: $(service-name)
  - name: helmNamespace
    value: $(namespace)
  - name: IMAGE_TAG
    value: $(Build.BuildId)
  - name: TARGET_BRANCH
    value: $(helm-branch)

resources:
  repositories:
    - repository: self

stages:
  - stage: Prepare
    displayName: 'Prepare Environment'
    jobs:
      - job: Setup
        displayName: 'Setup and Clone Repositories'
        pool:
          vmImage: ubuntu-latest
        steps:
          # Step 1: Checkout the application code repository
          - checkout: self
            fetchDepth: 0
            persistCredentials: true

          - script: |
              git config --global user.email "<EMAIL>"
              git config --global user.name "Ascendion Devops"
              git clone https://$(username):$(pat-token)@dev.azure.com/ascendionava/AAVA/_git/$(service-name) helmCharts
            displayName: 'Clone Repository'

          # Step 2: Verify the contents of the repositories
          - script: |
              echo "Listing contents of application code repository:"
              ls -lrt $(Build.SourcesDirectory)
              echo "Listing contents of Helm chart repository:"
              ls -lrt $(Build.SourcesDirectory)/helmCharts
            displayName: 'List repository contents'

          - script: |
              # Get current date in ddmmyyyyhhmmss format
              IMAGE_TAG=$(date +'%Y%m%d%H%M')
              echo "Generated IMAGE_TAG: $IMAGE_TAG"
              echo "##vso[task.setvariable variable=IMAGE_TAG;isOutput=true]$IMAGE_TAG"
            displayName: 'Set IMAGE_TAG to current date-time'
            name: SetImageTag

  - stage: CheckmarxScan
    displayName: 'Checkmarx AST Scan'
    dependsOn: Prepare
    jobs:
      - job: Checkmarx
        displayName: 'Run Checkmarx Scan'
        pool:
          vmImage: ubuntu-latest
        steps:
          - checkout: self
            fetchDepth: 0

          - task: Checkmarx AST@3
            displayName: 'Checkmarx SAST + SCA + Image + IaC Scan'
            inputs:
              CheckmarxService: 'cx_scan' # Service connection name
              projectName: '$(Build.Repository.Name)'
              branchName: '$(Build.SourceBranchName)'
              tenantName: 'ascendionava'
              #additionalOptions: '--threshold "sast-high=10; sast-medium=20"'

  # - stage: CodeQuality
  #   displayName: 'Code Quality Analysis'
  #   dependsOn: Prepare
  #   jobs:
  #     - job: SonarQube
  #       displayName: 'SonarQube Analysis'
  #       pool:
  #         vmImage: ubuntu-latest
  #       steps:
  #         - checkout: self
  #           fetchDepth: 0
  #
  #         # Prepare Analysis Configuration task
  #         - task: SonarQubePrepare@7
  #           displayName: 'Prepare SonarQube Analysis'
  #           inputs:
  #             SonarQube: 'sonarqube_scan'
  #             scannerMode: 'cli'
  #             configMode: 'manual'
  #             cliProjectKey: 'AVA_force-platform-api-fileindexer-new_da1a7781-496f-48a2-844f-6d80b40316a0'

  #        # Run Code Analysis task
  #        - task: SonarQubeAnalyze@7
  #          displayName: 'Run SonarQube Analysis'
  #          inputs:
  #            jdkversion: 'JAVA_HOME_17_X64'

  #       # Publish Quality Gate Result task
  #       - task: SonarQubePublish@7
  #         displayName: 'Publish SonarQube Quality Gate Result'
  #         inputs:
  #           pollingTimeoutSec: '300'

  - stage: Build
    displayName: 'Build and Push Docker Image'
    dependsOn:
      - Prepare
      - CheckmarxScan
    #  - CodeQuality
    variables:
      # CORRECT syntax for cross-stage variable reference
      IMAGE_TAG: $[stageDependencies.Prepare.Setup.outputs['SetImageTag.IMAGE_TAG']]
    jobs:
      - job: Build
        displayName: 'Build and Push Docker Image'
        pool:
          vmImage: ubuntu-latest
        steps:
          - checkout: self
            fetchDepth: 0

          # Debug: Show the IMAGE_TAG value
          - script: |
              echo "IMAGE_TAG value in Build stage: $(IMAGE_TAG)"
            displayName: 'Debug IMAGE_TAG'

          # Step 3: Build Docker image and push to Azure Container Registry (ACR)
          - task: Docker@2
            displayName: 'Build and Push Docker Image'
            inputs:
              containerRegistry: 'AAVA-ACR' # Name of the service connection to ACR
              repository: '$(AZURE_ACR_IMAGE_REPO)/$(Build.Repository.Name)-marketing' # Docker repository path
              command: 'buildAndPush'
              Dockerfile: '$(Build.SourcesDirectory)/projects/marketing/Dockerfile' # Path to the Dockerfile
              buildContext: $(Build.SourcesDirectory) # Context for Docker build
              tags: |
                $(IMAGE_TAG)

  - stage: Deploy
    displayName: 'Deploy Configuration'
    dependsOn:
      - Prepare
      - Build
    variables:
      # CORRECT syntax for cross-stage variable reference
      #IMAGE_TAG: $[stageDependencies.Build.Build.outputs['PassImageTag.IMAGE_TAG']]
      IMAGE_TAG: $[stageDependencies.Prepare.Setup.outputs['SetImageTag.IMAGE_TAG']]
    jobs:
      - job: UpdateHelmChart
        displayName: 'Update Helm Chart'
        pool:
          vmImage: ubuntu-latest
        steps:
          - checkout: self
            fetchDepth: 0
            persistCredentials: true

          # Debug: Show the IMAGE_TAG value
          - script: |
              echo "IMAGE_TAG value in Deploy stage: $(IMAGE_TAG)"
            displayName: 'Debug IMAGE_TAG'

          - script: |
              git config --global user.email "<EMAIL>"
              git config --global user.name "Ascendion Devops"
              git clone https://$(username):$(pat-token)@dev.azure.com/ascendionava/AAVA/_git/$(service-name) helmCharts
            displayName: 'Clone Helm Chart Repository'

          # Step 4: Push the image tag in deploy-dev branch for argocd deployment
          - script: |
              cd $(Build.SourcesDirectory)/helmCharts
              # Create and checkout new temporary branch from development
              git checkout $(TARGET_BRANCH)

              # Debug: Show current IMAGE_TAG before sed
              echo "About to update with IMAGE_TAG: $(IMAGE_TAG)"
              echo "Current values.yaml content:"
              cat $(helmChartPath)/values.yaml | grep -A5 -B5 "tag:"

              # Make changes in temporary branch
              sed -i "s/tag: .*/tag: \"$(IMAGE_TAG)\"/" $(helmChartPath)/values.yaml

              # Debug: Show updated values.yaml content
              echo "Updated values.yaml content:"
              cat $(helmChartPath)/values.yaml | grep -A5 -B5 "tag:"

              # Commit and push temporary branch
              git add $(helmChartPath)/values.yaml
              git config user.name "Ascendion Devops"
              git config user.email "<EMAIL>"
              git commit -m "Update image tag to $(IMAGE_TAG)"
              git push origin $(TARGET_BRANCH)
            displayName: 'Update and Push to Deploy Branch'
