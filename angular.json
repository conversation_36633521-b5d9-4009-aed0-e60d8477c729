{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"marketing": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/marketing", "sourceRoot": "projects/marketing/src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "projects/marketing/webpack.config.js"}, "outputPath": "dist/marketing", "index": "projects/marketing/src/index.html", "polyfills": ["zone.js"], "tsConfig": "projects/marketing/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/marketing/public", "output": "."}, {"glob": "**/*", "input": "projects/shared/public", "output": "."}, {"glob": "**/*", "input": "projects/marketing/src/assets", "output": "/assets/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/marketing/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["./node_modules"]}, "scripts": ["projects/marketing/public/localEnvSetup.js"], "main": "projects/marketing/src/main.ts", "commonChunk": false, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "50kb", "maximumError": "1MB"}], "baseHref": "/", "deployUrl": "/"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10MB"}, {"type": "anyComponentStyle", "maximumWarning": "50kb", "maximumError": "50kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "projects/marketing/src/environments/environment.ts", "with": "projects/marketing/src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "aot": true, "baseHref": "/", "deployUrl": "/"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "baseHref": "/", "deployUrl": "/"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"production": {"buildTarget": "marketing:build:production"}, "development": {"buildTarget": "marketing:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4200, "publicHost": "http://localhost:4200"}}, "extract-i18n": {"builder": "@angular-builders/custom-webpack:extract-i18n"}, "test": {"builder": "@angular-builders/custom-webpack:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/marketing/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/marketing/public", "output": "."}, {"glob": "**/*", "input": "projects/marketing/src/assets", "output": "/assets/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/marketing/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["./node_modules"]}, "scripts": []}}}}, "launchpad": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/launchpad", "sourceRoot": "projects/launchpad/src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "projects/launchpad/webpack.config.js"}, "outputPath": "dist/launchpad", "index": "projects/launchpad/src/index.html", "polyfills": ["zone.js"], "tsConfig": "projects/launchpad/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/launchpad/public", "output": "."}, {"glob": "**/*", "input": "projects/shared/public", "output": "."}, {"glob": "**/*", "input": "projects/launchpad/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/monaco-editor", "output": "/assets/monaco/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/launchpad/src/styles.scss"], "scripts": ["projects/launchpad/public/localEnvSetup.js"], "main": "projects/launchpad/src/main.ts", "commonChunk": false, "baseHref": "/launchpad/", "deployUrl": "/launchpad/"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "1MB", "maximumError": "50kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "projects/launchpad/src/environments/environment.ts", "with": "projects/launchpad/src/environments/environment.prod.ts"}], "baseHref": "/launchpad/", "deployUrl": "/launchpad/"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "baseHref": "/launchpad/", "deployUrl": "/launchpad/"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"production": {"buildTarget": "launchpad:build:production"}, "development": {"buildTarget": "launchpad:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4201, "publicHost": "http://localhost:4201"}}, "extract-i18n": {"builder": "@angular-builders/custom-webpack:extract-i18n"}, "test": {"builder": "@angular-builders/custom-webpack:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/launchpad/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/launchpad/public"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/launchpad/src/styles.scss"], "scripts": []}}}}, "console": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/console", "sourceRoot": "projects/console/src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "projects/console/webpack.config.js"}, "outputPath": "dist/console", "index": "projects/console/src/index.html", "polyfills": ["zone.js"], "tsConfig": "projects/console/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/console/public", "output": "."}, {"glob": "**/*", "input": "projects/shared/public", "output": "."}, {"glob": "**/*", "input": "projects/console/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/monaco-editor", "output": "/assets/monaco/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/console/src/styles.scss", "projects/console/src/assets/styles/animation.scss"], "stylePreprocessorOptions": {"includePaths": ["./node_modules"]}, "scripts": ["projects/console/public/localEnvSetup.js"], "main": "projects/console/src/main.ts", "commonChunk": false, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "50kb", "maximumError": "1MB"}], "baseHref": "/console/", "deployUrl": "/console/"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10MB"}, {"type": "anyComponentStyle", "maximumWarning": "50kb", "maximumError": "50kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "projects/console/src/environments/environment.ts", "with": "projects/console/src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "aot": true, "baseHref": "/console/", "deployUrl": "/console/"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "baseHref": "/console/", "deployUrl": "/console/"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"production": {"buildTarget": "console:build:production"}, "development": {"buildTarget": "console:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4202, "publicHost": "http://localhost:4202"}}, "extract-i18n": {"builder": "@angular-builders/custom-webpack:extract-i18n"}, "test": {"builder": "@angular-builders/custom-webpack:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/console/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/console/public", "output": "."}, {"glob": "**/*", "input": "projects/console/src/assets", "output": "/assets/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/console/src/styles.scss", "projects/console/src/assets/styles/animation.scss"], "stylePreprocessorOptions": {"includePaths": ["./node_modules"]}, "scripts": []}}}}, "experienceStudio": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/experience-studio", "sourceRoot": "projects/experience-studio/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/experience-studio", "index": "projects/experience-studio/src/index.html", "polyfills": ["projects/experience-studio/src/polyfills.ts"], "tsConfig": "projects/experience-studio/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/experience-studio/public", "output": "/"}, {"glob": "**/*", "input": "projects/shared/public", "output": "/"}, {"glob": "**/*", "input": "projects/experience-studio/src/assets", "output": "assets/"}, {"glob": "**/*", "input": "node_modules/monaco-editor", "output": "/assets/monaco/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "node_modules/@awe/play-comp-library/src/lib/styles/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/tokens.scss", "projects/experience-studio/src/styles.scss"], "scripts": ["projects/experience-studio/public/localEnvSetup.js"], "main": "projects/experience-studio/src/main.ts", "commonChunk": false, "baseHref": "/experience/", "deployUrl": "/experience/", "extraWebpackConfig": "projects/experience-studio/webpack.config.js"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "1MB", "maximumError": "200kb"}], "extraWebpackConfig": "projects/experience-studio/webpack.config.js", "outputHashing": "all", "fileReplacements": [{"replace": "projects/experience-studio/src/environments/environment.ts", "with": "projects/experience-studio/src/environments/environment.prod.ts"}], "baseHref": "/experience/", "deployUrl": "/experience/"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "baseHref": "/experience/", "deployUrl": "/experience/"}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"buildTarget": "experienceStudio:build:production", "extraWebpackConfig": "projects/experience-studio/webpack.prod.config.js"}, "development": {"buildTarget": "experienceStudio:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4203, "publicHost": "http://localhost:4203", "extraWebpackConfig": "projects/experience-studio/webpack.config.js"}}, "extract-i18n": {"builder": "@angular-builders/custom-webpack:extract-i18n"}, "test": {"builder": "@angular-builders/custom-webpack:karma", "options": {"polyfills": ["projects/experience-studio/src/polyfills.ts", "zone.js/testing"], "tsConfig": "projects/experience-studio/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/experience-studio/public", "output": "/"}, {"glob": "**/*", "input": "projects/play-comp-library/src/lib/assets", "output": "/assets/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/console/src/styles.scss"], "scripts": []}}}}, "product-studio": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/product-studio", "sourceRoot": "projects/product-studio/src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "projects/product-studio/webpack.config.js"}, "outputPath": "dist/product-studio", "index": "projects/product-studio/src/index.html", "main": "projects/product-studio/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/product-studio/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/product-studio/public", "output": "/"}, {"glob": "**/*", "input": "projects/shared/public", "output": "/"}, {"glob": "**/*", "input": "projects/product-studio/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/monaco-editor", "output": "/assets/monaco/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "node_modules/@awe/play-comp-library/src/lib/styles/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/tokens.scss", "projects/product-studio/src/styles.scss"], "scripts": ["projects/product-studio/public/localEnvSetup.js"], "commonChunk": false, "baseHref": "/product/", "deployUrl": "/product/"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "50kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "projects/product-studio/src/environments/environment.ts", "with": "projects/product-studio/src/environments/environment.prod.ts"}], "baseHref": "/product/", "deployUrl": "/product/"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "baseHref": "/product/", "deployUrl": "/product/"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "options": {"port": 4204, "publicHost": "http://localhost:4204", "buildTarget": "product-studio:build:development"}, "configurations": {"production": {"buildTarget": "product-studio:build:production"}, "development": {"buildTarget": "product-studio:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-builders/custom-webpack:extract-i18n"}, "test": {"builder": "@angular-builders/custom-webpack:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/product-studio/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/product-studio/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "projects/play-comp-library/src/lib/assets", "output": "/assets/"}], "styles": ["node_modules/@aava/play-core/src/lib/styles/main.scss", "projects/product-studio/src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "a994879d-2058-4e78-9973-abebd4e404dc"}}