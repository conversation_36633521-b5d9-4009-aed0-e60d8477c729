module.exports = {
  root: true,
  ignorePatterns: [
    'node_modules/**/*',
    'dist/**/*',
    'reports/**/*',
    'system/**/*',
    'projects/*/src/app/app.component.ts',
    'projects/shared/auth/components/login/login.component.ts',
  ],
  env: {
    browser: true,
    es2022: true,
    node: true,
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
  },
  overrides: [
    {
      files: ['*.ts'],
      parserOptions: {
        project: [
          'tsconfig.json',
          'projects/*/tsconfig.json',
          'projects/*/tsconfig.app.json',
          'projects/*/tsconfig.spec.json',
        ],
        createDefaultProgram: true,
      },
      extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:@angular-eslint/recommended',
        'plugin:@angular-eslint/template/process-inline-templates',
        'plugin:prettier/recommended',
      ],
      plugins: [
        '@angular-eslint',
        '@typescript-eslint',
        'prettier',
        'import',
        'unused-imports',
      ],
      rules: {
        // Angular-specific rules
        '@angular-eslint/directive-selector': [
          'error',
          {
            type: 'attribute',
            prefix: 'app',
            style: 'camelCase',
          },
        ],
        '@angular-eslint/component-selector': [
          'error',
          {
            type: 'element',
            prefix: 'app',
            style: 'kebab-case',
          },
        ],
        '@angular-eslint/no-empty-lifecycle-method': 'warn',

        // Code Quality Rules
        'prefer-const': 'error',
        'no-var': 'error',
        'prefer-arrow-callback': 'error',
        '@typescript-eslint/no-unused-vars': 'error',
        complexity: ['error', 10],
        'import/order': [
          'error',
          {
            groups: [
              'builtin',
              'external',
              'internal',
              'parent',
              'sibling',
              'index',
            ],
            'newlines-between': 'always',
            alphabetize: {
              order: 'asc',
              caseInsensitive: true,
            },
          },
        ],
        eqeqeq: 'error',
        'prefer-template': 'error',
        'no-throw-literal': 'error',
        'max-depth': ['warn', 4],
        'unused-imports/no-unused-imports': 'warn',

        // Play+ Logging Rules
        'no-console': [
          'error',
          {
            allow: ['warn', 'error'],
          },
        ],

        // Error Handling
        'prefer-promise-reject-errors': 'error',
        'no-alert': 'error',
        'no-debugger': 'error',

        // Security Rules
        'no-eval': 'error',
        'no-implied-eval': 'error',
        'no-new-func': 'error',
        'no-script-url': 'error',

        // TypeScript specific
        '@typescript-eslint/no-explicit-any': 'warn',

        // Formatting
        'prettier/prettier': 'error',
      },
    },
    {
      files: ['*.html'],
      extends: ['plugin:@angular-eslint/template/recommended'],
      rules: {
        '@angular-eslint/template/click-events-have-key-events': 'error',
        '@angular-eslint/template/no-autofocus': 'error',
        '@angular-eslint/template/no-positive-tabindex': 'error',
      },
    },
    {
      files: ['*.js'],
      extends: ['eslint:recommended', 'plugin:prettier/recommended'],
      rules: {
        'prefer-const': 'error',
        'no-var': 'error',
        'no-console': [
          'error',
          {
            allow: ['warn', 'error'],
          },
        ],
        'no-eval': 'error',
        'no-implied-eval': 'error',
        'no-new-func': 'error',
        'no-script-url': 'error',
      },
    },
    {
      files: ['*.spec.ts', '*.test.ts'],
      extends: ['eslint:recommended', 'plugin:@angular-eslint/recommended'],
      env: {
        jasmine: true,
      },
      rules: {
        '@angular-eslint/directive-selector': 'off',
        '@angular-eslint/component-selector': 'off',
        '@typescript-eslint/no-explicit-any': 'warn',
        'no-console': 'off',
        'no-debugger': 'off',
        'prefer-const': 'error',
        'no-var': 'error',
        'import/order': [
          'error',
          {
            groups: [
              'builtin',
              'external',
              'internal',
              'parent',
              'sibling',
              'index',
            ],
            'newlines-between': 'always',
          },
        ],
      },
    },
  ],
};
