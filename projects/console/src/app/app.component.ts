import {
  Component,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ElementRef,
} from '@angular/core';
import {
  TokenStorageService,
  AuthConfig,
  AuthService,
  CentralizedRedirectService,
  ThemeInitService,
  ThemeService,
  RevelioComponent,
  FooterComponent,
} from '@shared';
import { environment } from '../environments/environment';
import {
  AavaLayoutComponent,
  AavaRailNavigationSidebarComponent,
  RailNavigationConfig,
  AavaBreadcrumbsComponent,
} from '@aava/play-core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import {
  BreadcrumbService,
  BreadcrumbItem,
} from './services/breadcrumb.service';
import { Subscription, filter } from 'rxjs';
import { routes } from './app.routes';
@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    AavaLayoutComponent,
    AavaRailNavigationSidebarComponent,
    AavaBreadcrumbsComponent,
    RevelioComponent,
    FooterComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  leftPanelOpen = false;
  currentTheme = 'light';
  private routeSubscription?: Subscription;

  toggleRevelio: boolean = false;

  sidebarConfig: RailNavigationConfig = {
    headerIcon: {
      iconName: 'svgs/sidenav/ascendionLogo_sidenav.svg',
      label: '',
      action: 'ascendion-action',
      iconType: 'url',
      tooltip: '',
    },
    menuItems: [
      {
        id: 'dashboard',
        iconName: '',
        label: 'Home',
        action: 'dashboard-action',
        isSelected: true,
        tooltip: 'Dashboard',
        retainActiveState: true,
        iconType: 'lucide',
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none"><path d="M1.66732 9.66683C1.30998 9.66683 0.998429 9.53394 0.732651 9.26816C0.466873 9.00238 0.333984 8.69083 0.333984 8.3335V1.66683C0.333984 1.3095 0.466873 0.997941 0.732651 0.732163C0.998429 0.466386 1.30998 0.333496 1.66732 0.333496H8.33398C8.69132 0.333496 9.00287 0.466386 9.26865 0.732163C9.53443 0.997941 9.66732 1.3095 9.66732 1.66683V8.3335C9.66732 8.69083 9.53443 9.00238 9.26865 9.26816C9.00287 9.53394 8.69132 9.66683 8.33398 9.66683H1.66732ZM1.66732 21.6668C1.30998 21.6668 0.998429 21.5339 0.732651 21.2682C0.466873 21.0024 0.333984 20.6908 0.333984 20.3335V13.6668C0.333984 13.3095 0.466873 12.9979 0.732651 12.7322C0.998429 12.4664 1.30998 12.3335 1.66732 12.3335H8.33398C8.69132 12.3335 9.00287 12.4664 9.26865 12.7322C9.53443 12.9979 9.66732 13.3095 9.66732 13.6668V20.3335C9.66732 20.6908 9.53443 21.0024 9.26865 21.2682C9.00287 21.5339 8.69132 21.6668 8.33398 21.6668H1.66732ZM13.6673 9.66683C13.31 9.66683 12.9984 9.53394 12.7327 9.26816C12.4669 9.00238 12.334 8.69083 12.334 8.3335V1.66683C12.334 1.3095 12.4669 0.997941 12.7327 0.732163C12.9984 0.466386 13.31 0.333496 13.6673 0.333496H20.334C20.6913 0.333496 21.0029 0.466386 21.2686 0.732163C21.5344 0.997941 21.6673 1.3095 21.6673 1.66683V8.3335C21.6673 8.69083 21.5344 9.00238 21.2686 9.26816C21.0029 9.53394 20.6913 9.66683 20.334 9.66683H13.6673ZM13.6673 21.6668C13.31 21.6668 12.9984 21.5339 12.7327 21.2682C12.4669 21.0024 12.334 20.6908 12.334 20.3335V13.6668C12.334 13.3095 12.4669 12.9979 12.7327 12.7322C12.9984 12.4664 13.31 12.3335 13.6673 12.3335H20.334C20.6913 12.3335 21.0029 12.4664 21.2686 12.7322C21.5344 12.9979 21.6673 13.3095 21.6673 13.6668V20.3335C21.6673 20.6908 21.5344 21.0024 21.2686 21.2682C21.0029 21.5339 20.6913 21.6668 20.334 21.6668H13.6673ZM1.66732 8.3335H8.33398V1.66683H1.66732V8.3335ZM13.6673 8.3335H20.334V1.66683H13.6673V8.3335ZM13.6673 20.3335H20.334V13.6668H13.6673V20.3335ZM1.66732 20.3335H8.33398V13.6668H1.66732V20.3335Z" fill="#4C515B"/> </svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none"> <mask id="mask0_5731_61875" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="32"><rect width="32" height="32" fill="#D9D9D9"/></mask>  <g mask="url(#mask0_5731_61875)"><path d="M6.66732 14.6668C6.30998 14.6668 5.99843 14.5339 5.73265 14.2682C5.46687 14.0024 5.33398 13.6908 5.33398 13.3335V6.66683C5.33398 6.3095 5.46687 5.99794 5.73265 5.73216C5.99843 5.46639 6.30998 5.3335 6.66732 5.3335H13.334C13.6913 5.3335 14.0029 5.46639 14.2687 5.73216C14.5344 5.99794 14.6673 6.3095 14.6673 6.66683V13.3335C14.6673 13.6908 14.5344 14.0024 14.2687 14.2682C14.0029 14.5339 13.6913 14.6668 13.334 14.6668H6.66732ZM6.66732 26.6668C6.30998 26.6668 5.99843 26.5339 5.73265 26.2682C5.46687 26.0024 5.33398 25.6908 5.33398 25.3335V18.6668C5.33398 18.3095 5.46687 17.9979 5.73265 17.7322C5.99843 17.4664 6.30998 17.3335 6.66732 17.3335H13.334C13.6913 17.3335 14.0029 17.4664 14.2687 17.7322C14.5344 17.9979 14.6673 18.3095 14.6673 18.6668V25.3335C14.6673 25.6908 14.5344 26.0024 14.2687 26.2682C14.0029 26.5339 13.6913 26.6668 13.334 26.6668H6.66732ZM18.6673 14.6668C18.31 14.6668 17.9984 14.5339 17.7327 14.2682C17.4669 14.0024 17.334 13.6908 17.334 13.3335V6.66683C17.334 6.3095 17.4669 5.99794 17.7327 5.73216C17.9984 5.46639 18.31 5.3335 18.6673 5.3335H25.334C25.6913 5.3335 26.0029 5.46639 26.2686 5.73216C26.5344 5.99794 26.6673 6.3095 26.6673 6.66683V13.3335C26.6673 13.6908 26.5344 14.0024 26.2686 14.2682C26.0029 14.5339 25.6913 14.6668 25.334 14.6668H18.6673ZM18.6673 26.6668C18.31 26.6668 17.9984 26.5339 17.7327 26.2682C17.4669 26.0024 17.334 25.6908 17.334 25.3335V18.6668C17.334 18.3095 17.4669 17.9979 17.7327 17.7322C17.9984 17.4664 18.31 17.3335 18.6673 17.3335H25.334C25.6913 17.3335 26.0029 17.4664 26.2686 17.7322C26.5344 17.9979 26.6673 18.3095 26.6673 18.6668V25.3335C26.6673 25.6908 26.5344 26.0024 26.2686 26.2682C26.0029 26.5339 25.6913 26.6668 25.334 26.6668H18.6673Z" fill="var(--color-brand-primary)"/> </g></svg>',
      },
      {
        id: 'build',
        iconName: '',
        label: 'Build',
        action: 'build-action',
        isSelected: false,
        tooltip: 'Build',
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none"><path d="M6.00389 11.4666C6.41767 10.6393 6.86811 9.84275 7.35522 9.07697C7.84256 8.31119 8.38022 7.56241 8.96822 6.83064L6.92189 6.41264C6.78522 6.37864 6.65278 6.38297 6.52456 6.42564C6.39634 6.4683 6.28089 6.54097 6.17822 6.64364L2.94256 9.8793C2.89122 9.93064 2.87411 9.99053 2.89122 10.059C2.90834 10.1274 2.95111 10.1786 3.01956 10.2126L6.00389 11.4666ZM21.2759 1.50497C19.2246 1.65208 17.4181 2.10341 15.8566 2.85897C14.295 3.61453 12.8262 4.6803 11.4502 6.0563C10.4656 7.04097 9.60656 8.06664 8.87323 9.1333C8.13989 10.2 7.53567 11.2597 7.06056 12.3126L10.8349 16.0793C11.8878 15.6042 12.9519 15 14.0272 14.2666C15.1023 13.5333 16.1322 12.6743 17.1169 11.6896C18.4929 10.3136 19.5587 8.85042 20.3142 7.29997C21.0698 5.74953 21.5211 3.94864 21.6682 1.8973C21.6682 1.84264 21.6643 1.79219 21.6566 1.74597C21.6488 1.69997 21.6201 1.65219 21.5706 1.60264C21.521 1.55308 21.4732 1.52441 21.4272 1.51664C21.381 1.50886 21.3306 1.50497 21.2759 1.50497ZM14.1476 9.01797C13.7562 8.62641 13.5606 8.15675 13.5606 7.60897C13.5606 7.06119 13.7562 6.59153 14.1476 6.19997C14.5391 5.80841 15.0101 5.61264 15.5606 5.61264C16.111 5.61264 16.5819 5.80841 16.9732 6.19997C17.3648 6.59153 17.5606 7.06119 17.5606 7.60897C17.5606 8.15675 17.3648 8.62641 16.9732 9.01797C16.5819 9.4093 16.111 9.60497 15.5606 9.60497C15.0101 9.60497 14.5391 9.4093 14.1476 9.01797ZM11.6809 17.1616L12.9349 20.154C12.9691 20.2222 13.0203 20.2606 13.0886 20.2693C13.157 20.2778 13.2169 20.2563 13.2682 20.205L16.5039 16.995C16.6066 16.8923 16.6792 16.7769 16.7219 16.6486C16.7648 16.5204 16.7691 16.388 16.7349 16.2513L16.3169 14.205C15.5853 14.7932 14.8366 15.3295 14.0706 15.814C13.3048 16.2986 12.5082 16.7479 11.6809 17.1616ZM22.9449 1.3103C22.9347 3.53253 22.5133 5.58253 21.6809 7.4603C20.8484 9.33808 19.6075 11.1017 17.9579 12.7513C17.8723 12.8366 17.7911 12.9135 17.7142 12.982C17.6373 13.0504 17.5561 13.1273 17.4706 13.2126L18.0349 15.9743C18.1067 16.3332 18.0887 16.6811 17.9809 17.018C17.8733 17.3546 17.6896 17.6529 17.4296 17.9126L13.7169 21.6256C13.452 21.8905 13.1289 21.9884 12.7476 21.9193C12.3664 21.85 12.1041 21.6359 11.9606 21.277L10.3629 17.5103L5.63722 12.759L1.87056 11.1616C1.51167 11.0179 1.30056 10.7554 1.23722 10.3743C1.17411 9.99319 1.275 9.67008 1.53989 9.40497L5.25289 5.6923C5.51267 5.43253 5.81222 5.25308 6.15156 5.15397C6.49089 5.05464 6.84 5.04086 7.19889 5.11264L9.96056 5.67697C10.0459 5.59141 10.1184 5.51441 10.1782 5.44597C10.2382 5.37775 10.3109 5.30086 10.3962 5.2153C12.0458 3.56575 13.8094 2.32041 15.6872 1.4793C17.565 0.638414 19.615 0.212859 21.8372 0.202637C21.981 0.207748 22.1203 0.236304 22.2552 0.288304C22.3903 0.340526 22.516 0.424747 22.6322 0.540969C22.7485 0.657191 22.8283 0.778526 22.8719 0.90497C22.9154 1.03164 22.9398 1.16675 22.9449 1.3103ZM2.82189 16.5563C3.34322 16.035 3.977 15.7773 4.72322 15.7833C5.46945 15.7893 6.10322 16.053 6.62456 16.5743C7.14589 17.0956 7.40534 17.7294 7.40289 18.4756C7.40022 19.2219 7.13822 19.8556 6.61689 20.377C5.87489 21.1187 5.00267 21.5854 4.00022 21.777C2.99756 21.9683 1.98511 22.1127 0.962891 22.2103C1.06045 21.171 1.20922 20.1543 1.40922 19.1603C1.60922 18.1663 2.08011 17.2983 2.82189 16.5563ZM3.77322 17.5256C3.37834 17.9205 3.10445 18.3846 2.95156 18.918C2.79845 19.4513 2.686 20.0043 2.61422 20.577C3.18689 20.5052 3.73989 20.3897 4.27322 20.2306C4.80656 20.0717 5.27067 19.7949 5.66556 19.4C5.93222 19.1333 6.06811 18.8153 6.07323 18.446C6.07834 18.0769 5.94756 17.759 5.68089 17.4923C5.41422 17.2256 5.09634 17.0979 4.72722 17.109C4.35789 17.1201 4.03989 17.259 3.77322 17.5256Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none"><path d="M4.26862 11.8922C4.82929 10.6306 5.4724 9.42895 6.19795 8.28717C6.92373 7.14517 7.72073 6.05106 8.58895 5.00484L6.70195 4.6125C6.34284 4.54072 5.99495 4.5545 5.65829 4.65384C5.32162 4.75295 5.02329 4.93239 4.76329 5.19217L1.05062 8.90484C0.785731 9.16995 0.687842 9.49306 0.756953 9.87417C0.826286 10.2553 1.0404 10.5177 1.39929 10.6615L4.26862 11.8922ZM20.971 0.097168C18.9232 0.140057 16.9921 0.595612 15.1776 1.46383C13.363 2.33228 11.7334 3.48872 10.289 4.93317C9.35917 5.86317 8.53573 6.87339 7.81862 7.96383C7.10151 9.0545 6.46006 10.1947 5.89429 11.3845C5.78318 11.6221 5.72762 11.8652 5.72762 12.1138C5.72762 12.3627 5.82162 12.5812 6.00962 12.7692L9.43262 16.1665C9.62062 16.3545 9.83473 16.4528 10.075 16.4615C10.3152 16.4699 10.5541 16.4186 10.7916 16.3075C11.9814 15.7588 13.1215 15.1174 14.212 14.3832C15.3026 13.6489 16.313 12.8169 17.243 11.8872C18.6874 10.4427 19.8438 8.81317 20.7123 6.9985C21.5805 5.18406 22.0361 3.25284 22.079 1.20484C22.079 1.06128 22.0515 0.92628 21.9966 0.799835C21.942 0.673391 21.8565 0.552058 21.7403 0.435836C21.6241 0.319614 21.5027 0.234058 21.3763 0.179169C21.2498 0.124502 21.1147 0.097168 20.971 0.097168ZM13.6583 8.51783C13.2667 8.12628 13.071 7.65228 13.071 7.09584C13.071 6.53961 13.2667 6.06572 13.6583 5.67417C14.0496 5.28284 14.5235 5.08717 15.08 5.08717C15.6364 5.08717 16.1104 5.28284 16.502 5.67417C16.8933 6.06572 17.089 6.53961 17.089 7.09584C17.089 7.65228 16.8933 8.12628 16.502 8.51783C16.1104 8.90917 15.6364 9.10484 15.08 9.10484C14.5235 9.10484 14.0496 8.90917 13.6583 8.51783ZM10.2583 17.9075L11.489 20.7768C11.6325 21.1357 11.895 21.3511 12.2763 21.4228C12.6574 21.4946 12.9804 21.3981 13.2453 21.1332L16.9583 17.4205C17.2181 17.1605 17.3975 16.8566 17.4966 16.5088C17.596 16.1611 17.6096 15.8076 17.5376 15.4485L17.1533 13.5872C16.102 14.4554 15.0066 15.2524 13.8673 15.9782C12.728 16.7037 11.525 17.3468 10.2583 17.9075ZM1.86095 16.5282C2.38229 16.0066 3.01473 15.7488 3.75829 15.7548C4.50184 15.7608 5.13429 16.0245 5.65562 16.5458C6.17695 17.0674 6.43762 17.6999 6.43762 18.4435C6.43762 19.1871 6.17695 19.8195 5.65562 20.3408C4.91384 21.0828 4.04162 21.5495 3.03895 21.7408C2.03651 21.9324 1.02418 22.0768 0.00195312 22.1742C0.0992865 21.1348 0.249286 20.1182 0.451953 19.1242C0.654398 18.1302 1.12406 17.2648 1.86095 16.5282Z" fill="var(--color-brand-primary)"/></svg>',
        iconType: 'lucide',
        studioHeading: 'Build',
        studioCards: [
          {
            id: 'agent',
            title: 'Agent',
            description: 'It is an autonomous entity that can perceive its environment, reason for its actions defined by its role and goal.',
            image: 'svgs/sidenav/agent-nav.svg',
          },
          {
            id: 'pipeline',
            title: 'Pipeline',
            description: 'It is an  sequences of steps or tasks that are executed to achieve a desired outcome.',
            image: 'svgs/sidenav/pipeline-nav.svg',
          },
          {
            id: 'model',
            title: 'Model',
            description: 'A unified interface that seamlessly connects users to a variety of open AI models, delivering flexibility and convenience.',
            image: 'svgs/sidenav/modals-nav.svg',
          },
          {
            id: 'tool',
            title: 'Tool',
            description: 'It is an external function, API, or utility that agents can invoke to gather information or trigger actions.',
            image: 'svgs/sidenav/tools-nav.svg',
          },
          {
            id: 'guardrail',
            title: 'Guardrail',
            description: 'It is an set of guidelines designed to ensure responsible AI use. They act as safeguards.',
            image: 'svgs/sidenav/guardrails-nav.svg',
          },
          {
            id: 'knowledge',
            title: 'Knowledge Base',
            description: 'It combines the strengths of traditional information retrieval systems with the capabilities of generative large language models (LLMs).',
            image: 'svgs/sidenav/kb-nav.svg',
          },
        ],
      },
      {
        id: 'marketplace',
        iconName: 'square-m',
        label: '  Cortex',
        action: 'marketplace-action',
        isSelected: false,
        tooltip: 'Marketplace',
        iconType: 'lucide',
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="26" height="24" viewBox="0 0 26 24" fill="none"><path d="M3.82866 23.3332C3.21488 23.3332 2.70244 23.1276 2.29132 22.7165C1.88021 22.3054 1.67466 21.7929 1.67466 21.1792V10.4768C1.11221 10.0615 0.700658 9.51706 0.439992 8.8435C0.179325 8.16995 0.17377 7.45117 0.423325 6.68717L1.72099 2.41017C1.89877 1.86662 2.18544 1.44006 2.58099 1.1305C2.97677 0.82117 3.4661 0.666504 4.04899 0.666504H21.9157C22.4985 0.666504 22.9865 0.811392 23.3797 1.10117C23.773 1.39095 24.0611 1.81017 24.244 2.35884L25.5927 6.68717C25.8422 7.45117 25.8367 8.17595 25.576 8.8615C25.3153 9.54684 24.9038 10.1024 24.3413 10.5282V21.1792C24.3413 21.7929 24.1358 22.3054 23.7247 22.7165C23.3135 23.1276 22.8012 23.3332 22.1877 23.3332H3.82866ZM15.9413 9.99984C16.7978 9.99984 17.4371 9.76006 17.8593 9.2805C18.2815 8.80117 18.4593 8.30595 18.3927 7.79484L17.5053 1.99984H13.6747V7.59984C13.6747 8.2545 13.8985 8.81817 14.3463 9.29084C14.7943 9.7635 15.326 9.99984 15.9413 9.99984ZM9.94133 9.99984C10.6575 9.99984 11.2367 9.7635 11.6787 9.29084C12.1204 8.81817 12.3413 8.2545 12.3413 7.59984V1.99984H8.51066L7.62332 7.89717C7.56866 8.31095 7.74944 8.76484 8.16566 9.25884C8.58188 9.75284 9.17377 9.99984 9.94133 9.99984ZM4.00799 9.99984C4.59599 9.99984 5.09555 9.79984 5.50666 9.39984C5.91777 8.99984 6.17377 8.50073 6.27466 7.9025L7.11066 1.99984H4.04899C3.75832 1.99984 3.52755 2.06395 3.35666 2.19217C3.18577 2.32039 3.05755 2.51273 2.97199 2.76917L1.74132 6.97917C1.52244 7.68006 1.61599 8.35873 2.02199 9.01517C2.42799 9.67162 3.08999 9.99984 4.00799 9.99984ZM22.008 9.99984C22.8062 9.99984 23.4439 9.68873 23.921 9.0665C24.3979 8.44428 24.5158 7.7485 24.2747 6.97917L22.9773 2.71784C22.8918 2.46139 22.7635 2.27762 22.5927 2.1665C22.4218 2.05539 22.191 1.99984 21.9003 1.99984H18.9053L19.7413 7.9025C19.8422 8.50073 20.0982 8.99984 20.5093 9.39984C20.9204 9.79984 21.42 9.99984 22.008 9.99984ZM3.82866 21.9998H22.1877C22.4268 21.9998 22.6233 21.923 22.7773 21.7692C22.9311 21.6152 23.008 21.4185 23.008 21.1792V11.1638C22.8284 11.2254 22.6579 11.2691 22.4963 11.2948C22.335 11.3204 22.1722 11.3332 22.008 11.3332C21.408 11.3332 20.8802 11.2161 20.4247 10.9818C19.9691 10.7476 19.5362 10.3742 19.126 9.8615C18.7773 10.2955 18.3474 10.6493 17.8363 10.9228C17.3252 11.1964 16.7038 11.3332 15.972 11.3332C15.4404 11.3332 14.9349 11.2093 14.4553 10.9615C13.976 10.7135 13.4935 10.3468 13.008 9.8615C12.5618 10.3468 12.0673 10.7135 11.5247 10.9615C10.982 11.2093 10.4644 11.3332 9.97199 11.3332C9.4131 11.3332 8.86832 11.2263 8.33766 11.0125C7.80677 10.7989 7.34133 10.4153 6.94133 9.8615C6.28666 10.5162 5.71444 10.9251 5.22466 11.0882C4.73488 11.2515 4.32932 11.3332 4.00799 11.3332C3.84399 11.3332 3.67866 11.3204 3.51199 11.2948C3.34532 11.2691 3.17732 11.2254 3.00799 11.1638V21.1792C3.00799 21.4185 3.08488 21.6152 3.23866 21.7692C3.39266 21.923 3.58933 21.9998 3.82866 21.9998Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="26" height="24" viewBox="0 0 26 24" fill="none"><path d="M3.82866 23.3332C3.21488 23.3332 2.70244 23.1276 2.29132 22.7165C1.88021 22.3054 1.67466 21.7929 1.67466 21.1792V10.4768C1.11221 10.0615 0.700658 9.51706 0.439992 8.8435C0.179325 8.16995 0.17377 7.45117 0.423325 6.68717L1.72099 2.41017C1.89877 1.86662 2.18544 1.44006 2.58099 1.1305C2.97677 0.82117 3.4661 0.666504 4.04899 0.666504H21.9157C22.4985 0.666504 22.9865 0.811392 23.3797 1.10117C23.773 1.39095 24.0611 1.81017 24.244 2.35884L25.5927 6.68717C25.8422 7.45117 25.8367 8.17595 25.576 8.8615C25.3153 9.54684 24.9038 10.1024 24.3413 10.5282V21.1792C24.3413 21.7929 24.1358 22.3054 23.7247 22.7165C23.3135 23.1276 22.8012 23.3332 22.1877 23.3332H3.82866ZM15.9413 9.99984C16.7978 9.99984 17.4371 9.76006 17.8593 9.2805C18.2815 8.80117 18.4593 8.30595 18.3927 7.79484L17.5053 1.99984H13.6747V7.59984C13.6747 8.2545 13.8985 8.81817 14.3463 9.29084C14.7943 9.7635 15.326 9.99984 15.9413 9.99984ZM9.94133 9.99984C10.6575 9.99984 11.2367 9.7635 11.6787 9.29084C12.1204 8.81817 12.3413 8.2545 12.3413 7.59984V1.99984H8.51066L7.62332 7.89717C7.56866 8.31095 7.74944 8.76484 8.16566 9.25884C8.58188 9.75284 9.17377 9.99984 9.94133 9.99984ZM4.00799 9.99984C4.59599 9.99984 5.09555 9.79984 5.50666 9.39984C5.91777 8.99984 6.17377 8.50073 6.27466 7.9025L7.11066 1.99984H4.04899C3.75832 1.99984 3.52755 2.06395 3.35666 2.19217C3.18577 2.32039 3.05755 2.51273 2.97199 2.76917L1.74132 6.97917C1.52244 7.68006 1.61599 8.35873 2.02199 9.01517C2.42799 9.67162 3.08999 9.99984 4.00799 9.99984ZM22.008 9.99984C22.8062 9.99984 23.4439 9.68873 23.921 9.0665C24.3979 8.44428 24.5158 7.7485 24.2747 6.97917L22.9773 2.71784C22.8918 2.46139 22.7635 2.27762 22.5927 2.1665C22.4218 2.05539 22.191 1.99984 21.9003 1.99984H18.9053L19.7413 7.9025C19.8422 8.50073 20.0982 8.99984 20.5093 9.39984C20.9204 9.79984 21.42 9.99984 22.008 9.99984Z" fill="var(--color-brand-primary)"/></svg>',
      },
      {
        id: 'approvals',
        iconName: 'file-check',
        label: 'Approvals',
        action: 'approvals-action',
        isSelected: false,
        tooltip: 'Approvals',
        iconType: 'lucide',
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="25" viewBox="0 0 20 25" fill="none"><path d="M9.99935 24.4873C9.76179 24.4873 9.53535 24.4513 9.32002 24.3793C9.10446 24.3076 8.90013 24.1999 8.70702 24.0563L1.52768 18.6717C1.26257 18.4786 1.05268 18.2273 0.898016 17.918C0.743349 17.6087 0.666016 17.2856 0.666016 16.9487V2.154C0.666016 1.54022 0.871571 1.02778 1.28268 0.616667C1.69379 0.205556 2.20624 0 2.82002 0H17.1787C17.7925 0 18.3049 0.205556 18.716 0.616667C19.1271 1.02778 19.3327 1.54022 19.3327 2.154V16.9487C19.3327 17.2856 19.2553 17.6087 19.1007 17.918C18.946 18.2273 18.7361 18.4786 18.471 18.6717L11.2917 24.0563C11.0986 24.1999 10.8942 24.3076 10.6787 24.3793C10.4633 24.4513 10.2369 24.4873 9.99935 24.4873ZM9.51202 22.9743C9.6489 23.0941 9.81135 23.154 9.99935 23.154C10.1873 23.154 10.3498 23.0941 10.4867 22.9743L17.666 17.5897C17.7687 17.5043 17.8499 17.4061 17.9097 17.295C17.9695 17.1839 17.9993 17.0599 17.9993 16.923V2.154C17.9993 1.94867 17.9139 1.76056 17.743 1.58967C17.5721 1.41878 17.384 1.33333 17.1787 1.33333H2.82002C2.61468 1.33333 2.42657 1.41878 2.25568 1.58967C2.08479 1.76056 1.99935 1.94867 1.99935 2.154V16.923C1.99935 17.0599 2.02924 17.1839 2.08902 17.295C2.14879 17.4061 2.23002 17.5043 2.33268 17.5897L9.51202 22.9743ZM8.59935 13.1743L6.22002 10.795C6.09002 10.665 5.93702 10.597 5.76102 10.591C5.58479 10.585 5.42324 10.653 5.27635 10.795C5.12924 10.9419 5.05446 11.1004 5.05202 11.2707C5.04935 11.4407 5.12157 11.5991 5.26868 11.746L7.84535 14.323C8.0609 14.5383 8.31224 14.646 8.59935 14.646C8.88646 14.646 9.13779 14.5383 9.35335 14.323L14.7377 8.93833C14.8677 8.80856 14.9357 8.65556 14.9417 8.47933C14.9477 8.30333 14.8797 8.14189 14.7377 7.995C14.5908 7.84789 14.4322 7.77311 14.262 7.77067C14.092 7.768 13.9336 7.84022 13.7867 7.98733L8.59935 13.1743Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="25" viewBox="0 0 20 25" fill="none"><path d="M9.99935 24.4873C9.76179 24.4873 9.53535 24.4513 9.32002 24.3793C9.10446 24.3076 8.90013 24.1999 8.70702 24.0563L1.52768 18.6717C1.26257 18.4786 1.05268 18.2273 0.898016 17.918C0.743349 17.6087 0.666016 17.2856 0.666016 16.9487V2.154C0.666016 1.54022 0.871571 1.02778 1.28268 0.616667C1.69379 0.205556 2.20624 0 2.82002 0H17.1787C17.7925 0 18.3049 0.205556 18.716 0.616667C19.1271 1.02778 19.3327 1.54022 19.3327 2.154V16.9487C19.3327 17.2856 19.2553 17.6087 19.1007 17.918C18.946 18.2273 18.7361 18.4786 18.471 18.6717L11.2917 24.0563C11.0986 24.1999 10.8942 24.3076 10.6787 24.3793C10.4633 24.4513 10.2369 24.4873 9.99935 24.4873ZM8.59935 13.1743L6.22002 10.795C6.09002 10.665 5.93702 10.597 5.76102 10.591C5.58479 10.585 5.42324 10.653 5.27635 10.795C5.12924 10.9419 5.05446 11.1004 5.05202 11.2707C5.04935 11.4407 5.12157 11.5991 5.26868 11.746L7.84535 14.323C8.0609 14.5383 8.31224 14.646 8.59935 14.646C8.88646 14.646 9.13779 14.5383 9.35335 14.323L14.7377 8.93833C14.8677 8.80856 14.9357 8.65556 14.9417 8.47933C14.9477 8.30333 14.8797 8.14189 14.7377 7.995C14.5908 7.84789 14.4322 7.77311 14.262 7.77067C14.092 7.768 13.9336 7.84022 13.7867 7.98733L8.59935 13.1743Z" fill="var(--color-brand-primary)"/></svg>',
      },
      {
        id: 'manage',
        iconName: 'user-cog',
        label: 'Manage',
        action: 'manage-action',
        isSelected: false,
        tooltip: 'Manage',
        iconType: 'lucide',
        retainActiveState: false,
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none"> <path d="M15.5384 25.1283C15.0684 25.1283 14.6688 24.9638 14.3397 24.6347C14.0106 24.3056 13.846 23.906 13.846 23.436V15.5383C13.846 15.0683 14.0106 14.6688 14.3397 14.3397C14.6688 14.0106 15.0684 13.846 15.5384 13.846H23.436C23.906 13.846 24.3056 14.0106 24.6347 14.3397C24.9638 14.6688 25.1284 15.0683 25.1284 15.5383V23.436C25.1284 23.906 24.9638 24.3056 24.6347 24.6347C24.3056 24.9638 23.906 25.1283 23.436 25.1283H15.5384ZM10.6667 19.182V24H10.536C10.2265 24 9.97049 23.8983 9.76805 23.695C9.56538 23.4914 9.43672 23.2376 9.38205 22.9333L9.03071 20.1437C8.60516 20.0154 8.14494 19.8137 7.65005 19.5383C7.15516 19.2632 6.73427 18.9684 6.38738 18.654L3.83338 19.7513C3.55138 19.876 3.26505 19.8897 2.97438 19.7923C2.68371 19.6948 2.45983 19.5101 2.30272 19.2383L0.864049 16.7617C0.706937 16.4899 0.660827 16.2061 0.725715 15.9103C0.790604 15.6146 0.945271 15.3718 1.18972 15.182L3.41805 13.5153C3.37872 13.2744 3.34627 13.0262 3.32071 12.7707C3.29494 12.5151 3.28205 12.2668 3.28205 12.0257C3.28205 11.8017 3.29494 11.5662 3.32071 11.3193C3.34627 11.0722 3.37872 10.794 3.41805 10.4847L1.18972 8.818C0.945271 8.62822 0.794826 8.38122 0.738382 8.077C0.68216 7.77278 0.732604 7.48467 0.889715 7.21267L2.30272 4.81267C2.45983 4.558 2.68371 4.37767 2.97438 4.27167C3.26505 4.16567 3.55138 4.17511 3.83338 4.3L6.36172 5.37167C6.75994 5.04011 7.19072 4.741 7.65405 4.47433C8.11716 4.20767 8.56749 4.00167 9.00505 3.85633L9.38205 1.06667C9.43672 0.762444 9.57816 0.508556 9.80638 0.305001C10.0346 0.101667 10.3034 0 10.6127 0H13.3874C13.6967 0 13.9655 0.101667 14.1937 0.305001C14.4219 0.508556 14.5634 0.762444 14.618 1.06667L14.9694 3.882C15.4805 4.06156 15.9322 4.26756 16.3244 4.5C16.7166 4.73244 17.1205 5.023 17.536 5.37167L20.1924 4.3C20.4744 4.17511 20.7607 4.16567 21.0514 4.27167C21.342 4.37767 21.5659 4.558 21.723 4.81267L23.136 7.23833C23.2932 7.51011 23.3393 7.79522 23.2744 8.09367C23.2095 8.39189 23.0548 8.63333 22.8104 8.818L21.1897 10.0333C21.0326 10.1529 20.8715 10.2119 20.7064 10.2103C20.5415 10.2086 20.388 10.1316 20.246 9.97933C20.1043 9.82733 20.0385 9.659 20.0487 9.47433C20.0589 9.28967 20.1402 9.135 20.2924 9.01033L21.9257 7.8L20.6 5.53333L17.1974 6.959C16.794 6.51634 16.2637 6.10133 15.6064 5.714C14.9493 5.32689 14.3275 5.07011 13.741 4.94367L13.3334 1.33333H10.6744L10.259 4.918C9.58727 5.06156 8.96972 5.29278 8.40638 5.61167C7.84327 5.93033 7.28305 6.36833 6.72571 6.92567L3.40005 5.53333L2.07438 7.8L4.96672 9.959C4.8556 10.2581 4.77783 10.5829 4.73338 10.9333C4.68894 11.2838 4.66672 11.6479 4.66672 12.0257C4.66672 12.3641 4.68894 12.7 4.73338 13.0333C4.77783 13.3667 4.84705 13.6914 4.94105 14.0077L2.07438 16.2L3.40005 18.4667L6.70005 17.0667C7.23338 17.6051 7.83894 18.0491 8.51672 18.3987C9.19449 18.7482 9.91116 19.0093 10.6667 19.182ZM11.964 8.66667C11.0343 8.66667 10.2463 8.98845 9.60005 9.632C8.95383 10.2756 8.63072 11.0649 8.63072 12C8.63072 12.4153 8.70172 12.8111 8.84371 13.1873C8.98549 13.5633 9.19827 13.9086 9.48205 14.223C9.62405 14.3854 9.78694 14.4769 9.97071 14.4973C10.1543 14.518 10.3247 14.4573 10.482 14.3153C10.6394 14.1787 10.7202 14.021 10.7244 13.8423C10.7286 13.6637 10.6598 13.5093 10.518 13.3793C10.3334 13.2016 10.1949 12.9927 10.1027 12.7527C10.0103 12.5124 9.96405 12.2616 9.96405 12C9.96405 11.4444 10.1585 10.9722 10.5474 10.5833C10.9363 10.1944 11.4085 10 11.964 10C12.2205 10 12.4714 10.0517 12.7167 10.155C12.962 10.2586 13.1736 10.4027 13.3514 10.5873C13.4812 10.7189 13.6316 10.7782 13.8027 10.7653C13.9736 10.7524 14.1274 10.6674 14.264 10.5103C14.4009 10.353 14.4604 10.1838 14.4424 10.0027C14.4244 9.82134 14.3367 9.65978 14.1794 9.518C13.9025 9.25133 13.5675 9.04278 13.1744 8.89233C12.7813 8.74189 12.3778 8.66667 11.964 8.66667ZM19.4874 20.6153C18.9318 20.6153 18.4002 20.7004 17.8924 20.8707C17.3846 21.0407 16.9145 21.2898 16.482 21.618C16.2274 21.8129 16.032 22.0103 15.896 22.2103C15.7603 22.4103 15.6924 22.6129 15.6924 22.818C15.6924 22.9393 15.7394 23.047 15.8334 23.141C15.9274 23.235 16.035 23.282 16.1564 23.282H22.9514C23.0454 23.282 23.124 23.2393 23.1874 23.154C23.2505 23.0684 23.282 22.9564 23.282 22.818C23.282 22.6129 23.2142 22.4103 23.0784 22.2103C22.9424 22.0103 22.747 21.8129 22.4924 21.618C22.0599 21.2898 21.5898 21.0407 21.082 20.8707C20.5745 20.7004 20.0429 20.6153 19.4874 20.6153ZM19.4874 19.4873C19.9574 19.4873 20.3569 19.3228 20.686 18.9937C21.0149 18.6646 21.1794 18.265 21.1794 17.795C21.1794 17.3248 21.0149 16.9251 20.686 16.596C20.3569 16.2671 19.9574 16.1027 19.4874 16.1027C19.0172 16.1027 18.6175 16.2671 18.2884 16.596C17.9595 16.9251 17.795 17.3248 17.795 17.795C17.795 18.265 17.9595 18.6646 18.2884 18.9937C18.6175 19.3228 19.0172 19.4873 19.4874 19.4873Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none"><path d="M15.5384 25.1286C15.0684 25.1286 14.6688 24.9641 14.3397 24.635C14.0106 24.3058 13.846 23.9063 13.846 23.4363V15.5386C13.846 15.0686 14.0106 14.6691 14.3397 14.34C14.6688 14.0108 15.0684 13.8463 15.5384 13.8463H23.436C23.906 13.8463 24.3056 14.0108 24.6347 14.34C24.9638 14.6691 25.1284 15.0686 25.1284 15.5386V23.4363C25.1284 23.9063 24.9638 24.3058 24.6347 24.635C24.3056 24.9641 23.906 25.1286 23.436 25.1286H15.5384ZM10.0487 24.0003C9.88627 24.0003 9.75505 23.9572 9.65505 23.871C9.55505 23.7845 9.49138 23.6652 9.46405 23.513L9.03071 20.1593C8.60516 20.0311 8.14494 19.8294 7.65005 19.5543C7.15516 19.279 6.73427 18.9841 6.38738 18.6696L3.83338 19.767C3.55138 19.8918 3.26505 19.9055 2.97438 19.808C2.68371 19.7106 2.45983 19.5261 2.30272 19.2543L0.864049 16.7773C0.706937 16.5055 0.660827 16.2217 0.725715 15.926C0.790604 15.6302 0.945271 15.3874 1.18972 15.1976L3.41805 13.531C3.37872 13.2901 3.34627 13.0418 3.32071 12.7863C3.29494 12.5307 3.28205 12.2824 3.28205 12.0413C3.28205 11.8173 3.29494 11.5818 3.32071 11.335C3.34627 11.0878 3.37872 10.8096 3.41805 10.5003L1.18972 8.83362C0.945271 8.64385 0.794826 8.39685 0.738382 8.09263C0.68216 7.7884 0.732604 7.5004 0.889715 7.22862L2.30272 4.82862C2.45983 4.57373 2.68371 4.3934 2.97438 4.28762C3.26505 4.18162 3.55138 4.19096 3.83338 4.31563L6.36172 5.38762C6.75994 5.05585 7.19072 4.75662 7.65405 4.48996C8.11716 4.22329 8.56749 4.01729 9.00505 3.87196L9.38205 1.08229C9.43672 0.778069 9.57816 0.524291 9.80638 0.320957C10.0346 0.117402 10.3034 0.015625 10.6127 0.015625H13.3874C13.6967 0.015625 13.9655 0.117402 14.1937 0.320957C14.4219 0.524291 14.5634 0.778069 14.618 1.08229L14.9694 3.89762C15.4805 4.07718 15.9322 4.28318 16.3244 4.51562C16.7166 4.74807 17.1205 5.03874 17.536 5.38762L20.1924 4.31563C20.4744 4.19096 20.7607 4.18162 21.0514 4.28762C21.342 4.3934 21.5659 4.57373 21.723 4.82862L23.136 7.25429C23.2932 7.52607 23.3393 7.80974 23.2744 8.10529C23.2095 8.40107 23.0548 8.64385 22.8104 8.83362L20.618 10.4593C20.524 10.5311 20.4232 10.5836 20.3154 10.617C20.2078 10.6503 20.0959 10.667 19.9797 10.667H15.3C15.2009 10.667 15.1129 10.6396 15.036 10.585C14.9592 10.5303 14.8985 10.4645 14.854 10.3876C14.5667 9.88496 14.1679 9.47251 13.6577 9.15029C13.1475 8.82807 12.5829 8.66696 11.964 8.66696C11.0343 8.66696 10.2463 8.99007 9.60005 9.63629C8.95383 10.2823 8.63072 11.0703 8.63072 12.0003C8.63072 12.6532 8.78505 13.2391 9.09371 13.758C9.40216 14.2768 9.82483 14.6713 10.3617 14.9413C10.4557 14.9857 10.53 15.0507 10.5847 15.1363C10.6394 15.2216 10.6667 15.3138 10.6667 15.413V23.431C10.6667 23.5883 10.6056 23.7225 10.4834 23.8336C10.3612 23.9447 10.2163 24.0003 10.0487 24.0003ZM19.4874 20.6156C18.9318 20.6156 18.4002 20.7007 17.8924 20.871C17.3846 21.041 16.9145 21.2901 16.482 21.6183C16.2274 21.8132 16.032 22.0106 15.896 22.2106C15.7603 22.4106 15.6924 22.6132 15.6924 22.8183C15.6924 22.9396 15.7394 23.0473 15.8334 23.1413C15.9274 23.2353 16.035 23.2823 16.1564 23.2823H22.9514C23.0454 23.2823 23.124 23.2396 23.1874 23.1543C23.2505 23.0687 23.282 22.9567 23.282 22.8183C23.282 22.6132 23.2142 22.4106 23.0784 22.2106C22.9424 22.0106 22.747 21.8132 22.4924 21.6183C22.0599 21.2901 21.5898 21.041 21.082 20.871C20.5745 20.7007 20.0429 20.6156 19.4874 20.6156ZM19.4874 19.4876C19.9574 19.4876 20.3569 19.3231 20.686 18.994C21.0149 18.6648 21.1794 18.2653 21.1794 17.7953C21.1794 17.3251 21.0149 16.9254 20.686 16.5963C20.3569 16.2674 19.9574 16.103 19.4874 16.103C19.0172 16.103 18.6175 16.2674 18.2884 16.5963C17.9595 16.9254 17.795 17.3251 17.795 17.7953C17.795 18.2653 17.9595 18.6648 18.2884 18.994C18.6175 19.3231 19.0172 19.4876 19.4874 19.4876Z" fill="var(--color-brand-primary)"/></svg>',
      },
      {
        id: 'analytics',
        iconName: 'chart-no-axes-combined',
        label: 'Analytics',
        action: 'analytics-action',
        isSelected: false,
        tooltip: 'Analytics',
        isDisabled: true,
        iconType: 'lucide',
        retainActiveState: false,
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none"><path d="M2.48798 21.6668C1.87421 21.6668 1.36176 21.4613 0.950651 21.0502C0.53954 20.6391 0.333984 20.1266 0.333984 19.5128V2.4875C0.333984 1.87372 0.53954 1.36127 0.950651 0.950163C1.36176 0.539052 1.87421 0.333496 2.48798 0.333496H19.5133C20.1271 0.333496 20.6395 0.539052 21.0506 0.950163C21.4618 1.36127 21.6673 1.87372 21.6673 2.4875V19.5128C21.6673 20.1266 21.4618 20.6391 21.0506 21.0502C20.6395 21.4613 20.1271 21.6668 19.5133 21.6668H2.48798ZM2.48798 20.3335H19.5133C19.7187 20.3335 19.9068 20.2481 20.0777 20.0772C20.2485 19.9063 20.334 19.7182 20.334 19.5128V2.4875C20.334 2.28216 20.2485 2.09405 20.0777 1.92316C19.9068 1.75227 19.7187 1.66683 19.5133 1.66683H2.48798C2.28265 1.66683 2.09454 1.75227 1.92365 1.92316C1.75276 2.09405 1.66732 2.28216 1.66732 2.4875V19.5128C1.66732 19.7182 1.75276 19.9063 1.92365 20.0772C2.09454 20.2481 2.28265 20.3335 2.48798 20.3335ZM5.97498 9.00016C5.78521 9.00016 5.62665 9.06383 5.49932 9.19116C5.37198 9.31849 5.30832 9.47705 5.30832 9.66683V16.3335C5.30832 16.5233 5.37198 16.6818 5.49932 16.8092C5.62665 16.9365 5.78521 17.0002 5.97498 17.0002C6.16476 17.0002 6.32332 16.9365 6.45065 16.8092C6.57798 16.6818 6.64165 16.5233 6.64165 16.3335V9.66683C6.64165 9.47705 6.57798 9.31849 6.45065 9.19116C6.32332 9.06383 6.16476 9.00016 5.97498 9.00016ZM11.0007 5.00016C10.8109 5.00016 10.6523 5.06383 10.525 5.19116C10.3977 5.3185 10.334 5.47705 10.334 5.66683V16.3335C10.334 16.5233 10.3977 16.6818 10.525 16.8092C10.6523 16.9365 10.8109 17.0002 11.0007 17.0002C11.1904 17.0002 11.349 16.9365 11.4763 16.8092C11.6037 16.6818 11.6673 16.5233 11.6673 16.3335V5.66683C11.6673 5.47705 11.6037 5.3185 11.4763 5.19116C11.349 5.06383 11.1904 5.00016 11.0007 5.00016ZM16.0263 13.0002C15.8365 13.0002 15.678 13.0638 15.5507 13.1912C15.4233 13.3185 15.3597 13.4771 15.3597 13.6668V16.3335C15.3597 16.5233 15.4233 16.6818 15.5507 16.8092C15.678 16.9365 15.8365 17.0002 16.0263 17.0002C16.2161 17.0002 16.3746 16.9365 16.502 16.8092C16.6293 16.6818 16.693 16.5233 16.693 16.3335V13.6668C16.693 13.4771 16.6293 13.3185 16.502 13.1912C16.3746 13.0638 16.2161 13.0002 16.0263 13.0002Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none"><path d="M2.48798 21.6668C1.87421 21.6668 1.36176 21.4613 0.950651 21.0502C0.53954 20.6391 0.333984 20.1266 0.333984 19.5128V2.4875C0.333984 1.87372 0.53954 1.36127 0.950651 0.950163C1.36176 0.539052 1.87421 0.333496 2.48798 0.333496H19.5133C20.1271 0.333496 20.6395 0.539052 21.0507 0.950163C21.4618 1.36127 21.6673 1.87372 21.6673 2.4875V19.5128C21.6673 20.1266 21.4618 20.6391 21.0507 21.0502C20.6395 21.4613 20.1271 21.6668 19.5133 21.6668H2.48798ZM5.97398 9.00016C5.78487 9.00016 5.62665 9.06405 5.49932 9.19183C5.37198 9.31961 5.30832 9.47794 5.30832 9.66683V16.3335C5.30832 16.5224 5.37232 16.6807 5.50032 16.8085C5.62832 16.9363 5.78687 17.0002 5.97598 17.0002C6.1651 17.0002 6.32332 16.9363 6.45065 16.8085C6.57798 16.6807 6.64165 16.5224 6.64165 16.3335V9.66683C6.64165 9.47794 6.57765 9.31961 6.44965 9.19183C6.32187 9.06405 6.16332 9.00016 5.97398 9.00016ZM10.9997 5.00016C10.8105 5.00016 10.6523 5.06405 10.525 5.19183C10.3977 5.31961 10.334 5.47794 10.334 5.66683V16.3335C10.334 16.5224 10.398 16.6807 10.526 16.8085C10.654 16.9363 10.8125 17.0002 11.0017 17.0002C11.1908 17.0002 11.349 16.9363 11.4763 16.8085C11.6036 16.6807 11.6673 16.5224 11.6673 16.3335V5.66683C11.6673 5.47794 11.6033 5.31961 11.4753 5.19183C11.3473 5.06405 11.1888 5.00016 10.9997 5.00016ZM16.0253 13.0002C15.8362 13.0002 15.678 13.0641 15.5507 13.1918C15.4233 13.3196 15.3597 13.4779 15.3597 13.6668V16.3335C15.3597 16.5224 15.4237 16.6807 15.5517 16.8085C15.6794 16.9363 15.838 17.0002 16.0273 17.0002C16.2164 17.0002 16.3746 16.9363 16.502 16.8085C16.6293 16.6807 16.693 16.5224 16.693 16.3335V13.6668C16.693 13.4779 16.629 13.3196 16.501 13.1918C16.373 13.0641 16.2144 13.0002 16.0253 13.0002Z" fill="var(--color-brand-primary)"/></svg>',
      },
    ],
    //    secondaryAvatar: {
    //   imageUrl: 'svgs/sidenav/ascendion.svg',
    //   altText: 'Secondary Avatar',
    //   action: 'secondary-avatar-action',
    //   tooltip: 'Secondary Avatar - Click for context action',
    //   isDisabled: true, 
    //   selectItems: [
    //     {
    //       id: 'ascendion',
    //       label: 'Ascendion',
    //       avatar: 'svgs/sidenav/ascendion.svg',
    //       isSelected: true,
    //       action: 'select-ascendion'
    //     },
    //     {
    //       id: 'axos',
    //       label: 'Axos',
    //       avatar: 'svgs/sidenav/axos.svg',
    //       isSelected: false,
    //       action: 'select-axos'
    //     },
    //     {
    //       id: 'hp',
    //       label: 'HP',
    //       avatar: 'svgs/sidenav/hp.svg',
    //       isSelected: false,
    //       action: 'select-hp'
    //     }
    //   ]
    // },
    subMenuItems: [
      {
        id: 'closePanel',
        iconName: 'panel-left-close',
        label: '',
        action: 'close-panel-action',
        isSelected: false,
        iconType: 'lucide',
        tooltip: '',
        retainActiveState: false, // This item will NOT retain active state (action-based)
      },
    ],
    profile: {
      altText: 'User Profile',
      action: 'profile-action',
      tooltip: '',
      initials: '',
      initialsBackground: '',
      initialsColor: '',
      profileMenu: [
        {
          id: 'settings',
          iconName: 'Settings',
          label: '',
          action: 'open-settings',
          iconType: 'lucide',
          tooltip: '',
        },
        {
          id: 'theme',
          iconName: 'Sun',
          label: '',
          action: 'toggle-theme',
          iconType: 'lucide',
          tooltip: '',
          isToggle: true,
          isDisabled: true,
          subMenuItems: [
            {
              id: 'light',
              iconName: 'Sun',
              label: '',
              action: 'set-theme-light',
              iconType: 'lucide',
              tooltip: '',
            },
            {
              id: 'dark',
              iconName: 'Moon',
              label: '',
              action: 'set-theme-dark',
              iconType: 'lucide',
              tooltip: '',
            },
            {
              id: 'auto',
              iconName: 'Monitor',
              label: '',
              action: 'set-theme-auto',
              iconType: 'lucide',
              tooltip: '',
            },
          ],
          selectedSubItem: {
            id: 'light',
            iconName: 'Sun',
            label: '',
            action: 'set-theme-light',
            iconType: 'lucide',
            tooltip: '',
          },
        },
        {
          id: 'language',
          iconName: 'Languages',
          label: '',
          action: 'change-language',
          iconType: 'lucide',
          isDisabled: true,
          tooltip: '',
          isToggle: true,
        },
        {
          id: 'logout',
          iconName: 'LogOut',
          label: '',
          action: 'logout',
          iconType: 'lucide',
          tooltip: '',
          isDestructive: true,
        },
      ],
    },
  };

  breadcrumbList: BreadcrumbItem[] = [];
  private breadcrumbSubscription: Subscription;

  constructor(
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private centralizedRedirectService: CentralizedRedirectService,
    private themeInitService: ThemeInitService,
    // private themeService: ThemeService,
    private breadcrumbService: BreadcrumbService,
    private router: Router,
    private elementRef: ElementRef
  ) {
    // Subscribe to breadcrumb updates from the service
    this.breadcrumbSubscription = this.breadcrumbService.breadcrumbs$.subscribe(
      breadcrumbs => {
        this.breadcrumbList = [...breadcrumbs]; // Create new array reference for OnPush change detection

        // Update sidebar selection when breadcrumbs change
        const currentRoute = this.router.url;
        const menuItemId = this.getMenuItemIdFromRoute(currentRoute);
        if (menuItemId) {
          this.updateSidebarSelection(menuItemId);
        }
      }
    );
  }

  toggleChat(): boolean {
    this.toggleRevelio = !this.toggleRevelio;
    return this.toggleRevelio;
  }
  ngOnInit(): void {
    // Initialize theme system early
    this.themeInitService.initialize();

    // Initialize breadcrumb service with routes
    this.breadcrumbService.initializeWithRoutes(routes);

    // Sync sidebar selection with route changes
    this.syncSidebarWithRoute();

    // Initialize sidebar selection based on current route
    this.initializeSidebarSelection();

    // Set user initials and colors in profile
    this.sidebarConfig.profile!.initials = this.getUserInitials();
    const avatarColors = this.getUserAvatarColors();
    this.sidebarConfig.profile!.initialsBackground = avatarColors.backgroundColor;
    this.sidebarConfig.profile!.initialsColor = avatarColors.textColor;

    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: environment.consoleRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'console',
    };

    this.authService.setAuthConfig(authConfig);
    this.authService.handleAuthCodeAndToken();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    if (this.breadcrumbSubscription) {
      this.breadcrumbSubscription.unsubscribe();
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  onMenuItemClick(item: any): void {
    // Navigate to the matching route based on menu item ID
    if (item && item.id) {
      const routePath = this.getRoutePathFromMenuItemId(item.id);
      if (routePath) {
        // Navigate to the route
        this.router.navigate([routePath]);

        // Update sidebar selection to match navigation
        this.updateSidebarSelection(item.id);
      }
    }
  }

  /**
   * Update sidebar selection state
   */
  private updateSidebarSelection(selectedItemId: string): void {
    // First reset all items to false
    this.sidebarConfig.menuItems.forEach(menuItem => {
      menuItem.isSelected = false;
    });

    // Then set the selected item to true
    const selectedItem = this.sidebarConfig.menuItems.find(
      menuItem => menuItem.id === selectedItemId
    );
    if (selectedItem) {
      selectedItem.isSelected = true;
    }
  }

  /**
   * Initialize sidebar selection based on current route
   */
  private initializeSidebarSelection(): void {
    const currentRoute = this.router.url;
    const menuItemId = this.getMenuItemIdFromRoute(currentRoute);

    if (menuItemId) {
      this.updateSidebarSelection(menuItemId);
    }
  }

  /**
   * Sync sidebar selection with route changes
   */
  private syncSidebarWithRoute(): void {
    this.routeSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        const currentRoute = event.urlAfterRedirects || event.url;
        const menuItemId = this.getMenuItemIdFromRoute(currentRoute);

        if (menuItemId) {
          this.updateSidebarSelection(menuItemId);
        }
      });
  }

  /**
   * Get menu item ID from current route
   */
  private getMenuItemIdFromRoute(route: string): string | null {
    // Remove leading slash and 'console' prefix if present
    const cleanRoute = route.replace(/^\/?(console\/)?/, '');

    // Handle root route (empty string or just '/')
    if (!cleanRoute || cleanRoute === '/') {
      return 'dashboard';
    }

    // Extract the first segment of the route
    const firstSegment = cleanRoute.split('/')[0];

    // Map route segments to menu item IDs
    const routeToMenuMapping: { [key: string]: string } = {
      dashboard: '/',
      approvals: 'approvals',
      marketplace: 'marketplace',
      manage: 'manage',
      // analytics: 'analytics',
      agent: 'build/agent',
      pipeline: 'build/pipelines',
      model: 'build/model',
      tool: 'build/tools',
      guardrail: 'build/guardrails',
      knowledge: 'build/knowledge',
    };

    return routeToMenuMapping[firstSegment] || null;
  }

  /**
   * Get route path from menu item ID
   */
  private getRoutePathFromMenuItemId(menuItemId: string): string | null {
    const routeMapping: { [key: string]: string } = {
      dashboard: '/',
      approvals: 'approvals',
      marketplace: 'marketplace',
      manage: 'manage',
      // analytics: 'analytics',
      agent: 'build/agent',
      pipeline: 'build/pipelines',
      model: 'build/model',
      tool: 'build/tools',
      guardrail: 'build/guardrails',
      knowledge: 'build/knowledge',
    };

    return routeMapping[menuItemId] || null;
  }

  onSubMenuItemClick(item: any): void {
    if (item.id === 'closePanel') {
      this.leftPanelOpen = false;
    } else if (item.id === 'themetoggle') {
      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', this.currentTheme);
    }
  }

  onProfileClick(action: string): void {
    console.log('Profile clicked:', action);
  }

  onStudioCardClick(event: { menuItemId: string; card: any }): void {
    if (event.menuItemId === 'build') {
      if (event.card && event.card.id) {
        const routePath = this.getRoutePathFromMenuItemId(event.card.id);
        if (routePath) {
          // Navigate to the route
          this.router.navigate([routePath]);

          // Update sidebar selection to match navigation
          this.updateSidebarSelection(event.card.id);
        }
      }
    }
  }

  // Handle clicking outside of Revelio component to close it
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.toggleRevelio) {
      const target = event.target as HTMLElement;

      // Get the revelio component element
      const revelioElement =
        this.elementRef.nativeElement.querySelector('app-revelio');

      // Get the chat icon that triggers the revelio
      const chatIcon = this.elementRef.nativeElement.querySelector(
        '.header-right .revelio-icon'
      );

      // Check if click is outside both the revelio component and the chat icon
      if (
        revelioElement &&
        chatIcon &&
        !revelioElement.contains(target) &&
        !chatIcon.contains(target)
      ) {
        this.toggleRevelio = false;
      }
    }
  }

  onProfileMenuItemClick(item: any): void {
    console.log("item: ", item);
    if (!item) {
      return;
    }

    // Handle logout action
    if (item.action === 'logout' || item.id === 'logout') {
      // SSO logout only - clears state and navigates to provider logout URL
      this.authService.logout().subscribe({
        error: () => {
          // Fallback in case of error
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
      return;
    }

    if (item.action === 'open-settings') {
      this.router.navigate(['settings']);
    }
  }

  onProfileSubMenuItemClick(item: any): void {
    // Handle theme changes
    console.log("item: ", item);
    if (item.parentItem?.action === 'toggle-theme' && item.subItem) {
      const themeAction = item.subItem.action;

      switch (themeAction) {
        case 'set-theme-light':
          // this.themeService.setUserPreference('light');
          break;

        case 'set-theme-dark':
          // this.themeService.setUserPreference('dark');
          break;

        case 'set-theme-auto':
          // this.themeService.setUserPreference('system');
          break;

        default:
          console.warn('Unknown theme action:', themeAction);
      }

      // Update current theme property for UI updates
      // this.currentTheme = this.themeService.getCurrentTheme();
    }
  }

  //   onSecondaryAvatarSelectionChange(selectedItem: SecondaryAvatarSelectItem): void {
  //   console.log('Secondary avatar selection changed:', selectedItem);

  //   // Handle specific secondary avatar selection actions
  //   switch (selectedItem.id) {
  //     case 'axos':
  //       console.log('Switched to Axos context');
  //       // You can add specific logic for Axos context here
  //       break;
  //     case 'hp':
  //       console.log('Switched to HP context');
  //       // You can add specific logic for HP context here
  //       break;
  //     case 'ascendion':
  //       console.log('Switched to Ascendion context');
  //       // You can add specific logic for Ascendion context here
  //       break;
  //     case 'microsoft':
  //       console.log('Switched to Microsoft context');
  //       // You can add specific logic for Microsoft context here
  //       break;
  //     default:
  //       console.log('Unknown context selected:', selectedItem.id);
  //   }
  // }

  /**
   * Get user initials from the logged-in user's name
   */
  getUserInitials(): string {
    const fullName = this.tokenStorage.getDaName();
    if (!fullName || fullName === 'Guest') {
      return ''; // Return empty string for guest users - no avatar will be shown
    }

    const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);

    if (nameParts.length >= 2) {
      // First letter of first name and first letter of last name
      return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
    } else if (nameParts.length === 1) {
      // Just first letter if only one name
      return nameParts[0][0].toUpperCase();
    } else {
      return ''; // Return empty string - no avatar will be shown
    }
  }

  /**
   * Generate dynamic background and text colors based on username
   */
  getUserAvatarColors(): { backgroundColor: string; textColor: string } {
    const colors = [
      '#ED4B82',
      '#9661F1',
      '#5082EF',
      '#FB8DAE',
      '#FEE765',
      '#35CADD',
      '#69CAA6',
      '#898E99',
      '#E35151',
    ];

    const fullName = this.tokenStorage.getDaName();
    if (!fullName || fullName === 'Guest') {
      return { backgroundColor: colors[0], textColor: '#ffffff' };
    }

    // Use username length to determine color index for consistency
    const colorIndex = fullName.length % colors.length;

    return {
      backgroundColor: colors[colorIndex],
      textColor: '#ffffff'
    };
  }
}
