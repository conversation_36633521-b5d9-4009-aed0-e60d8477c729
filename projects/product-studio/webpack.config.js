const { ModuleFederationPlugin } = require('webpack').container;

module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'product-studio',
    globalObject: 'self',
    module: false,
  },
  optimization: {
    runtimeChunk: false,
  },
  experiments: {
    outputModule: false,
  },
  module: {
    rules: [
      {
        test: /\.ttf$/,
        use: ['file-loader'],
      },
    ],
  },
  resolve: {
    fallback: {
      path: false,
      fs: false,
    },
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'product-studio',
      library: { type: 'var', name: 'product-studio' },
      filename: 'remoteEntry.js',
      exposes: {},
      shared: {
        '@angular/core': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/common': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/router': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/animations': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
      },
    }),
  ],
};
