import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  HostListener,
  ElementRef,
} from '@angular/core';
import {
  TokenStorageService,
  AuthConfig,
  AuthService,
  CentralizedRedirectService,
  ThemeInitService,
  ThemeService,
  RevelioComponent,
  FooterComponent,
} from '@shared';
import { environment } from '../environments/environment';
import {
  AavaLayoutComponent,
  AavaRailNavigationSidebarComponent,
  RailNavigationConfig,
  AavaBreadcrumbsComponent,
} from '@aava/play-core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import {
  BreadcrumbService,
  BreadcrumbItem,
} from './services/breadcrumb.service';
import { Subscription, filter } from 'rxjs';
import { routes } from './app.routes';
@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    AavaBreadcrumbsComponent,
    AavaRailNavigationSidebarComponent,
    AavaLayoutComponent,
    RevelioComponent,
    FooterComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  leftPanelOpen = false;
  currentTheme = 'light';
  private routeSubscription?: Subscription;

  toggleRevelio: boolean = false;

  sidebarConfig: RailNavigationConfig = {
    headerIcon: {
      iconName: 'svgs/sidenav/ascendionLogo_sidenav.svg',
      label: '',
      action: 'ascendion-action',
      iconType: 'url',
      tooltip: '',
    },
    menuItems: [
      {
        id: 'dashboard',
        iconName: '',
        label: 'Dashboard',
        action: 'dashboard-action',
        isSelected: true,
        tooltip: 'Dashboard',
        iconType: 'lucide',
        retainActiveState: true,
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none"><path d="M1.66732 9.66683C1.30998 9.66683 0.998429 9.53394 0.732651 9.26816C0.466873 9.00238 0.333984 8.69083 0.333984 8.3335V1.66683C0.333984 1.3095 0.466873 0.997941 0.732651 0.732163C0.998429 0.466386 1.30998 0.333496 1.66732 0.333496H8.33398C8.69132 0.333496 9.00287 0.466386 9.26865 0.732163C9.53443 0.997941 9.66732 1.3095 9.66732 1.66683V8.3335C9.66732 8.69083 9.53443 9.00238 9.26865 9.26816C9.00287 9.53394 8.69132 9.66683 8.33398 9.66683H1.66732ZM1.66732 21.6668C1.30998 21.6668 0.998429 21.5339 0.732651 21.2682C0.466873 21.0024 0.333984 20.6908 0.333984 20.3335V13.6668C0.333984 13.3095 0.466873 12.9979 0.732651 12.7322C0.998429 12.4664 1.30998 12.3335 1.66732 12.3335H8.33398C8.69132 12.3335 9.00287 12.4664 9.26865 12.7322C9.53443 12.9979 9.66732 13.3095 9.66732 13.6668V20.3335C9.66732 20.6908 9.53443 21.0024 9.26865 21.2682C9.00287 21.5339 8.69132 21.6668 8.33398 21.6668H1.66732ZM13.6673 9.66683C13.31 9.66683 12.9984 9.53394 12.7327 9.26816C12.4669 9.00238 12.334 8.69083 12.334 8.3335V1.66683C12.334 1.3095 12.4669 0.997941 12.7327 0.732163C12.9984 0.466386 13.31 0.333496 13.6673 0.333496H20.334C20.6913 0.333496 21.0029 0.466386 21.2686 0.732163C21.5344 0.997941 21.6673 1.3095 21.6673 1.66683V8.3335C21.6673 8.69083 21.5344 9.00238 21.2686 9.26816C21.0029 9.53394 20.6913 9.66683 20.334 9.66683H13.6673ZM13.6673 21.6668C13.31 21.6668 12.9984 21.5339 12.7327 21.2682C12.4669 21.0024 12.334 20.6908 12.334 20.3335V13.6668C12.334 13.3095 12.4669 12.9979 12.7327 12.7322C12.9984 12.4664 13.31 12.3335 13.6673 12.3335H20.334C20.6913 12.3335 21.0029 12.4664 21.2686 12.7322C21.5344 12.9979 21.6673 13.3095 21.6673 13.6668V20.3335C21.6673 20.6908 21.5344 21.0024 21.2686 21.2682C21.0029 21.5339 20.6913 21.6668 20.334 21.6668H13.6673ZM1.66732 8.3335H8.33398V1.66683H1.66732V8.3335ZM13.6673 8.3335H20.334V1.66683H13.6673V8.3335ZM13.6673 20.3335H20.334V13.6668H13.6673V20.3335ZM1.66732 20.3335H8.33398V13.6668H1.66732V20.3335Z" fill="#4C515B"/> </svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none"> <mask id="mask0_5731_61875" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="32"><rect width="32" height="32" fill="#D9D9D9"/></mask>  <g mask="url(#mask0_5731_61875)"><path d="M6.66732 14.6668C6.30998 14.6668 5.99843 14.5339 5.73265 14.2682C5.46687 14.0024 5.33398 13.6908 5.33398 13.3335V6.66683C5.33398 6.3095 5.46687 5.99794 5.73265 5.73216C5.99843 5.46639 6.30998 5.3335 6.66732 5.3335H13.334C13.6913 5.3335 14.0029 5.46639 14.2687 5.73216C14.5344 5.99794 14.6673 6.3095 14.6673 6.66683V13.3335C14.6673 13.6908 14.5344 14.0024 14.2687 14.2682C14.0029 14.5339 13.6913 14.6668 13.334 14.6668H6.66732ZM6.66732 26.6668C6.30998 26.6668 5.99843 26.5339 5.73265 26.2682C5.46687 26.0024 5.33398 25.6908 5.33398 25.3335V18.6668C5.33398 18.3095 5.46687 17.9979 5.73265 17.7322C5.99843 17.4664 6.30998 17.3335 6.66732 17.3335H13.334C13.6913 17.3335 14.0029 17.4664 14.2687 17.7322C14.5344 17.9979 14.6673 18.3095 14.6673 18.6668V25.3335C14.6673 25.6908 14.5344 26.0024 14.2687 26.2682C14.0029 26.5339 13.6913 26.6668 13.334 26.6668H6.66732ZM18.6673 14.6668C18.31 14.6668 17.9984 14.5339 17.7327 14.2682C17.4669 14.0024 17.334 13.6908 17.334 13.3335V6.66683C17.334 6.3095 17.4669 5.99794 17.7327 5.73216C17.9984 5.46639 18.31 5.3335 18.6673 5.3335H25.334C25.6913 5.3335 26.0029 5.46639 26.2686 5.73216C26.5344 5.99794 26.6673 6.3095 26.6673 6.66683V13.3335C26.6673 13.6908 26.5344 14.0024 26.2686 14.2682C26.0029 14.5339 25.6913 14.6668 25.334 14.6668H18.6673ZM18.6673 26.6668C18.31 26.6668 17.9984 26.5339 17.7327 26.2682C17.4669 26.0024 17.334 25.6908 17.334 25.3335V18.6668C17.334 18.3095 17.4669 17.9979 17.7327 17.7322C17.9984 17.4664 18.31 17.3335 18.6673 17.3335H25.334C25.6913 17.3335 26.0029 17.4664 26.2686 17.7322C26.5344 17.9979 26.6673 18.3095 26.6673 18.6668V25.3335C26.6673 25.6908 26.5344 26.0024 26.2686 26.2682C26.0029 26.5339 25.6913 26.6668 25.334 26.6668H18.6673Z" fill="var(--color-brand-primary)"/> </g></svg>',
      },
      {
        id: 'build',
        iconName: '',
        label: 'Build',
        action: 'build-action',
        isSelected: false,
        tooltip: 'Build',
        iconType: 'lucide',
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none"><path d="M6.00389 11.4666C6.41767 10.6393 6.86811 9.84275 7.35522 9.07697C7.84256 8.31119 8.38022 7.56241 8.96822 6.83064L6.92189 6.41264C6.78522 6.37864 6.65278 6.38297 6.52456 6.42564C6.39634 6.4683 6.28089 6.54097 6.17822 6.64364L2.94256 9.8793C2.89122 9.93064 2.87411 9.99053 2.89122 10.059C2.90834 10.1274 2.95111 10.1786 3.01956 10.2126L6.00389 11.4666ZM21.2759 1.50497C19.2246 1.65208 17.4181 2.10341 15.8566 2.85897C14.295 3.61453 12.8262 4.6803 11.4502 6.0563C10.4656 7.04097 9.60656 8.06664 8.87323 9.1333C8.13989 10.2 7.53567 11.2597 7.06056 12.3126L10.8349 16.0793C11.8878 15.6042 12.9519 15 14.0272 14.2666C15.1023 13.5333 16.1322 12.6743 17.1169 11.6896C18.4929 10.3136 19.5587 8.85042 20.3142 7.29997C21.0698 5.74953 21.5211 3.94864 21.6682 1.8973C21.6682 1.84264 21.6643 1.79219 21.6566 1.74597C21.6488 1.69997 21.6201 1.65219 21.5706 1.60264C21.521 1.55308 21.4732 1.52441 21.4272 1.51664C21.381 1.50886 21.3306 1.50497 21.2759 1.50497ZM14.1476 9.01797C13.7562 8.62641 13.5606 8.15675 13.5606 7.60897C13.5606 7.06119 13.7562 6.59153 14.1476 6.19997C14.5391 5.80841 15.0101 5.61264 15.5606 5.61264C16.111 5.61264 16.5819 5.80841 16.9732 6.19997C17.3648 6.59153 17.5606 7.06119 17.5606 7.60897C17.5606 8.15675 17.3648 8.62641 16.9732 9.01797C16.5819 9.4093 16.111 9.60497 15.5606 9.60497C15.0101 9.60497 14.5391 9.4093 14.1476 9.01797ZM11.6809 17.1616L12.9349 20.154C12.9691 20.2222 13.0203 20.2606 13.0886 20.2693C13.157 20.2778 13.2169 20.2563 13.2682 20.205L16.5039 16.995C16.6066 16.8923 16.6792 16.7769 16.7219 16.6486C16.7648 16.5204 16.7691 16.388 16.7349 16.2513L16.3169 14.205C15.5853 14.7932 14.8366 15.3295 14.0706 15.814C13.3048 16.2986 12.5082 16.7479 11.6809 17.1616ZM22.9449 1.3103C22.9347 3.53253 22.5133 5.58253 21.6809 7.4603C20.8484 9.33808 19.6075 11.1017 17.9579 12.7513C17.8723 12.8366 17.7911 12.9135 17.7142 12.982C17.6373 13.0504 17.5561 13.1273 17.4706 13.2126L18.0349 15.9743C18.1067 16.3332 18.0887 16.6811 17.9809 17.018C17.8733 17.3546 17.6896 17.6529 17.4296 17.9126L13.7169 21.6256C13.452 21.8905 13.1289 21.9884 12.7476 21.9193C12.3664 21.85 12.1041 21.6359 11.9606 21.277L10.3629 17.5103L5.63722 12.759L1.87056 11.1616C1.51167 11.0179 1.30056 10.7554 1.23722 10.3743C1.17411 9.99319 1.275 9.67008 1.53989 9.40497L5.25289 5.6923C5.51267 5.43253 5.81222 5.25308 6.15156 5.15397C6.49089 5.05464 6.84 5.04086 7.19889 5.11264L9.96056 5.67697C10.0459 5.59141 10.1184 5.51441 10.1782 5.44597C10.2382 5.37775 10.3109 5.30086 10.3962 5.2153C12.0458 3.56575 13.8094 2.32041 15.6872 1.4793C17.565 0.638414 19.615 0.212859 21.8372 0.202637C21.981 0.207748 22.1203 0.236304 22.2552 0.288304C22.3903 0.340526 22.516 0.424747 22.6322 0.540969C22.7485 0.657191 22.8283 0.778526 22.8719 0.90497C22.9154 1.03164 22.9398 1.16675 22.9449 1.3103ZM2.82189 16.5563C3.34322 16.035 3.977 15.7773 4.72322 15.7833C5.46945 15.7893 6.10322 16.053 6.62456 16.5743C7.14589 17.0956 7.40534 17.7294 7.40289 18.4756C7.40022 19.2219 7.13822 19.8556 6.61689 20.377C5.87489 21.1187 5.00267 21.5854 4.00022 21.777C2.99756 21.9683 1.98511 22.1127 0.962891 22.2103C1.06045 21.171 1.20922 20.1543 1.40922 19.1603C1.60922 18.1663 2.08011 17.2983 2.82189 16.5563ZM3.77322 17.5256C3.37834 17.9205 3.10445 18.3846 2.95156 18.918C2.79845 19.4513 2.686 20.0043 2.61422 20.577C3.18689 20.5052 3.73989 20.3897 4.27322 20.2306C4.80656 20.0717 5.27067 19.7949 5.66556 19.4C5.93222 19.1333 6.06811 18.8153 6.07323 18.446C6.07834 18.0769 5.94756 17.759 5.68089 17.4923C5.41422 17.2256 5.09634 17.0979 4.72722 17.109C4.35789 17.1201 4.03989 17.259 3.77322 17.5256Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none"><path d="M4.26862 11.8922C4.82929 10.6306 5.4724 9.42895 6.19795 8.28717C6.92373 7.14517 7.72073 6.05106 8.58895 5.00484L6.70195 4.6125C6.34284 4.54072 5.99495 4.5545 5.65829 4.65384C5.32162 4.75295 5.02329 4.93239 4.76329 5.19217L1.05062 8.90484C0.785731 9.16995 0.687842 9.49306 0.756953 9.87417C0.826286 10.2553 1.0404 10.5177 1.39929 10.6615L4.26862 11.8922ZM20.971 0.097168C18.9232 0.140057 16.9921 0.595612 15.1776 1.46383C13.363 2.33228 11.7334 3.48872 10.289 4.93317C9.35917 5.86317 8.53573 6.87339 7.81862 7.96383C7.10151 9.0545 6.46006 10.1947 5.89429 11.3845C5.78318 11.6221 5.72762 11.8652 5.72762 12.1138C5.72762 12.3627 5.82162 12.5812 6.00962 12.7692L9.43262 16.1665C9.62062 16.3545 9.83473 16.4528 10.075 16.4615C10.3152 16.4699 10.5541 16.4186 10.7916 16.3075C11.9814 15.7588 13.1215 15.1174 14.212 14.3832C15.3026 13.6489 16.313 12.8169 17.243 11.8872C18.6874 10.4427 19.8438 8.81317 20.7123 6.9985C21.5805 5.18406 22.0361 3.25284 22.079 1.20484C22.079 1.06128 22.0515 0.92628 21.9966 0.799835C21.942 0.673391 21.8565 0.552058 21.7403 0.435836C21.6241 0.319614 21.5027 0.234058 21.3763 0.179169C21.2498 0.124502 21.1147 0.097168 20.971 0.097168ZM13.6583 8.51783C13.2667 8.12628 13.071 7.65228 13.071 7.09584C13.071 6.53961 13.2667 6.06572 13.6583 5.67417C14.0496 5.28284 14.5235 5.08717 15.08 5.08717C15.6364 5.08717 16.1104 5.28284 16.502 5.67417C16.8933 6.06572 17.089 6.53961 17.089 7.09584C17.089 7.65228 16.8933 8.12628 16.502 8.51783C16.1104 8.90917 15.6364 9.10484 15.08 9.10484C14.5235 9.10484 14.0496 8.90917 13.6583 8.51783ZM10.2583 17.9075L11.489 20.7768C11.6325 21.1357 11.895 21.3511 12.2763 21.4228C12.6574 21.4946 12.9804 21.3981 13.2453 21.1332L16.9583 17.4205C17.2181 17.1605 17.3975 16.8566 17.4966 16.5088C17.596 16.1611 17.6096 15.8076 17.5376 15.4485L17.1533 13.5872C16.102 14.4554 15.0066 15.2524 13.8673 15.9782C12.728 16.7037 11.525 17.3468 10.2583 17.9075ZM1.86095 16.5282C2.38229 16.0066 3.01473 15.7488 3.75829 15.7548C4.50184 15.7608 5.13429 16.0245 5.65562 16.5458C6.17695 17.0674 6.43762 17.6999 6.43762 18.4435C6.43762 19.1871 6.17695 19.8195 5.65562 20.3408C4.91384 21.0828 4.04162 21.5495 3.03895 21.7408C2.03651 21.9324 1.02418 22.0768 0.00195312 22.1742C0.0992865 21.1348 0.249286 20.1182 0.451953 19.1242C0.654398 18.1302 1.12406 17.2648 1.86095 16.5282Z" fill="var(--color-brand-primary)"/></svg>',
        studioHeading: 'Build',
        studioCards: [
          {
            id: 'agent',
            title: 'Agent',
            description: 'It is an autonomous entity that can perceive its environment, reason for its actions defined by its role and goal.',
            image: 'svgs/sidenav/agent.svg',
          },
          {
            id: 'pipeline',
            title: 'Pipeline',
            description: 'It is an  sequences of steps or tasks that are executed to achieve a desired outcome.',
            image: 'svgs/sidenav/pipeline.svg',
          },
          {
            id: 'model',
            title: 'Model',
            description: 'A unified interface that seamlessly connects users to a variety of open AI models, delivering flexibility and convenience.',
            image: 'svgs/sidenav/modals.svg',
          },
          {
            id: 'tool',
            title: 'Tool',
            description: 'It is an external function, API, or utility that agents can invoke to gather information or trigger actions.',
            image: 'svgs/sidenav/tools.svg',
          },
          {
            id: 'guardrail',
            title: 'Guardrail',
            description: 'It is an set of guidelines designed to ensure responsible AI use. They act as safeguards.',
            image: 'svgs/sidenav/guardrail.svg',
          },
          {
            id: 'knowledge',
            title: 'Knowledge Base',
            description: 'It combines the strengths of traditional information retrieval systems with the capabilities of generative large language models (LLMs).',
            image: 'svgs/sidenav/kb.svg',
          },
        ],
      },
      {
        id: 'studios',
        iconName: '',
        label: 'Studios',
        action: '',
        isSelected: false,
        tooltip: 'Studios',
        iconType: 'lucide',
        isDisabled: true,
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="26" viewBox="0 0 24 26" fill="none"><path d="M7.11797 21.6668H3.97697C3.57341 21.6668 3.20886 21.5659 2.8833 21.3642C2.55775 21.1626 2.2983 20.8934 2.10497 20.5565L0.707636 18.0438C0.514524 17.7121 0.417969 17.3642 0.417969 17.0002C0.417969 16.6362 0.514524 16.2883 0.707636 15.9565L2.3693 13.0002L0.707636 10.0438C0.514524 9.71205 0.417969 9.36416 0.417969 9.00016C0.417969 8.63616 0.514524 8.28827 0.707636 7.9565L2.10497 5.44383C2.2983 5.10694 2.55775 4.83772 2.8833 4.63616C3.20886 4.43439 3.57341 4.3335 3.97697 4.3335H7.11797L8.77164 1.41816C8.96497 1.08127 9.22441 0.816274 9.54997 0.623163C9.87553 0.430052 10.2401 0.333496 10.6436 0.333496H13.3563C13.7599 0.333496 14.1244 0.430052 14.45 0.623163C14.7755 0.816274 15.035 1.08127 15.2283 1.41816L16.882 4.3335H20.023C20.4265 4.3335 20.7911 4.43439 21.1166 4.63616C21.4422 4.83772 21.7016 5.10694 21.895 5.44383L23.2923 7.9565C23.4854 8.28827 23.582 8.63616 23.582 9.00016C23.582 9.36416 23.4854 9.71205 23.2923 10.0438L21.6306 13.0002L23.2923 15.9565C23.4854 16.2883 23.582 16.6362 23.582 17.0002C23.582 17.3642 23.4854 17.7121 23.2923 18.0438L21.895 20.5565C21.7016 20.8934 21.4422 21.1626 21.1166 21.3642C20.7911 21.5659 20.4265 21.6668 20.023 21.6668H16.882L15.2283 24.5822C15.035 24.9191 14.7755 25.1841 14.45 25.3772C14.1244 25.5703 13.7599 25.6668 13.3563 25.6668H10.6436C10.2401 25.6668 9.87553 25.5703 9.54997 25.3772C9.22441 25.1841 8.96497 24.9191 8.77164 24.5822L7.11797 21.6668ZM16.882 12.3335H20.518L22.1383 9.4105C22.2067 9.27361 22.241 9.13683 22.241 9.00016C22.241 8.8635 22.2067 8.72672 22.1383 8.58983L20.7486 6.07716C20.6633 5.94027 20.5607 5.83772 20.441 5.7695C20.3214 5.70105 20.1846 5.66683 20.0306 5.66683H16.882L14.9973 9.00016L16.882 12.3335ZM10.2153 16.3335H13.7846L15.6693 13.0002L13.7846 9.66683H10.2153L8.33064 13.0002L10.2153 16.3335ZM10.2153 8.3335H13.7846L15.7026 4.96683L14.0486 2.07716C13.9633 1.94027 13.8565 1.83772 13.7283 1.7695C13.6001 1.70105 13.4675 1.66683 13.3306 1.66683H10.6693C10.5324 1.66683 10.3999 1.70105 10.2716 1.7695C10.1434 1.83772 10.0366 1.94027 9.9513 2.07716L8.2973 4.96683L10.2153 8.3335ZM9.00264 9.00016L7.11797 5.66683H4.00264C3.84864 5.66683 3.71186 5.70105 3.5923 5.7695C3.47252 5.83772 3.36997 5.94027 3.28464 6.07716L1.86164 8.58983C1.77608 8.72672 1.7333 8.8635 1.7333 9.00016C1.7333 9.13683 1.77608 9.27361 1.86164 9.4105L3.5153 12.3335H7.11797L9.00264 9.00016ZM9.00264 17.0002L7.11797 13.6668H3.48197L1.86164 16.5898C1.79319 16.7267 1.75897 16.8635 1.75897 17.0002C1.75897 17.1368 1.79319 17.2736 1.86164 17.4105L3.28464 19.9232C3.36997 20.0601 3.47252 20.1626 3.5923 20.2308C3.71186 20.2993 3.84864 20.3335 4.00264 20.3335H7.11797L9.00264 17.0002ZM10.2153 17.6668L8.2973 21.0335L9.9513 23.9232C10.0366 24.0601 10.1434 24.1626 10.2716 24.2308C10.3999 24.2993 10.5324 24.3335 10.6693 24.3335H13.3306C13.4675 24.3335 13.6001 24.2993 13.7283 24.2308C13.8565 24.1626 13.9633 24.0601 14.0486 23.9232L15.7026 21.0335L13.7846 17.6668H10.2153ZM16.882 20.3335H19.9973C20.1513 20.3335 20.2881 20.2993 20.4076 20.2308C20.5274 20.1626 20.63 20.0601 20.7153 19.9232L22.1383 17.4105C22.2239 17.2736 22.2666 17.1368 22.2666 17.0002C22.2666 16.8635 22.2239 16.7267 22.1383 16.5898L20.4846 13.6668H16.882L14.9973 17.0002L16.882 20.3335Z" fill="#898E99"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="26" viewBox="0 0 24 26" fill="none"><path d="M17.2636 12.4875H21.5253L23.1099 9.65916C23.3031 9.32761 23.3996 8.97972 23.3996 8.6155C23.3996 8.2515 23.3031 7.90361 23.1099 7.57183L22.1379 5.85416C21.9448 5.51727 21.6855 5.24794 21.3599 5.04616C21.0342 4.84461 20.6696 4.74383 20.2663 4.74383H17.2636L15.0996 8.6155L17.2636 12.4875ZM9.86894 16.8718H14.1303L16.2946 13.0002L14.1303 9.1285H9.86894L7.70461 13.0002L9.86894 16.8718ZM9.86894 8.07716H14.1303L16.2946 4.20516L14.7433 1.44383C14.5502 1.10694 14.2907 0.837719 13.9649 0.636163C13.6394 0.434386 13.2748 0.333496 12.8713 0.333496H11.1279C10.7244 0.333496 10.3598 0.434386 10.0343 0.636163C9.7085 0.837719 9.44905 1.10694 9.25594 1.44383L7.70461 4.20516L9.86894 8.07716ZM8.87661 8.6155L6.73561 4.74383H3.73294C3.32961 4.74383 2.96505 4.84461 2.63928 5.04616C2.31372 5.24794 2.05439 5.51727 1.86128 5.85416L0.889276 7.57183C0.696165 7.90361 0.599609 8.2515 0.599609 8.6155C0.599609 8.97972 0.696165 9.32761 0.889276 9.65916L2.47394 12.4875H6.73561L8.87661 8.6155ZM8.87661 17.3848L6.73561 13.5128H2.47394L0.889276 16.3412C0.696165 16.6727 0.599609 17.0206 0.599609 17.3848C0.599609 17.7488 0.696165 18.0967 0.889276 18.4285L1.86128 20.1462C2.05439 20.4831 2.31372 20.7524 2.63928 20.9542C2.96505 21.1557 3.32961 21.2565 3.73294 21.2565H6.73561L8.87661 17.3848ZM9.86894 17.9232L7.70461 21.7952L9.28694 24.5718C9.48005 24.9036 9.73816 25.1691 10.0613 25.3682C10.3842 25.5673 10.7448 25.6668 11.1433 25.6668H12.8713C13.2748 25.6668 13.6394 25.5659 13.9649 25.3642C14.2907 25.1626 14.5502 24.8934 14.7433 24.5565L16.2946 21.7952L14.1303 17.9232H9.86894ZM17.2636 21.2565H20.2663C20.6696 21.2565 21.0342 21.1557 21.3599 20.9542C21.6855 20.7524 21.9448 20.4831 22.1379 20.1462L23.1099 18.4285C23.3031 18.0967 23.3996 17.7488 23.3996 17.3848C23.3996 17.0206 23.3031 16.6727 23.1099 16.3412L21.5253 13.5128H17.2636L15.0996 17.3848L17.2636 21.2565Z" fill="var(--color-brand-primary)"/></svg>',
      },
      {
        id: 'marketplace',
        iconName: '',
        label: 'Cortex',
        action: 'marketplace-action',
        isSelected: false,
        tooltip: 'Marketplace',
        iconType: 'lucide',
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="26" height="24" viewBox="0 0 26 24" fill="none"><path d="M3.82866 23.3332C3.21488 23.3332 2.70244 23.1276 2.29132 22.7165C1.88021 22.3054 1.67466 21.7929 1.67466 21.1792V10.4768C1.11221 10.0615 0.700658 9.51706 0.439992 8.8435C0.179325 8.16995 0.17377 7.45117 0.423325 6.68717L1.72099 2.41017C1.89877 1.86662 2.18544 1.44006 2.58099 1.1305C2.97677 0.82117 3.4661 0.666504 4.04899 0.666504H21.9157C22.4985 0.666504 22.9865 0.811392 23.3797 1.10117C23.773 1.39095 24.0611 1.81017 24.244 2.35884L25.5927 6.68717C25.8422 7.45117 25.8367 8.17595 25.576 8.8615C25.3153 9.54684 24.9038 10.1024 24.3413 10.5282V21.1792C24.3413 21.7929 24.1358 22.3054 23.7247 22.7165C23.3135 23.1276 22.8012 23.3332 22.1877 23.3332H3.82866ZM15.9413 9.99984C16.7978 9.99984 17.4371 9.76006 17.8593 9.2805C18.2815 8.80117 18.4593 8.30595 18.3927 7.79484L17.5053 1.99984H13.6747V7.59984C13.6747 8.2545 13.8985 8.81817 14.3463 9.29084C14.7943 9.7635 15.326 9.99984 15.9413 9.99984ZM9.94133 9.99984C10.6575 9.99984 11.2367 9.7635 11.6787 9.29084C12.1204 8.81817 12.3413 8.2545 12.3413 7.59984V1.99984H8.51066L7.62332 7.89717C7.56866 8.31095 7.74944 8.76484 8.16566 9.25884C8.58188 9.75284 9.17377 9.99984 9.94133 9.99984ZM4.00799 9.99984C4.59599 9.99984 5.09555 9.79984 5.50666 9.39984C5.91777 8.99984 6.17377 8.50073 6.27466 7.9025L7.11066 1.99984H4.04899C3.75832 1.99984 3.52755 2.06395 3.35666 2.19217C3.18577 2.32039 3.05755 2.51273 2.97199 2.76917L1.74132 6.97917C1.52244 7.68006 1.61599 8.35873 2.02199 9.01517C2.42799 9.67162 3.08999 9.99984 4.00799 9.99984ZM22.008 9.99984C22.8062 9.99984 23.4439 9.68873 23.921 9.0665C24.3979 8.44428 24.5158 7.7485 24.2747 6.97917L22.9773 2.71784C22.8918 2.46139 22.7635 2.27762 22.5927 2.1665C22.4218 2.05539 22.191 1.99984 21.9003 1.99984H18.9053L19.7413 7.9025C19.8422 8.50073 20.0982 8.99984 20.5093 9.39984C20.9204 9.79984 21.42 9.99984 22.008 9.99984ZM3.82866 21.9998H22.1877C22.4268 21.9998 22.6233 21.923 22.7773 21.7692C22.9311 21.6152 23.008 21.4185 23.008 21.1792V11.1638C22.8284 11.2254 22.6579 11.2691 22.4963 11.2948C22.335 11.3204 22.1722 11.3332 22.008 11.3332C21.408 11.3332 20.8802 11.2161 20.4247 10.9818C19.9691 10.7476 19.5362 10.3742 19.126 9.8615C18.7773 10.2955 18.3474 10.6493 17.8363 10.9228C17.3252 11.1964 16.7038 11.3332 15.972 11.3332C15.4404 11.3332 14.9349 11.2093 14.4553 10.9615C13.976 10.7135 13.4935 10.3468 13.008 9.8615C12.5618 10.3468 12.0673 10.7135 11.5247 10.9615C10.982 11.2093 10.4644 11.3332 9.97199 11.3332C9.4131 11.3332 8.86832 11.2263 8.33766 11.0125C7.80677 10.7989 7.34133 10.4153 6.94133 9.8615C6.28666 10.5162 5.71444 10.9251 5.22466 11.0882C4.73488 11.2515 4.32932 11.3332 4.00799 11.3332C3.84399 11.3332 3.67866 11.3204 3.51199 11.2948C3.34532 11.2691 3.17732 11.2254 3.00799 11.1638V21.1792C3.00799 21.4185 3.08488 21.6152 3.23866 21.7692C3.39266 21.923 3.58933 21.9998 3.82866 21.9998Z" fill="#4C515B"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="26" height="24" viewBox="0 0 26 24" fill="none"><path d="M3.82866 23.3332C3.21488 23.3332 2.70244 23.1276 2.29132 22.7165C1.88021 22.3054 1.67466 21.7929 1.67466 21.1792V10.4768C1.11221 10.0615 0.700658 9.51706 0.439992 8.8435C0.179325 8.16995 0.17377 7.45117 0.423325 6.68717L1.72099 2.41017C1.89877 1.86662 2.18544 1.44006 2.58099 1.1305C2.97677 0.82117 3.4661 0.666504 4.04899 0.666504H21.9157C22.4985 0.666504 22.9865 0.811392 23.3797 1.10117C23.773 1.39095 24.0611 1.81017 24.244 2.35884L25.5927 6.68717C25.8422 7.45117 25.8367 8.17595 25.576 8.8615C25.3153 9.54684 24.9038 10.1024 24.3413 10.5282V21.1792C24.3413 21.7929 24.1358 22.3054 23.7247 22.7165C23.3135 23.1276 22.8012 23.3332 22.1877 23.3332H3.82866ZM15.9413 9.99984C16.7978 9.99984 17.4371 9.76006 17.8593 9.2805C18.2815 8.80117 18.4593 8.30595 18.3927 7.79484L17.5053 1.99984H13.6747V7.59984C13.6747 8.2545 13.8985 8.81817 14.3463 9.29084C14.7943 9.7635 15.326 9.99984 15.9413 9.99984ZM9.94133 9.99984C10.6575 9.99984 11.2367 9.7635 11.6787 9.29084C12.1204 8.81817 12.3413 8.2545 12.3413 7.59984V1.99984H8.51066L7.62332 7.89717C7.56866 8.31095 7.74944 8.76484 8.16566 9.25884C8.58188 9.75284 9.17377 9.99984 9.94133 9.99984ZM4.00799 9.99984C4.59599 9.99984 5.09555 9.79984 5.50666 9.39984C5.91777 8.99984 6.17377 8.50073 6.27466 7.9025L7.11066 1.99984H4.04899C3.75832 1.99984 3.52755 2.06395 3.35666 2.19217C3.18577 2.32039 3.05755 2.51273 2.97199 2.76917L1.74132 6.97917C1.52244 7.68006 1.61599 8.35873 2.02199 9.01517C2.42799 9.67162 3.08999 9.99984 4.00799 9.99984ZM22.008 9.99984C22.8062 9.99984 23.4439 9.68873 23.921 9.0665C24.3979 8.44428 24.5158 7.7485 24.2747 6.97917L22.9773 2.71784C22.8918 2.46139 22.7635 2.27762 22.5927 2.1665C22.4218 2.05539 22.191 1.99984 21.9003 1.99984H18.9053L19.7413 7.9025C19.8422 8.50073 20.0982 8.99984 20.5093 9.39984C20.9204 9.79984 21.42 9.99984 22.008 9.99984Z" fill="var(--color-brand-primary)"/></svg>',
      },
      {
        id: 'account',
        iconName: '',
        label: 'Account',
        action: 'account-action',
        isSelected: false,
        defaultSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none"><path d="M11.2927 1.18735L18.472 6.59768C18.7413 6.79501 18.9522 7.04624 19.1047 7.35135C19.2573 7.65646 19.3337 7.97957 19.3337 8.32068V19.0897C19.3337 19.6821 19.1228 20.1892 18.701 20.611C18.2792 21.0328 17.7721 21.2437 17.1797 21.2437H2.82099C2.22855 21.2437 1.72144 21.0328 1.29966 20.611C0.877881 20.1892 0.666992 19.6821 0.666992 19.0897V8.32068C0.666992 7.97957 0.743325 7.65646 0.895992 7.35135C1.04844 7.04624 1.25933 6.79501 1.52866 6.59768L8.70799 1.18735C9.08377 0.900014 9.51322 0.756348 9.99633 0.756348C10.4794 0.756348 10.9115 0.900014 11.2927 1.18735ZM9.99733 13.9103C10.9224 13.9103 11.7098 13.5866 12.3593 12.939C13.0089 12.2915 13.3337 11.5051 13.3337 10.58C13.3337 9.6549 13.0099 8.86757 12.3623 8.21801C11.7148 7.56846 10.9284 7.24368 10.0033 7.24368C9.07821 7.24368 8.29088 7.56746 7.64133 8.21501C6.99177 8.86257 6.66699 9.6489 6.66699 10.574C6.66699 11.4991 6.99077 12.2865 7.63833 12.936C8.28588 13.5856 9.07222 13.9103 9.99733 13.9103ZM10.0003 12.577C9.45166 12.577 8.9811 12.3809 8.58866 11.9887C8.19644 11.5962 8.00033 11.1257 8.00033 10.577C8.00033 10.0283 8.19644 9.55779 8.58866 9.16535C8.9811 8.77313 9.45166 8.57701 10.0003 8.57701C10.549 8.57701 11.0195 8.77313 11.412 9.16535C11.8042 9.55779 12.0003 10.0283 12.0003 10.577C12.0003 11.1257 11.8042 11.5962 11.412 11.9887C11.0195 12.3809 10.549 12.577 10.0003 12.577ZM9.93166 17.9103C8.79277 17.9103 7.68744 18.0812 6.61566 18.423C5.54388 18.765 4.53366 19.2608 3.58499 19.9103H16.2877C15.3559 19.2608 14.3483 18.765 13.265 18.423C12.1817 18.0812 11.0705 17.9103 9.93166 17.9103ZM2.00033 8.32068V19.5C3.12166 18.5651 4.35733 17.8442 5.70733 17.3373C7.05733 16.8305 8.46266 16.577 9.92333 16.577C11.414 16.577 12.8482 16.8261 14.226 17.3243C15.6038 17.8228 16.8619 18.5395 18.0003 19.4743V8.32068C18.0003 8.18379 17.9704 8.05979 17.9107 7.94868C17.8509 7.83757 17.7697 7.73935 17.667 7.65401L10.4877 2.26935C10.3508 2.14957 10.1883 2.08968 10.0003 2.08968C9.81233 2.08968 9.64988 2.14957 9.51299 2.26935L2.33366 7.65401C2.23099 7.73935 2.14977 7.83757 2.08999 7.94868C2.03021 8.05979 2.00033 8.18379 2.00033 8.32068Z" fill="#898E99"/></svg>',
        activeSvg: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none"><path d="M9.99733 13.9103C10.9224 13.9103 11.7098 13.5866 12.3593 12.939C13.0089 12.2915 13.3337 11.5051 13.3337 10.58C13.3337 9.6549 13.0099 8.86757 12.3623 8.21801C11.7148 7.56846 10.9284 7.24368 10.0033 7.24368C9.07821 7.24368 8.29088 7.56746 7.64133 8.21501C6.99177 8.86257 6.66699 9.6489 6.66699 10.574C6.66699 11.4991 6.99077 12.2865 7.63833 12.936C8.28588 13.5856 9.07222 13.9103 9.99733 13.9103ZM9.92333 15.8077C8.63444 15.8077 7.38444 16.0081 6.17333 16.409C4.96221 16.8099 3.82933 17.389 2.77466 18.1463C2.57644 18.2848 2.39777 18.4446 2.23866 18.6257C2.07977 18.807 2.00033 19.019 2.00033 19.2617V19.3463C2.00033 19.5172 2.06444 19.6539 2.19266 19.7563C2.32088 19.859 2.47899 19.9103 2.66699 19.9103H17.3337C17.5217 19.9103 17.6798 19.8548 17.808 19.7437C17.9362 19.6326 18.0003 19.4916 18.0003 19.3207V19.2617C18.0003 19.019 17.9209 18.8028 17.762 18.613C17.6029 18.4232 17.4242 18.2591 17.226 18.1207C16.1542 17.3633 14.9988 16.7886 13.7597 16.3963C12.5208 16.0039 11.242 15.8077 9.92333 15.8077ZM2.82099 21.2437C2.22855 21.2437 1.72144 21.0328 1.29966 20.611C0.877881 20.1892 0.666992 19.6821 0.666992 19.0897V8.32068C0.666992 7.97957 0.743325 7.65646 0.895992 7.35135C1.04844 7.04624 1.25933 6.79501 1.52866 6.59768L8.70799 1.18735C9.08377 0.900014 9.51322 0.756348 9.99633 0.756348C10.4794 0.756348 10.9115 0.900014 11.2927 1.18735L18.472 6.59768C18.7413 6.79501 18.9522 7.04624 19.1047 7.35135C19.2573 7.65646 19.3337 7.97957 19.3337 8.32068V19.0897C19.3337 19.6821 19.1228 20.1892 18.701 20.611C18.2792 21.0328 17.7721 21.2437 17.1797 21.2437H2.82099Z" fill="var(--color-brand-primary)"/></svg>',
        tooltip: 'My Account',
        iconType: 'lucide',
      },
    ],
    subMenuItems: [
      {
        id: 'closePanel',
        iconName: 'panel-left-close',
        label: '',
        action: 'close-panel-action',
        isSelected: false,
        iconType: 'lucide',
        tooltip: '',
        retainActiveState: false, // This item will NOT retain active state (action-based)
      },
    ],
    profile: {
      altText: 'User Profile',
      action: 'profile-action',
      tooltip: '',
      initials: '',
      initialsBackground: '',
      initialsColor: '',
      profileMenu: [
        {
          id: 'settings',
          iconName: 'Settings',
          label: '',
          action: 'open-settings',
          iconType: 'lucide',
          tooltip: '',
          isDisabled: true,
        },
        {
          id: 'theme',
          iconName: 'Sun',
          label: '',
          action: 'toggle-theme',
          iconType: 'lucide',
          isDisabled: true,
          tooltip: '',
          isToggle: true,
          subMenuItems: [
            {
              id: 'light',
              iconName: 'Sun',
              label: '',
              action: 'set-theme-light',
              iconType: 'lucide',
              tooltip: '',
            },
            {
              id: 'dark',
              iconName: 'Moon',
              label: '',
              action: 'set-theme-dark',
              iconType: 'lucide',
              tooltip: '',
            },
            {
              id: 'auto',
              iconName: 'Monitor',
              label: '',
              action: 'set-theme-auto',
              iconType: 'lucide',
              tooltip: '',
            },
          ],
          selectedSubItem: {
            id: 'light',
            iconName: 'Sun',
            label: '',
            action: 'set-theme-light',
            iconType: 'lucide',
            tooltip: '',
          },
        },
        {
          id: 'language',
          iconName: 'Languages',
          label: '',
          action: 'change-language',
          iconType: 'lucide',
          tooltip: '',
          isToggle: true,
          isDisabled: true,
        },
        {
          id: 'logout',
          iconName: 'LogOut',
          label: '',
          action: 'logout',
          iconType: 'lucide',
          tooltip: '',
          isDestructive: true,
        },
      ],
    },
  };

  breadcrumbList: BreadcrumbItem[] = [];
  private breadcrumbSubscription: Subscription;

  constructor(
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private centralizedRedirectService: CentralizedRedirectService,
    private themeInitService: ThemeInitService,
    // private themeService: ThemeService,
    private breadcrumbService: BreadcrumbService,
    private router: Router,
    private elementRef: ElementRef
  ) {
    // Subscribe to breadcrumb updates from the service
    this.breadcrumbSubscription = this.breadcrumbService.breadcrumbs$.subscribe(
      breadcrumbs => {
        this.breadcrumbList = [...breadcrumbs]; // Create new array reference for OnPush change detection

        // Update sidebar selection when breadcrumbs change
        const currentRoute = this.router.url;
        const menuItemId = this.getMenuItemIdFromRoute(currentRoute);
        if (menuItemId) {
          this.updateSidebarSelection(menuItemId);
        }
      }
    );
  }

  toggleChat(): boolean {
    this.toggleRevelio = !this.toggleRevelio;
    return this.toggleRevelio;
  }
  ngOnInit(): void {
    // Initialize theme system early
    this.themeInitService.initialize();

    // Initialize breadcrumb service with routes
    this.breadcrumbService.initializeWithRoutes(routes);

    // Sync sidebar selection with route changes
    this.syncSidebarWithRoute();

    // Initialize sidebar selection based on current route
    this.initializeSidebarSelection();

    // Set user initials and colors in profile
    this.sidebarConfig.profile!.initials = this.getUserInitials();
    const avatarColors = this.getUserAvatarColors();
    this.sidebarConfig.profile!.initialsBackground = avatarColors.backgroundColor;
    this.sidebarConfig.profile!.initialsColor = avatarColors.textColor;

    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: environment.consoleRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'console',
    };

    this.authService.setAuthConfig(authConfig);
    this.authService.handleAuthCodeAndToken();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    if (this.breadcrumbSubscription) {
      this.breadcrumbSubscription.unsubscribe();
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  onMenuItemClick(item: any): void {
    // Navigate to the matching route based on menu item ID
    if (item && item.id) {
      const routePath = this.getRoutePathFromMenuItemId(item.id);
      if (routePath) {
        // Navigate to the route
        this.router.navigate([routePath]);

        // Update sidebar selection to match navigation
        this.updateSidebarSelection(item.id);
      }
    }
  }

  /**
   * Update sidebar selection state
   */
  private updateSidebarSelection(selectedItemId: string): void {
    // First reset all items to false
    this.sidebarConfig.menuItems.forEach(menuItem => {
      menuItem.isSelected = false;
    });

    // Then set the selected item to true
    const selectedItem = this.sidebarConfig.menuItems.find(
      menuItem => menuItem.id === selectedItemId
    );
    if (selectedItem) {
      selectedItem.isSelected = true;
    }
  }

  /**
   * Initialize sidebar selection based on current route
   */
  private initializeSidebarSelection(): void {
    const currentRoute = this.router.url;
    const menuItemId = this.getMenuItemIdFromRoute(currentRoute);

    if (menuItemId) {
      this.updateSidebarSelection(menuItemId);
    }
  }

  /**
   * Sync sidebar selection with route changes
   */
  private syncSidebarWithRoute(): void {
    this.routeSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        const currentRoute = event.urlAfterRedirects || event.url;
        const menuItemId = this.getMenuItemIdFromRoute(currentRoute);

        if (menuItemId) {
          this.updateSidebarSelection(menuItemId);
        }
      });
  }

  /**
   * Get menu item ID from current route
   */
  private getMenuItemIdFromRoute(route: string): string | null {
    // Remove leading slash and 'console' prefix if present
    const cleanRoute = route.replace(/^\/?(console\/)?/, '');

    // Handle root route (empty string or just '/')
    if (!cleanRoute || cleanRoute === '/') {
      return 'dashboard';
    }

    // Extract the first segment of the route
    const firstSegment = cleanRoute.split('/')[0];

    // Map route segments to menu item IDs
    const routeToMenuMapping: { [key: string]: string } = {
      dashboard: '/',
      approvals: 'approvals',
      marketplace: 'marketplace',
      manage: 'manage',
      // analytics: 'analytics',
      agent: 'build/agent',
      pipeline: 'build/pipelines',
      model: 'build/model',
      tool: 'build/tools',
      guardrail: 'build/guardrails',
      knowledge: 'build/knowledge',
    };

    return routeToMenuMapping[firstSegment] || null;
  }

  /**
   * Get route path from menu item ID
   */
  private getRoutePathFromMenuItemId(menuItemId: string): string | null {
    const routeMapping: { [key: string]: string } = {
      dashboard: '/',
      account: 'account',
      marketplace: 'marketplace',
      analytics: 'analytics',
      'my-list': 'my-list',
      agent: 'build/agent',
      pipeline: 'build/pipelines',
      model: 'build/model',
      tool: 'build/tools',
      guardrail: 'build/guardrails',
      knowledge: 'build/knowledge',
    };

    return routeMapping[menuItemId] || null;
  }

  onSubMenuItemClick(item: any): void {
    if (item.id === 'closePanel') {
      this.leftPanelOpen = false;
    } else if (item.id === 'themetoggle') {
      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', this.currentTheme);
    }
  }

  onProfileClick(action: string): void {
    // console.log('Profile clicked:', action);
  }

  onStudioCardClick(event: { menuItemId: string; card: any }): void {
    if (event.menuItemId === 'build') {
      if (event.card && event.card.id) {
        const routePath = this.getRoutePathFromMenuItemId(event.card.id);
        if (routePath) {
          // Navigate to the route
          this.router.navigate([routePath]);

          // Update sidebar selection to match navigation
          this.updateSidebarSelection(event.card.id);
        }
      }
    }
  }

  // Handle clicking outside of Revelio component to close it
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.toggleRevelio) {
      const target = event.target as HTMLElement;

      // Get the revelio component element
      const revelioElement =
        this.elementRef.nativeElement.querySelector('app-revelio');

      // Get the chat icon that triggers the revelio
      const chatIcon = this.elementRef.nativeElement.querySelector(
        '.header-right .revelio-icon'
      );

      // Check if click is outside both the revelio component and the chat icon
      if (
        revelioElement &&
        chatIcon &&
        !revelioElement.contains(target) &&
        !chatIcon.contains(target)
      ) {
        this.toggleRevelio = false;
      }
    }
  }

  onProfileMenuItemClick(item: any): void {
    if (!item) {
      return;
    }

    // Handle logout action
    if (item.action === 'logout' || item.id === 'logout') {
      // SSO logout only - clears state and navigates to provider logout URL
      this.authService.logout().subscribe({
        error: () => {
          // Fallback in case of error
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
      return;
    }
  }

  onProfileSubMenuItemClick(item: any): void {
    // Handle theme changes
    if (item.parentItem?.action === 'toggle-theme' && item.subItem) {
      const themeAction = item.subItem.action;

      switch (themeAction) {
        case 'set-theme-light':
          // this.themeService.setUserPreference('light');
          break;

        case 'set-theme-dark':
          // this.themeService.setUserPreference('dark');
          break;

        case 'set-theme-auto':
          // this.themeService.setUserPreference('system');
          break;

        default:
          console.warn('Unknown theme action:', themeAction);
      }

      // Update current theme property for UI updates
      // this.currentTheme = this.themeService.getCurrentTheme();
    }
  }

  /**
   * Get user initials from the logged-in user's name
   */
  getUserInitials(): string {
    const fullName = this.tokenStorage.getDaName();
    if (!fullName || fullName === 'Guest') {
      return ''; // Return empty string for guest users - no avatar will be shown
    }

    const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);

    if (nameParts.length >= 2) {
      // First letter of first name and first letter of last name
      return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
    } else if (nameParts.length === 1) {
      // Just first letter if only one name
      return nameParts[0][0].toUpperCase();
    } else {
      return ''; // Return empty string - no avatar will be shown
    }
  }

  /**
   * Generate dynamic background and text colors based on username
   */
  getUserAvatarColors(): { backgroundColor: string; textColor: string } {
    const colors = [
      '#ED4B82',
      '#9661F1',
      '#5082EF',
      '#FB8DAE',
      '#FEE765',
      '#35CADD',
      '#69CAA6',
      '#898E99',
      '#E35151',
    ];

    const fullName = this.tokenStorage.getDaName();
    if (!fullName || fullName === 'Guest') {
      return { backgroundColor: colors[0], textColor: '#ffffff' };
    }

    // Use username length to determine color index for consistency
    const colorIndex = fullName.length % colors.length;

    return {
      backgroundColor: colors[colorIndex],
      textColor: '#ffffff'
    };
  }
}
