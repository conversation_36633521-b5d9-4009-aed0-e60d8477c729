/**
 * Polyfills for Experience Studio Application
 *
 * This file includes polyfills needed by <PERSON><PERSON> and is loaded before the app.
 * You can add your own extra polyfills to this file.
 *
 * This file is divided into 2 sections:
 *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by the order of browsers.
 *   2. Application imports. Files imported after ZoneJS that should be loaded before your main
 *      file.
 *
 * The current setup is for so-called "evergreen" browsers; the last versions of browsers that
 * automatically update themselves. This includes recent versions of Safari, Chrome (including
 * Opera), Edge on the desktop, and iOS and Chrome on mobile.
 *
 * Learn more in https://angular.io/guide/browser-support
 */

/***************************************************************************************************
 * BROWSER POLYFILLS
 */

/**
 * EventSource Polyfill
 *
 * This polyfill provides EventSource support with additional features:
 * - Custom headers support (including last-event-id)
 * - Better error handling and reconnection
 * - Cross-browser compatibility
 * - CORS support with credentials
 *
 * The polyfill extends the native EventSource API while maintaining compatibility.
 * It enables header-based event ID checkpointing instead of URL parameters.
 */
import 'event-source-polyfill';

/***************************************************************************************************
 * Zone JS is required by default for Angular itself.
 */
import 'zone.js'; // Included with Angular CLI.

/***************************************************************************************************
 * APPLICATION IMPORTS
 */

/**
 * EventSource Polyfill Type Declarations
 *
 * Extend the global EventSource interface to include polyfill-specific options
 * that support custom headers and enhanced configuration.
 */
declare global {
  interface EventSourceInit {
    withCredentials?: boolean;
    headers?: Record<string, string>;
    proxy?: string;
    https?: any;
    rejectUnauthorized?: boolean;
  }

  /**
   * Enhanced EventSource constructor that supports custom headers
   * This is provided by the event-source-polyfill package
   */
  interface EventSourceConstructor {
    new (url: string, eventSourceInitDict?: EventSourceInit): EventSource;
    readonly CLOSED: number;
    readonly CONNECTING: number;
    readonly OPEN: number;
  }
}

/**
 * Configure EventSource Polyfill
 *
 * The polyfill automatically replaces the native EventSource if needed,
 * but we can configure default behavior here.
 */
if (typeof window !== 'undefined') {
  // Log polyfill status for debugging
  // Log polyfill status for debugging in development only
  import('./app/shared/utils/logger').then(({ logger }) => {
    logger.info('🔧 EventSource Polyfill loaded:', {
      hasNativeEventSource: 'EventSource' in window,
      polyfillVersion: 'event-source-polyfill',
      supportsHeaders: true,
      timestamp: new Date().toISOString()
    });
  });
}
