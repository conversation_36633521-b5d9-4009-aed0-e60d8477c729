/**
* =========================================================================
* Play+ Design System: Console Theme
*
* Console theme overrides for semantic tokens.
* This theme uses a blue and aqua color scheme for a modern console look.
* Primary: Blue (#2563EB) | Secondary: Aqua (#03BDD4)
* =========================================================================
*/

[data-theme='light'] {
  /* --- Light Theme Color Overrides --- */
  /* PRIMARY (Vibrant Pink) */
  --color-brand-primary: var(--global-color-pink-500);
  --color-brand-primary-hover: var(--global-color-pink-700);
  --color-brand-primary-active: var(--global-color-purple-500);
  --color-surface-interactive-primary: var(--global-color-pink-500);
  --color-surface-interactive-primary-hover: var(--global-color-pink-700);
  --color-surface-interactive-primary-active: var(--global-color-purple-500);
  --color-border-primary: var(--global-color-pink-500);
  --color-border-primary-hover: var(--global-color-pink-700);
  --color-border-primary-active: var(--global-color-purple-500);
  --color-text-primary: var(--global-color-gray-700);
  --color-text-on-primary: var(--global-color-white);
  --color-text-inactive-tab-button: var(--global-color-black);
  --color-text-active-stepper-circle: var(--global-color-gray-800);
  --color-text-accordion-content: var(--global-color-gray-800);
  --color-textbox-input: var(--global-color-gray-700);
  --textbox-surface-primary: var(--global-color-white);

  /* SECONDARY (Light Blue) */
  --color-brand-secondary: var(--global-color-blue-info-500);
  --color-brand-secondary-hover: var(--global-color-royal-blue-500);
  --color-brand-secondary-active: var(--global-color-royal-blue-700);
  --color-surface-interactive-secondary: var(--global-color-blue-100);
  --color-surface-interactive-secondary-hover: var(--global-color-blue-info-500);
  --color-surface-interactive-secondary-active: var(--global-color-royal-blue-500);
  --color-border-secondary: var(--global-color-blue-info-500);
  --color-border-secondary-hover: var(--global-color-royal-blue-500);
  --color-border-secondary-active: var(--global-color-royal-blue-700);
  --color-text-secondary: var(--global-color-gray-700);
  --color-text-on-secondary: var(--global-color-white);
  --color-background-secondary: var(--global-color-blue-100);

  /* BUTTONS, TABS, TAGS: Use these tokens for all secondary/primary states */
  --color-text-placeholder: var(--global-color-gray-400);
  --color-text-disabled: var(--global-color-gray-400);
  --color-text-on-brand: var(--global-color-white);
  --color-text-interactive: var(--global-color-pink-500);
  --color-text-interactive-hover: var(--global-color-pink-700);
  --color-text-success: var(--global-color-green-500);
  --color-text-error: var(--global-color-red-500);
  --color-background-primary: var(--global-color-white);
  /* --color-background-disabled: var(--global-color-gray-100);*/
  --color-background-disabled: #d1d3d8;

  --color-surface-interactive-default: var(--global-color-pink-500);
  --color-surface-interactive-hover: var(--global-color-pink-700);
  --color-surface-interactive-active: var(--global-color-pink-700);
  --color-surface-disabled: var(--global-color-gray-200);
  --color-surface-subtle-hover: var(--global-color-gray-100);
  --color-border-default: var(--global-color-gray-300);
  --color-border-subtle: var(--global-color-gray-200);
  --color-border-interactive: var(--global-color-pink-500);
  --color-border-focus: var(--global-color-pink-500);
  --color-border-error: var(--global-color-red-500);
  --color-background-error: var(--global-color-red-500);
  /* Semantic Border Colors */
  --color-border-warning: var(--global-color-yellow-500);
  --color-border-success: var(--global-color-green-500);
  --color-border-info: var(--global-color-blue-info-500);

  /* Semantic Text Colors */
  --color-text-warning: var(--global-color-yellow-600);
  --color-text-success: var(--global-color-green-600);
  --color-text-error: var(--global-color-red-600);
  --color-text-info: var(--global-color-blue-info-500);

  /* Semantic Background Colors */
  --color-background-warning: var(--global-color-yellow-500);
  --color-background-success: var(--global-color-green-500);
  --color-background-info: var(--global-color-blue-info-500);

  /* --- Light Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.6);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /*---- Time picker ----*/
  --color-icon-border: var(--global-color-black);

  /*---------header icon color--------*/
  --color-icon: var(--global-color-black);

  /* =======================
     LIGHT THEME: RGB OVERRIDES
     Extract RGB values from light theme semantic colors
     ======================= */
  --rgb-brand-primary: 233, 30, 99;
  /* From #e91e63 */
  --rgb-brand-secondary: 156, 39, 176;
  /* From #9c27b0 */
  --rgb-brand-tertiary: 37, 99, 235;
  /* From #2563eb */
  --rgb-brand-quaternary: 3, 189, 212;
  /* From #03bdd4 */
  --rgb-brand-quinary: 67, 189, 144;
  /* From #43bd90 */
  --rgb-brand-senary: 250, 112, 154;
  /* From #fa709a */
  --rgb-violet: 124, 58, 237;
  /* From #7c3aed */
  --rgb-royal-blue: 37, 99, 235;
  /* From #2563eb */
  --rgb-cyan: 3, 189, 212;
  /* From #03bdd4 */
  --rgb-spearmint: 67, 189, 144;
  /* From #43bd90 */
  --rgb-rose: 250, 112, 154;
  /* From #fa709a */
  --rgb-white: 255, 255, 255;
  /* From #ffffff */
  --rgb-black: 0, 0, 0;
  /* From #000000 */
  --rgb-neutral-100: 243, 244, 246;
  /* From #f3f4f6 */

  /* =======================
     LIGHT THEME: EFFECT COLOR OVERRIDES
     Override all effect colors for proper light theme adaptation
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  /* Pink - consistent with light theme */
  --effect-color-secondary: var(--rgb-brand-secondary);
  /* Blue instead of purple in light */
  --effect-color-accent: var(--rgb-violet);
  /* Violet accent - keep consistent */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work well on light bg */
  --effect-color-surface: var(--rgb-white);
  --color-login-background: rgb(255, 255, 255, 0.4);
  /* White glass/shimmer on light bg */

  /* =======================
     LIGHT THEME: PERSONALITY OVERRIDES
     Only override personality tokens that need light theme adjustments
     ======================= */
  /* Most personalities use base values, only override if light theme needs different intensity */
  /* Base personalities work well for light theme, no overrides needed currently */

  /* =======================
     LIGHT THEME: SEMANTIC COMPONENT TOKENS
     Theme-aware component-specific tokens using the metaphor system
     ======================= */

  /* Glass Metaphor (Theme-Aware) */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor (Theme-Aware) */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor (Theme-Aware) */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* =======================
     LIGHT THEME: SOPHISTICATED GLASS CHAINING
     Override glass surface colors for light theme variants
     ======================= */

  /* Light theme glass surface - keep default white */
  --glass-surface-color: var(--rgb-white);

  /* Light theme variant glass colors */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: 76, 175, 80;
  --glass-variant-warning: 255, 152, 0;
  --glass-variant-danger: 244, 67, 54;
  --glass-variant-info: 33, 150, 243;

  /* Custom variant example - Add new colors here */
  --glass-variant-purple: 156, 39, 176;
  /* Custom purple variant */
  --glass-variant-emerald: 16, 185, 129;
  /* Custom emerald variant */

  /* Light theme effect color adjustments */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White highlights on light bg */

  --neutral-bg: #f0f1f2;
}

[data-theme='dark'] {
  /* --- Dark Theme Color Overrides --- */
  /* PRIMARY (Lighter Pink for dark backgrounds) */
  --color-brand-primary: var(--global-color-pink-300);
  --color-brand-primary-hover: var(--global-color-pink-200);
  --color-brand-primary-active: var(--global-color-deep-purple-300);
  --color-surface-interactive-primary: var(--global-color-pink-300);
  --color-surface-interactive-primary-hover: var(--global-color-pink-200);
  --color-surface-interactive-primary-active: var(--global-color-deep-purple-300);
  --color-border-primary: var(--global-color-pink-300);
  --color-border-primary-hover: var(--global-color-pink-200);
  --color-border-primary-active: var(--global-color-deep-purple-300);
  --color-text-primary: var(--global-color-gray-100);
  --color-text-on-primary: var(--global-color-white);
  --color-text-inactive-tab-button: var(--global-color-gray-300);
  --color-text-active-stepper-circle: var(--global-color-gray-100);
  --color-text-accordion-content: var(--global-color-gray-100);
  --color-textbox-input: var(--global-color-gray-700);
  --textbox-surface-primary: #1e1e1ecc;
  /* SECONDARY (Lighter Blue for dark backgrounds) */
  --color-brand-secondary: var(--global-color-blue-300);
  --color-brand-secondary-hover: var(--global-color-royal-blue-300);
  --color-brand-secondary-active: var(--global-color-royal-blue-400);
  --color-surface-interactive-secondary: var(--global-color-blue-800);
  --color-surface-interactive-secondary-hover: var(--global-color-blue-300);
  --color-surface-interactive-secondary-active: var(--global-color-royal-blue-300);
  --color-border-secondary: var(--global-color-blue-300);
  --color-border-secondary-hover: var(--global-color-royal-blue-300);
  --color-border-secondary-active: var(--global-color-royal-blue-400);
  --color-text-secondary: var(--global-color-royal-blue-300);
  --color-text-on-secondary: var(--global-color-gray-900);
  --color-background-secondary: var(--global-color-blue-800);

  /* BUTTONS, TABS, TAGS: Use these tokens for all secondary/primary states */
  --color-text-placeholder: var(--global-color-gray-500);
  --color-text-disabled: var(--global-color-gray-600);
  --color-text-on-brand: var(--global-color-gray-900);
  --color-text-interactive: var(--global-color-pink-300);
  --color-text-interactive-hover: var(--global-color-pink-200);
  --color-text-success: var(--global-color-spearmint-300);
  --color-text-error: var(--global-color-rose-300);
  --color-background-primary: var(--global-color-gray-900);
  --color-background-disabled: var(--global-color-gray-800);
  --color-surface-interactive-default: var(--global-color-pink-300);
  --color-surface-interactive-hover: var(--global-color-pink-200);
  --color-surface-interactive-active: var(--global-color-pink-200);
  --color-surface-disabled: var(--global-color-gray-700);
  --color-surface-subtle-hover: var(--global-color-gray-800);
  --color-border-default: var(--global-color-gray-600);
  --color-border-subtle: var(--global-color-gray-700);
  --color-border-interactive: var(--global-color-pink-300);
  --color-border-focus: var(--global-color-pink-300);
  --color-border-error: var(--global-color-rose-300);
  --color-background-error: var(--global-color-rose-300);

  /* Semantic Border Colors */
  --color-border-warning: var(--global-color-amber-300);
  --color-border-success: var(--global-color-spearmint-300);
  --color-border-info: var(--global-color-blue-300);
  --color-login-background: rgb(0, 0, 0, 0.4);

  /* Semantic Text Colors */
  --color-text-warning: var(--global-color-amber-300);
  --color-text-success: var(--global-color-spearmint-300);
  --color-text-info: var(--global-color-blue-300);

  /* Semantic Background Colors */
  --color-background-warning: var(--global-color-amber-300);
  --color-background-success: var(--global-color-spearmint-300);
  --color-background-info: var(--global-color-blue-300);

  /* --- Dark Theme Glassmorphism --- */
  --glass-backdrop-blur: 16px;
  --glass-background-color: rgba(0, 0, 0, 0.4);
  --glass-border-color: rgba(255, 255, 255, 0.1);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-03);

  /*---- Time picker ----*/
  --color-icon-border: var(--global-color-white);

  /*---------header icon color--------*/
  --color-icon: var(--global-color-white);

  /* =======================
      DARK THEME: RGB OVERRIDES
      Extract RGB values from dark theme semantic colors (lighter shades)
      ======================= */
  --rgb-brand-primary: 240, 98, 146;
  /* From pink-300 #f06292 */
  --rgb-brand-secondary: 149, 117, 205;
  /* From deep-purple-300 #9575cd */
  --rgb-brand-tertiary: 100, 181, 246;
  /* From blue-300 #64b5f6 */
  --rgb-brand-quaternary: 103, 232, 249;
  /* From cyan-300 #67e8f9 */
  --rgb-brand-quinary: 94, 234, 212;
  /* From spearmint-300 #5eead4 */
  --rgb-brand-senary: 253, 164, 175;
  /* From rose-300 #fda4af */
  --rgb-violet: 169, 149, 255;
  /* From violet-300 #a995ff */
  --rgb-royal-blue: 147, 197, 253;
  /* From royal-blue-300 #93c5fd */
  --rgb-cyan: 103, 232, 249;
  /* From cyan-300 #67e8f9 */
  --rgb-spearmint: 94, 234, 212;
  /* From spearmint-300 #5eead4 */
  --rgb-rose: 253, 164, 175;
  /* From rose-300 #fda4af */
  --rgb-white: 255, 255, 255;
  /* From #ffffff */
  --rgb-black: 0, 0, 0;
  /* From #000000 */
  --rgb-neutral-100: 17, 24, 39;
  /* Dark background equivalent */

  /* =======================
      DARK THEME: EFFECT COLOR OVERRIDES
      Override all effect colors for proper dark theme adaptation
      ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  /* Pink 300 - lighter for dark bg */
  --effect-color-secondary: var(--rgb-brand-secondary);
  /* Deep purple 300 - lighter for dark bg */
  --effect-color-accent: var(--rgb-violet);
  /* Violet 300 - lighter accent */
  --effect-color-neutral: var(--rgb-white);
  /* White shadows/effects on dark bg */
  --effect-color-surface: var(--rgb-black);
  /* Black glass/shimmer on dark bg */

  /* =======================
      DARK THEME: SEMANTIC COMPONENT TOKENS
      Theme-aware component-specific tokens using the metaphor system
      ======================= */

  /* Glass Metaphor (Dark Theme-Aware) */
  --surface-glass-bg: rgba(var(--effect-color-neutral), 0.08);
  --surface-glass-border: rgba(var(--effect-color-neutral), 0.12);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.25);

  /* Light Metaphor (Dark Theme-Aware) */
  --color-light-glow: rgba(var(--effect-color-primary), 0.35);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.55);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.15);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor (Dark Theme-Aware) */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* =======================
      DARK THEME: SOPHISTICATED GLASS CHAINING
      Override glass surface colors for dark theme variants
      ======================= */

  /* Dark theme glass surface - use dark background */
  --glass-surface-color: var(--rgb-black);

  /* Dark theme variant glass colors (lighter shades) */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: 94, 234, 212;
  /* spearmint-300 */
  --glass-variant-warning: 252, 211, 77;
  /* amber-300 */
  --glass-variant-danger: 253, 164, 175;
  /* rose-300 */
  --glass-variant-info: 100, 181, 246;
  /* blue-300 */

  /* Custom variant example - Lighter versions for dark theme */
  --glass-variant-purple: 149, 117, 205;
  /* deep-purple-300 */
  --glass-variant-emerald: 94, 234, 212;
  /* spearmint-300 */

  /* Dark theme effect color adjustments */
  --effect-color-neutral: var(--rgb-white);
  /* White highlights work on dark bg */
  --effect-color-surface: var(--rgb-black);
  /* Black depths on dark bg */

  /* =======================
      DARK THEME: ADDITIONAL OVERRIDES
      Additional tokens that need adjustment for dark theme
      ======================= */

  /* Text contrast adjustments */
  --color-text-muted: var(--global-color-gray-400);
  --color-text-subtle: var(--global-color-gray-500);
  --color-text-inverse: var(--global-color-gray-900);

  /* Surface variations for dark theme */
  --color-surface-elevated: var(--global-color-gray-800);
  --color-surface-sunken: var(--global-color-gray-900);
  --color-surface-overlay: rgba(0, 0, 0, 0.8);

  /* Input and form controls */
  --color-input-background: var(--global-color-gray-800);
  --color-input-border: var(--global-color-gray-600);
  --color-input-border-focus: var(--global-color-pink-300);
  --color-input-text: var(--global-color-gray-100);

  /* Navigation and menu items */
  --color-nav-item-hover: rgba(var(--rgb-brand-primary), 0.1);
  --color-nav-item-active: rgba(var(--rgb-brand-primary), 0.2);

  /* Dividers and separators */
  --color-divider: var(--global-color-gray-700);
  --color-divider-subtle: var(--global-color-gray-800);
}
