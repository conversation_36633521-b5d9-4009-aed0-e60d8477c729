import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  ViewChild,
  ViewContainerRef,
  inject,
  DestroyRef,
  ChangeDetectionStrategy,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AuthConfig, AuthService, CentralizedRedirectService, TokenStorageService } from '@shared';

// import { TokenStorageService } from '@shared/auth/services/token-storage.service';
// import { AuthTokenService } from '@shared/auth/services/auth-token.service';
// import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
// import { AuthService } from '@shared/auth/services/auth.service';
// import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';
import { environment } from '../environments/environment';
// import { SharedAppHeaderComponent, HeaderConfig } from '@shared/components/app-header/app-header.component';
// import { experienceStudioHeaderConfig } from './config/header.config';
import { ThemeService } from './shared/services/theme-service/theme.service';
// import { LenisSmoothScrollService } from './shared/services/lenis-smooth-scroll.service';
// import { getLenisConfigForContext } from './config/lenis.config';
import { createLogger } from './shared/utils/logger';


@Component({
  selector: 'app-root',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterOutlet,
    CommonModule,
    // SharedAppHeaderComponent
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;

  // @ViewChild(SharedAppHeaderComponent)
  // headerComponent!: SharedAppHeaderComponent;

  showHeaderAndNav: boolean = true;
  redirectUrl = '';

  // Angular 19+ Dependency Injection
  private readonly destroyRef = inject(DestroyRef);
  // private readonly lenisSmoothScrollService = inject(LenisSmoothScrollService);
  // private readonly authTokenService = inject(AuthTokenService);
  private readonly tokenStorage = inject(TokenStorageService);
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  public readonly themeService = inject(ThemeService);
  private readonly centralizedRedirectService = inject(CentralizedRedirectService);

  private readonly logger = createLogger('AppComponent');

  // Header configuration
  // headerConfig: HeaderConfig = experienceStudioHeaderConfig;

  ngOnInit(): void {
    // Initialize Lenis smooth scrolling
    // this.initializeLenisScrolling();

    // const savedTheme = this.themeService.getCurrentTheme();
    // this.themeService.setTheme(savedTheme);

    const authConfig: AuthConfig = {
      apiAuthUrl: environment.experianceApiAuthUrl,
      redirectUrl: environment.experianceRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'experience-studio',
    };
    this.authService.setAuthConfig(authConfig);

    // Check authentication status and redirect if needed
    if (!this.checkAuthenticationAndRedirect()) {
      return; // Don't continue if not authenticated
    }

    // this.authTokenService.handleAuthCodeAndToken();
    // this.authTokenService.startTokenCheck();

    // org_path is now set during login, no need to check here
  }

  ngOnDestroy() {
    // this.authTokenService.stopTokenCheck();
    // Cleanup Lenis smooth scrolling
    // this.lenisSmoothScrollService.destroy();
  }

  // Simple authentication check
  private checkAuthenticationAndRedirect(): boolean {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (!accessToken && !refreshToken) {
      // Store current URL and redirect to marketing login
      // this.centralizedRedirectService.storeIntendedDestination(window.location.href);
      this.centralizedRedirectService.redirectToMarketingLogin();
      return false;
    }
    return true;
  }

  // Header event handlers
  onNavigation(route: string): void {
    this.logger.info('Experience Studio Navigation to:', route);
  }
   onLogout() {
    if (this.tokenStorage.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Redirect to centralized marketing login
          this.centralizedRedirectService.redirectToMarketingLogin();
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          this.logger.error('Basic logout failed:', error);
          // Still redirect to marketing login even if logout fails
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
    } else {
      // For SSO logout, redirect to marketing login
      this.centralizedRedirectService.redirectToMarketingLogin();
    }
  }

  onProfileAction(action: string): void {
    this.logger.info('Profile action:', action);
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }

  /**
   * Enable logo animation programmatically
   * Can be called from UI components or services
   */
  // public enableHeaderLogoAnimation(): void {
  //   if (this.headerComponent) {
  //     this.headerComponent.enableLogoAnimation();
  //     this.logger.info('✅ Experience Studio: Logo animation enabled');
  //   }
  // }

  /**
   * Disable logo animation programmatically
   */
  // public disableHeaderLogoAnimation(): void {
  //   if (this.headerComponent) {
  //     this.headerComponent.disableLogoAnimation();
  //     this.logger.info('🔇 Experience Studio: Logo animation disabled');
  //   }
  // }

  /**
   * Toggle logo animation on/off
   */
  // public toggleHeaderLogoAnimation(): void {
  //   if (this.headerComponent) {
  //     this.headerComponent.toggleLogoAnimation();
  //     const isEnabled = this.headerComponent.isLogoAnimationEnabled();
  //     this.logger.info(`🔄 Experience Studio: Logo animation ${isEnabled ? 'enabled' : 'disabled'}`);
  //   }
  // }

  /**
   * Initialize Lenis smooth scrolling with optimized configuration
   */
  // private initializeLenisScrolling(): void {
  //   try {
  //     // Get context-aware configuration
  //     const lenisConfig = getLenisConfigForContext();

  //     // Initialize Lenis with the configuration
  //     this.lenisSmoothScrollService.initialize(lenisConfig);

  //     this.logger.info('✅ Experience Studio: Lenis smooth scrolling initialized');
  //   } catch (error) {
  //     this.logger.error('❌ Experience Studio: Failed to initialize Lenis smooth scrolling:', error);
  //   }
  // }
}
