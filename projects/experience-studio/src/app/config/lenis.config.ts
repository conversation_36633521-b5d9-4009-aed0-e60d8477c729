// // import { LenisConfig } from '../shared/services/lenis-smooth-scroll.service';

// /**
//  * Optimized easing functions for fast and smooth scrolling
//  */
// export const LenisEasingFunctions = {
//   // Fast and smooth quartic ease-out
//   quartOut: (t: number): number => 1 - Math.pow(1 - t, 4),

//   // Performance cubic ease-out
//   cubicOut: (t: number): number => 1 - Math.pow(1 - t, 3),

//   // Fast quadratic ease-out for maximum performance
//   quadOut: (t: number): number => 1 - Math.pow(1 - t, 2),

//   // Ultra-smooth quintic ease-out
//   quintOut: (t: number): number => 1 - Math.pow(1 - t, 5),

//   // Maximum smoothness sextic ease-out
//   sextOut: (t: number): number => 1 - Math.pow(1 - t, 6),

//   // Fast and smooth premium curve
//   premiumSmooth: (t: number): number => {
//     // Optimized blend for speed and smoothness
//     const quartic = 1 - Math.pow(1 - t, 4);
//     const exponential = 1 - Math.exp(-4 * t);
//     return quartic * 0.8 + exponential * 0.2;
//   },

//   // Fast responsive curve with smooth deceleration
//   fastSmooth: (t: number): number => {
//     // Quick start with smooth finish
//     return t < 0.3
//       ? 2.5 * t * t
//       : 1 - Math.pow(-1.5 * t + 1.5, 2) / 2;
//   },

//   // Luxury curve with extended deceleration
//   luxuryDecel: (t: number): number => {
//     // Extended deceleration for premium feel
//     return t < 0.5
//       ? 4 * t * t * t
//       : 1 - Math.pow(-2 * t + 2, 3) / 2;
//   }
// } as const;

// /**
//  * Lenis Smooth Scroll Configuration for Experience Studio
//  *
//  * Optimized settings for the Experience Studio application with consideration for:
//  * - Chat interfaces with custom scroll behavior
//  * - Code editors (Monaco) that need native scrolling
//  * - Landing page smooth scrolling
//  * - Performance optimization
//  * - Mobile responsiveness
//  */

// /**
//  * Main Lenis configuration for Experience Studio
//  *
//  * Optimized for fast and smooth scrolling experience:
//  * - lerp: 0.1 (fast interpolation for responsive feel)
//  * - duration: 1.0s (balanced scrolling duration)
//  * - wheelMultiplier: 1.0 (standard responsive control)
//  * - easing: quartOut (fast and smooth curve)
//  */
// export const experienceStudioLenisConfig: LenisConfig = {
//   enabled: true,

//   // Fast and smooth scrolling parameters - optimized for speed and smoothness
//   lerp: 0.1, // Faster interpolation for responsive feel
//   duration: 1.0, // Balanced duration for quick yet smooth scrolling

//   // Optimized easing function for fast and smooth experience
//   easing: LenisEasingFunctions.quartOut,

//   // Scroll orientation and gestures
//   orientation: 'vertical',
//   gestureOrientation: 'vertical',

//   // Enhanced wheel and touch settings for faster response
//   smoothWheel: true,
//   wheelMultiplier: 1.0, // Standard sensitivity for responsive scrolling
//   touchMultiplier: 1.3, // Enhanced touch sensitivity for mobile

//   // Advanced settings
//   infinite: false, // No infinite scroll for better UX
//   autoResize: true, // Automatic resize handling
//   overscroll: true, // Allow overscroll behavior
//   autoRaf: false, // Manual RAF control for performance

//   // Prevent smooth scrolling on specific elements
//   prevent: (node: Element) => {
//     // Check for data attributes
//     if (node.hasAttribute('data-lenis-prevent') ||
//         node.hasAttribute('data-lenis-prevent-wheel') ||
//         node.hasAttribute('data-lenis-prevent-touch')) {
//       return true;
//     }

//     // Preserve native scrolling for chat components
//     if (node.classList.contains('chat-messages') ||
//         node.classList.contains('chat-scroll-container') ||
//         node.closest('.chat-messages') ||
//         node.closest('.chat-scroll-container')) {
//       return true;
//     }

//     // Preserve native scrolling for prompt bar components
//     if (node.classList.contains('prompt-text') ||
//         node.classList.contains('prompt-bar') ||
//         node.closest('.prompt-text') ||
//         node.closest('.prompt-bar')) {
//       return true;
//     }

//     // Preserve native scrolling for code editors
//     if (node.classList.contains('monaco-editor') ||
//         node.classList.contains('code-content') ||
//         node.classList.contains('line-numbers') ||
//         node.closest('.monaco-editor') ||
//         node.closest('.code-content')) {
//       return true;
//     }

//     // Preserve native scrolling for specific UI components
//     if (node.classList.contains('dropdown-menu') ||
//         node.classList.contains('modal-body') ||
//         node.classList.contains('tooltip') ||
//         node.classList.contains('popover') ||
//         node.closest('.dropdown-menu') ||
//         node.closest('.modal-body')) {
//       return true;
//     }

//     // Preserve scrolling for viewport debug components
//     if (node.classList.contains('viewport-debug') ||
//         node.closest('.viewport-debug')) {
//       return true;
//     }

//     return false;
//   },

//   // Virtual scroll customization
//   virtualScroll: (e: any): boolean => {
//     // Allow normal scrolling behavior for prevented elements
//     if (e.event) {
//       // Skip smooth scrolling if shift key is pressed (for precision scrolling)
//       if (e.event.shiftKey) {
//         return false;
//       }

//       // Reduce scroll speed when ctrl/cmd key is pressed (for zoom-like behavior)
//       if (e.event.ctrlKey || e.event.metaKey) {
//         e.deltaY *= 0.3;
//         e.deltaX *= 0.3;
//       }
//     }

//     return true;
//   }
// };

// /**
//  * Fast and ultra-smooth configuration for high-end devices
//  */
// export const experienceStudioLenisUltraSmoothConfig: LenisConfig = {
//   ...experienceStudioLenisConfig,
//   lerp: 0.08, // Faster interpolation while maintaining smoothness
//   duration: 1.2, // Quick yet elegant duration
//   wheelMultiplier: 1.2, // Enhanced wheel responsiveness
//   touchMultiplier: 1.5, // Improved touch sensitivity
//   easing: LenisEasingFunctions.premiumSmooth
// };

// /**
//  * Fast performance configuration for low-end devices
//  */
// export const experienceStudioLenisPerformanceConfig: LenisConfig = {
//   ...experienceStudioLenisConfig,
//   lerp: 0.15, // Fast interpolation for snappy performance
//   duration: 0.7, // Quick duration for immediate response
//   wheelMultiplier: 1.1, // Enhanced wheel responsiveness
//   touchMultiplier: 1.2, // Improved touch sensitivity
//   easing: LenisEasingFunctions.cubicOut
// };

// /**
//  * Fast mobile configuration - optimized for touch responsiveness
//  */
// export const experienceStudioLenisMobileConfig: LenisConfig = {
//   ...experienceStudioLenisConfig,
//   lerp: 0.12, // Faster interpolation for mobile responsiveness
//   duration: 0.9, // Quick duration for mobile attention spans
//   wheelMultiplier: 1.0, // Standard for mobile (though less relevant)
//   touchMultiplier: 1.6, // Enhanced touch sensitivity for fast scrolling
//   gestureOrientation: 'vertical', // Strict vertical scrolling on mobile
//   easing: LenisEasingFunctions.quartOut
// };

// /**
//  * Utility function to get appropriate config based on device/context
//  */
// export function getLenisConfigForContext(): LenisConfig {
//   // Detect mobile devices
//   const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

//   // Detect performance preference
//   const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

//   if (prefersReducedMotion) {
//     // Disable smooth scrolling for users who prefer reduced motion
//     return {
//       ...experienceStudioLenisConfig,
//       enabled: false
//     };
//   }

//   if (isMobile) {
//     return experienceStudioLenisMobileConfig;
//   }

//   // Enhanced device capability detection
//   const hardwareConcurrency = navigator.hardwareConcurrency || 4;
//   const deviceMemory = (navigator as any).deviceMemory || 4;
//   const connection = (navigator as any).connection;

//   // Check for low-end devices
//   const isLowEndDevice = hardwareConcurrency <= 2 || deviceMemory <= 2;

//   // Check for slow network (affects perceived performance)
//   const isSlowNetwork = connection && (
//     connection.effectiveType === 'slow-2g' ||
//     connection.effectiveType === '2g' ||
//     connection.saveData
//   );

//   if (isLowEndDevice || isSlowNetwork) {
//     return experienceStudioLenisPerformanceConfig;
//   }

//   // Check for high-end desktop devices for ultra-smooth experience
//   const isHighEndDesktop = hardwareConcurrency >= 8 && deviceMemory >= 8 && !isMobile;

//   if (isHighEndDesktop) {
//     return experienceStudioLenisUltraSmoothConfig;
//   }

//   // Default to standard smooth configuration
//   return experienceStudioLenisConfig;
// }

// /**
//  * CSS classes that should be added to elements that need to prevent Lenis scrolling
//  */
// export const LENIS_PREVENT_CLASSES = [
//   'lenis-prevent',
//   'chat-messages',
//   'chat-scroll-container',
//   'prompt-text',
//   'prompt-bar',
//   'monaco-editor',
//   'code-content',
//   'line-numbers',
//   'dropdown-menu',
//   'modal-body',
//   'tooltip',
//   'popover',
//   'viewport-debug'
// ] as const;

// /**
//  * Data attributes for preventing Lenis scrolling
//  */
// export const LENIS_PREVENT_ATTRIBUTES = [
//   'data-lenis-prevent',
//   'data-lenis-prevent-wheel',
//   'data-lenis-prevent-touch'
// ] as const;
