import { Buttons, FileAttachOption, IconOption } from '../models/image-to-code.model';

export const promptContentConstants = {
  acceptedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  acceptedDocumentTypes: ['text/plain'],

  // Prefilled prompt text for wireframe generation when files are uploaded
  wireframePrefilledPrompt: 'Create a modern, responsive wireframe design based on the uploaded content. Focus on user experience, clean layout.',

  // Prefilled prompt text for prompt-to-application generation when documents are uploaded
  promptToApplicationPrefilledPrompt: 'Create a responsive web app by carefully analyzing and implementing the features and requirements detailed in the uploaded document.',
  techOptions: [
    { name: 'React', icon: 'assets/icons/awe_react.svg', value: 'react', isLocalSvg: true },
    // {
    //   name: 'Angular (Coming Soon)',
    //   icon: 'assets/icons/awe_angular.svg',
    //   value: 'angular',
    //   isLocalSvg: true,
    //   disabled: true,
    // },
    // {
    //   name: 'Vue (Coming Soon)',
    //   icon: 'assets/icons/awe_vue.svg',
    //   value: 'vue',
    //   isLocalSvg: true,
    //   disabled: true,
    // },
  ],
  animatedTexts: [
    'a web app that..',
    'an internal tool that..',
    'an e-commerce site with..',
    'a portfolio website for my..',
    'a booking system for..',
    'a social media platform for..',
    'a customer support portal with..',
    'a real-time analytics dashboard for..',
    'a landing page for my..',
    'a dashboard to..',
  ],
  // Animated texts to show when an image is uploaded
  imageUploadAnimatedTexts: [
    'an application based on this image..',
    'a web app that looks like this image..',
    'a UI similar to this design..',
    'a responsive interface based on this mockup..',
    'a functional app from this wireframe..',
    'code that implements this design..',
    'a working prototype of this UI..',
    'an interactive version of this mockup..',
    'a component that matches this design..',
    'a pixel-perfect implementation of this image..',
  ],
};
// Dummy creative prompt options for testing/demo
const dummyPromptLabels: Buttons[] = [
  {
    label: '✨Build a meditation timer app with relaxing sounds',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a pet adoption portal with photo gallery',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a language learning app with flashcards',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Generate a virtual event platform with live chat',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a personal journal app with mood tracking',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a quiz app with leaderboard and badges',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a travel itinerary planner with maps',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a music playlist manager with drag-and-drop',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a digital recipe box with shopping list',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a book club app with discussion threads',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

// Extended list of creative button label options
const allButtonLabels: Buttons[] = [
  {
    label: '✨Create a personal budgeting app with spending insights',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a virtual classroom with video and quizzes',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a smart home dashboard for IoT devices',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a team task tracker with kanban boards',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Generate a digital art portfolio with gallery view',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a language exchange app with chat rooms',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a conference schedule app with speaker bios',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a mindfulness journal with daily prompts',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Generate a travel expense splitter for groups',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a plant care tracker with watering reminders',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a food delivery app with a modern user-friendly UI',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create an eCommerce dashboard using provided layout',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a Spotify clone with a Doraemon-inspired color theme',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a project management tool for remote teams',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Generate a personal finance tracker with charts',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a travel booking site with interactive maps',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a fitness app with workout and diet plans',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a blogging platform with markdown support',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a weather dashboard with animated icons',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Generate a movie review site with star ratings',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a recipe app with ingredient search',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a collaborative whiteboard for brainstorming',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

// Utility to get N random unique items from an array
function getRandomUniqueItems<T>(arr: T[], n: number): T[] {
  const shuffled = arr.slice().sort(() => 0.5 - Math.random());
  return shuffled.slice(0, n);
}

// Export only 3 random dummy prompts for demo/testing
export const dummyButtonLabels: Buttons[] = getRandomUniqueItems(dummyPromptLabels, 3);

// Export only 3 random button labels each time this file is loaded
export const buttonLabels: Buttons[] = getRandomUniqueItems(allButtonLabels, 3);

export const iconOptions: IconOption[] = [
  // { name: 'Angular', icon: 'awe_modules', value: 'angular' },
  { name: 'React', icon: 'awe_react', value: 'react' },
  // { name: 'Vue', icon: 'awe_toggled_button', value: 'vue' },
  // { name: 'Vue', icon: 'awe_toggled_button', value: 'vue' },
];

export const designOptions: IconOption[] = [
  {
    name: 'Tailwind',
    icon: 'assets/icons/awe_tailwind.svg',
    value: 'tailwind',
    isLocalSvg: true,
  },
  // {
  //   name: 'Material UI (Coming Soon)',
  //   icon: 'assets/icons/awe_material.svg',
  //   value: 'material',
  //   isLocalSvg: true,
  //   disabled: true,
  // },
  // {
  //   name: 'Bootstrap (Coming Soon)',
  //   icon: 'assets/icons/awe_bootstrap.svg',
  //   value: 'bootstrap',
  //   isLocalSvg: true,
  //   disabled: true,
  // },
];

export const fileOptions: FileAttachOption[] = [
  { name: 'Upload image (JPEG, PNG, GIF, WEBP, SVG)\nMax size: 5MB\n from Computer', icon: 'awe_upload', value: 'computer' },
  // { name: 'Attach file from Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
];

// File options for Image to Application flow (mandatory image upload)
export const imageToApplicationFileOptions: FileAttachOption[] = [
  { name: 'Upload image (JPEG, PNG, GIF, WEBP, SVG)\nMax size: 5MB\n from Computer', icon: 'awe_upload', value: 'computer' },
];

// File options for Prompt to Application flow (optional document upload)
export const promptToApplicationFileOptions: FileAttachOption[] = [
  { name: 'Upload (.txt) From Computer', icon: 'awe_upload', value: 'computer' },
];

// File options for Generate Wireframes flow (accepts both images and .txt files)
export const generateWireframesFileOptions: FileAttachOption[] = [
  { name: 'Upload Ref Image (JPEG, PNG, GIF, WEBP, SVG)\nMax size: 5MB, docs(.txt) from Computer', icon: 'awe_upload', value: 'computer' },
];
