// @use '../../../../../../../public/assets/styles/mixins' as mixins;

@use '../../../../../../public/assets//styles//mixins' as mixins;

// ENHANCEMENT: Image upload field theme mixin for consistent theming
@mixin image-upload-field-theme() {
  .selected-files {
    .file-item {
      // Use theme transition for smooth theme switching
      transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;

      // Light theme styles (default)
      background-color: var(--file-item-bg);
      border-color: var(--file-item-border);
      box-shadow: 0 2px 4px var(--file-item-shadow);

      &:hover {
        background-color: var(--file-item-hover-bg);
        border-color: var(--file-item-hover-border);
        box-shadow: 0 4px 8px var(--file-item-hover-shadow);
      }

      .file-preview {
        .file-preview-image {
          border-color: var(--file-preview-image-border);
          transition: transform 0.2s ease, border-color 0.3s ease;
        }

        .file-name {
          color: var(--file-name-color);
          transition: color 0.3s ease;
        }
      }
    }
  }
}

// Apply theme mixin at component level for both light and dark themes
:host {
  @include image-upload-field-theme();
}

// Dark theme specific adjustments using host-context pattern
:host-context(.dark-theme) {
  .selected-files .file-item {
    // Enhanced dark theme styling with better contrast
    background-color: var(--file-item-bg) !important;
    border-color: var(--file-item-border) !important;
    box-shadow: 0 2px 6px var(--file-item-shadow) !important;

    &:hover {
      background-color: var(--file-item-hover-bg) !important;
      border-color: var(--file-item-hover-border) !important;
      box-shadow: 0 4px 12px var(--file-item-hover-shadow) !important;
    }

    .file-preview {
      .file-preview-image {
        border-color: var(--file-preview-image-border) !important;
      }

      .file-name {
        color: var(--file-name-color) !important;
      }
    }
  }
}
#prompt-content-container {
  margin-top: 7%;
  margin-bottom: 7%; //todo remove when suggestions are added
  #suggestions-container {
    .invisible {
      visibility: hidden !important;
    }
  }

  #divider-section-container {
    padding-top: 4rem;
    .divider-image {
      width: 100%;
      @media (max-width: 767px) {
        max-width: 90%;
      }
    }
  }
}

.disabled-prompt-bar {
  @include mixins.disabledProperty(0.7, none, not-allowed);
  ::ng-deep textarea {
    @include mixins.disabledProperty(0.7, none, not-allowed);
  }
}

.custom-content {
  exp-file-attach-pill.disabled {
    @include mixins.disabledProperty(0.5, none, not-allowed);
  }

  // Enhanced disabled state for UI Design mode
  exp-file-attach-pill.ui-design-disabled {
    @include mixins.disabledProperty(0.6, none, not-allowed);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg,
        rgba(255, 193, 7, 0.1),
        rgba(255, 193, 7, 0.05));
      border-radius: inherit;
      pointer-events: none;
      z-index: 1;
    }

    // Add subtle animation to indicate in-progress state
    &::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      right: 2px;
      bottom: 2px;
      border: 1px solid rgba(255, 193, 7, 0.3);
      border-radius: inherit;
      animation: uiDesignPulse 2s ease-in-out infinite;
      pointer-events: none;
      z-index: 1;
    }
  }
  .selected-files {
    width: 100%;
    .file-item {
      // ENHANCEMENT: Improved theme compatibility for image upload field
      background-color: var(--file-item-bg, var(--preview-page-bg-color, #ffffff));
      border: 1px solid var(--file-item-border, var(--code-viewer-search-border, #e0e0e0));
      border-radius: 8px;
      max-width: 200px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2px 4px var(--file-item-shadow, rgba(0, 0, 0, 0.1));

      // Apply theme mixin for consistent theming
      @include image-upload-field-theme();

      &:hover {
        background-color: var(--file-item-hover-bg, var(--card-hover-bg, #f5f5f5));
        border-color: var(--file-item-hover-border, var(--selected-card-border, #3f51b5));
        box-shadow: 0 4px 8px var(--file-item-hover-shadow, rgba(0, 0, 0, 0.15));
      }

      .file-preview {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        flex: 1;

        .file-preview-image {
          width: 24px;
          height: 24px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid var(--file-preview-image-border, rgba(0, 0, 0, 0.1));

          &:hover {
            transform: scale(1.1);
          }
        }

        // Text file preview styling
        &.text-file-preview {
          cursor: default; // No click action for text files

          exp-icons {
            font-size: 20px;
            color: var(--primary-color, #3f51b5);
          }
        }

        .file-name {
          color: var(--file-name-color, var(--body-text-color, #000000));
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }
      }

      // Close button styling
      exp-icons {
        flex-shrink: 0;
        opacity: 0.7;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .enhance-icons {
    justify-content: flex-end;
    flex-shrink: 0;
    exp-icons {
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      font-size: 24px;
      color: var(--icon-enabled-color) !important;
      &.disabled {
        @include mixins.disabledProperty(0.4, none, not-allowed);
        color: var(--icon-disabled-color) !important;
      }
    }
    .loading-spinner {
      min-width: auto;
      width: 24px;
      height: 24px;
      .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--icon-enabled-color);
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    // Enhanced loading container for contextual text
    .enhance-loading-container {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: auto;

      .enhance-loading-text {
        font-size: 12px;
        font-weight: 500;
        color: var(--color-text-secondary, #666);
        white-space: nowrap;
        position: relative;
        overflow: hidden;
        background: linear-gradient(
          90deg,
          var(--shimmer-base-color, #666) 0%,
          var(--shimmer-base-color, #666) 35%,
          var(--shimmer-highlight-color, #999) 45%,
          var(--shimmer-peak-color, #bbb) 50%,
          var(--shimmer-highlight-color, #999) 55%,
          var(--shimmer-base-color, #666) 65%,
          var(--shimmer-base-color, #666) 100%
        );
        background-size: 300% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: shimmer-effect 2s ease-in-out infinite;
        transition: opacity 0.3s ease;
      }

      .loading-spinner {
        min-width: auto;
        width: 24px;
        height: 24px;
      }
    }
  }
}
.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-in-out;
  backdrop-filter: blur(10px);
  z-index: 9999;

  exp-icons {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
    display: flex;
    ::ng-deep path {
      fill: white !important;
      stroke: white !important;
    }
  }

  img {
    max-width: 90vw;
    max-height: 90vh;
    object-fit: contain;
    animation: fadeIn 0.3s ease-in-out;
  }
}

:host ::ng-deep button.secondary {
  border: 1px solid var(--button-border-color);
  color: var(--button-text-color);
  border-radius: 0.5rem;
  background: var(--prompt-bar-suggestion-button-bg);
  &:hover {
    border-color: var(--prompt-bar-hover-color);
  }
}

:host {
  ::ng-deep {
    .prompt-bar {
      &.dark {
        @include mixins.prompt-bar-style(none, promptBarBreatheDark, rgba(255, 92, 163, 1));
      }
      &.light {
        @include mixins.prompt-bar-style(
          var(--Light---60, rgba(240, 240, 245, 0.5)),
          promptBarBreatheLight,
          rgba(66, 68, 194, 0.8)
        );
      }
    }
  }
}

:host ::ng-deep .prompt-bar.disabled {
  @include mixins.disabledProperty(0.7, none, not-allowed);
  border-color: var(--prompt-bar-disabled-border, #ccc) !important;
  background-color: var(--prompt-bar-disabled-bg, rgba(240, 240, 245, 0.3)) !important;
  animation: none !important;
}

:host ::ng-deep .prompt-bar.disabled .prompt-text {
  color: var(--prompt-bar-disabled-text, #999) !important;
}

::ng-deep .file-attach-pill {
  min-width: unset !important;
  transition: all 0.4s ease-in-out !important;
  exp-icons path {
    fill: var(--pill-text-color) !important;
  }
}

::ng-deep.icon-pill {
  min-width: unset !important;
  transition: all 0.4s ease-in-out !important;
}

::ng-deep exp-icons path {
  fill: var(--pill-text-color) !important;
}

::ng-deep {
  // .file-attach-pill .icon-wrapper {
  //   margin-top: 8px !important;
  // }
  .file-attach-pill .text,
  .icon-pill .text,
  .dropdown-item .dropdown-item-text {
    color: var(--pill-text-color) !important;
  }
  .dropdown-item .dropdown-item-text {
    width: max-content !important;
  }
  .dropdown-item {
    width: 100% !important;
  }
  .dropdown.show {
    background-color: var(--preview-page-bg-color) !important;
  }
  .dropdown-item:hover,
  .dropdown-item:focus {
    background-color: transparent !important;
  }
}
.tools-container {
  width: 100%;
  align-items: baseline !important;
}
.pills-container {
  flex: 1;
}

// ===== UI DESIGN TOOLTIP STYLES =====

::ng-deep .ui-design-tooltip {
  position: absolute;
  background: var(--tooltip-bg, rgba(0, 0, 0, 0.9));
  color: var(--tooltip-text, #ffffff);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 10000;
  opacity: 0;
  transform: translateY(4px);
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);

  // Tooltip arrow
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
  }

  // Top position arrow
  &.tooltip-top::before {
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px 5px 0 5px;
    border-color: var(--tooltip-bg, rgba(0, 0, 0, 0.9)) transparent transparent transparent;
  }

  // Bottom position arrow
  &.tooltip-bottom::before {
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 5px 5px 5px;
    border-color: transparent transparent var(--tooltip-bg, rgba(0, 0, 0, 0.9)) transparent;
  }

  // Left position arrow
  &.tooltip-left::before {
    right: -5px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px 0 5px 5px;
    border-color: transparent transparent transparent var(--tooltip-bg, rgba(0, 0, 0, 0.9));
  }

  // Right position arrow
  &.tooltip-right::before {
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px 5px 5px 0;
    border-color: transparent var(--tooltip-bg, rgba(0, 0, 0, 0.9)) transparent transparent;
  }

  // Visible state
  &.tooltip-visible {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark theme tooltip styles
:host-context(.dark-theme) ::ng-deep .ui-design-tooltip {
  background: var(--tooltip-bg-dark, rgba(255, 255, 255, 0.95));
  color: var(--tooltip-text-dark, #1a1a1a);

  &.tooltip-top::before {
    border-color: var(--tooltip-bg-dark, rgba(255, 255, 255, 0.95)) transparent transparent transparent;
  }

  &.tooltip-bottom::before {
    border-color: transparent transparent var(--tooltip-bg-dark, rgba(255, 255, 255, 0.95)) transparent;
  }

  &.tooltip-left::before {
    border-color: transparent transparent transparent var(--tooltip-bg-dark, rgba(255, 255, 255, 0.95));
  }

  &.tooltip-right::before {
    border-color: transparent var(--tooltip-bg-dark, rgba(255, 255, 255, 0.95)) transparent transparent;
  }
}

// ===== UI DESIGN ANIMATIONS =====

@keyframes uiDesignPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}
#prompt-bar-container {
  width: 100%;
  ::ng-deep .prompt-bar-wrapper {
    width: 100%;
  }
  ::ng-deep .prompt-input-wrapper {
    width: 100%;
  }
  ::ng-deep .text-input-container {
    width: 100%;
  }
}
// Code Cleanup done till here

// Animation keyframes
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// Shimmer effect keyframes for enhance loading text - enhanced visibility
@keyframes shimmer-effect {
  0% {
    background-position: -300% 0;
  }
  100% {
    background-position: 300% 0;
  }
}

// CSS Custom Properties for shimmer effect
:host {
  // Light theme shimmer colors - enhanced contrast for better visibility
  --shimmer-base-color: #555;
  --shimmer-highlight-color: #888;
  --shimmer-peak-color: #aaa;
}

// Dark theme support for enhanced loading text
:host-context(.dark-theme) {
  // Dark theme shimmer colors - enhanced contrast for better visibility
  --shimmer-base-color: #bbb;
  --shimmer-highlight-color: #ddd;
  --shimmer-peak-color: #fff;

  .enhance-icons {
    .enhance-loading-container {
      .enhance-loading-text {
        color: var(--color-text-secondary-dark, #ccc);
        background: linear-gradient(
          90deg,
          var(--shimmer-base-color) 0%,
          var(--shimmer-base-color) 35%,
          var(--shimmer-highlight-color) 45%,
          var(--shimmer-peak-color) 50%,
          var(--shimmer-highlight-color) 55%,
          var(--shimmer-base-color) 65%,
          var(--shimmer-base-color) 100%
        );
        background-size: 300% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
::ng-deep .prompt-bar .prompt-text {
  font-weight: 500 !important;
}

::ng-deep .prompt-bar textarea::placeholder {
  opacity: 0.7;
}
