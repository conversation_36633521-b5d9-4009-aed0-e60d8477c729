<div class="container-fluid">
  <!-- <shared-app-header
    class="experience-nav-header p-0 m-0"
    #headerComponent
    *ngIf="showHeaderAndNav"
    [config]="headerConfig"
    [themeService]="themeService"
    (navigationEvent)="onNavigation($event)"
    (profileAction)="onProfileAction($event)"
    (themeToggle)="onThemeToggle($event)">
  </shared-app-header> -->
  <router-outlet></router-outlet>
</div>
