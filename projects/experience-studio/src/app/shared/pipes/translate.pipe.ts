import { <PERSON><PERSON>, PipeTransform, inject, ChangeDetectorRef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { I18nService } from '../services/i18n/i18n.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRef } from '@angular/core';

/**
 * Translation Pipe for Experience Studio
 *
 * Provides reactive translation functionality in templates with automatic updates
 * when the language changes. Uses Angular 19+ patterns for optimal performance.
 *
 * Features:
 * - Automatic updates when language changes
 * - Parameter interpolation support
 * - Fallback to key if translation not found
 * - Memory efficient with proper cleanup
 *
 * @example
 * ```html
 * <!-- Basic translation -->
 * {{ 'common.buttons.submit' | translate }}
 *
 * <!-- Translation with parameters -->
 * {{ 'common.validation.minLength' | translate:{ min: 5 } }}
 *
 * <!-- Translation with fallback -->
 * {{ 'some.missing.key' | translate:'Default Text' }}
 * ```
 */
@Pipe({
  name: 'translate',
  standalone: true,
  pure: false // Impure pipe to react to language changes
})
export class TranslatePipe implements PipeTransform, OnD<PERSON>roy {
  private readonly i18nService = inject(I18nService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly destroyRef = inject(DestroyRef);

  private lastKey: string = '';
  private lastParams: Record<string, any> | undefined;
  private lastValue: string = '';
  private initialized = false;

  constructor() {
    this.setupLanguageChangeSubscription();
  }

  /**
   * Transform method for the pipe
   * @param key Translation key
   * @param params Optional parameters for interpolation
   * @param fallback Optional fallback text if translation not found
   * @returns Translated string
   */
  transform(
    key: string,
    params?: Record<string, any>,
    fallback?: string
  ): string {
    if (!key) {
      return fallback || '';
    }

    // Check if we need to update the translation
    const needsUpdate = this.shouldUpdate(key, params);

    if (needsUpdate) {
      this.lastKey = key;
      this.lastParams = params;
      this.lastValue = this.getTranslation(key, params, fallback);
    }

    return this.lastValue;
  }

  /**
   * Check if the translation needs to be updated
   * @param key Current translation key
   * @param params Current parameters
   * @returns True if update is needed
   */
  private shouldUpdate(key: string, params?: Record<string, any>): boolean {
    return (
      !this.initialized ||
      key !== this.lastKey ||
      !this.areParamsEqual(params, this.lastParams)
    );
  }

  /**
   * Get the translation from the service
   * @param key Translation key
   * @param params Optional parameters
   * @param fallback Optional fallback text
   * @returns Translated string
   */
  private getTranslation(
    key: string,
    params?: Record<string, any>,
    fallback?: string
  ): string {
    const translation = this.i18nService.translate(key, params);

    // If translation equals the key (not found), use fallback if provided
    if (translation === key && fallback) {
      return fallback;
    }

    return translation;
  }

  /**
   * Compare parameters for equality
   * @param params1 First parameter set
   * @param params2 Second parameter set
   * @returns True if parameters are equal
   */
  private areParamsEqual(
    params1?: Record<string, any>,
    params2?: Record<string, any>
  ): boolean {
    if (params1 === params2) {
      return true;
    }

    if (!params1 || !params2) {
      return false;
    }

    const keys1 = Object.keys(params1);
    const keys2 = Object.keys(params2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    return keys1.every(key => params1[key] === params2[key]);
  }

  /**
   * Set up subscription to language changes for automatic updates
   */
  private setupLanguageChangeSubscription(): void {
    this.i18nService.getLanguageChange$()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        // Reset state to force re-evaluation
        this.initialized = false;
        this.lastKey = '';
        this.lastParams = undefined;
        this.lastValue = '';

        // Trigger change detection
        this.cdr.markForCheck();
      });

    this.initialized = true;
  }

  /**
   * Cleanup when pipe is destroyed
   */
  ngOnDestroy(): void {
    // Cleanup is handled by takeUntilDestroyed
  }
}

/**
 * Async Translation Pipe for Experience Studio
 *
 * Provides reactive translation functionality using signals for better performance.
 * This pipe is pure and more efficient for static translations.
 *
 * @example
 * ```html
 * <!-- Basic async translation -->
 * {{ 'common.buttons.submit' | translateAsync | async }}
 *
 * <!-- With parameters -->
 * {{ ('common.validation.minLength' | translateAsync:{ min: 5 }) | async }}
 * ```
 */
@Pipe({
  name: 'translateAsync',
  standalone: true,
  pure: true // Pure pipe for better performance
})
export class TranslateAsyncPipe implements PipeTransform {
  private readonly i18nService = inject(I18nService);

  /**
   * Transform method for async translation
   * @param key Translation key
   * @param params Optional parameters for interpolation
   * @returns Signal with translated string
   */
  transform(key: string, params?: Record<string, any>) {
    if (!key) {
      return this.i18nService.translateSignal('', params);
    }

    return this.i18nService.translateSignal(key, params);
  }
}

/**
 * Translation Directive for Experience Studio
 *
 * Provides translation functionality as a directive for more complex scenarios.
 *
 * @example
 * ```html
 * <!-- Basic directive usage -->
 * <span [translate]="'common.buttons.submit'"></span>
 *
 * <!-- With parameters -->
 * <span [translate]="'common.validation.minLength'" [translateParams]="{ min: 5 }"></span>
 *
 * <!-- With fallback -->
 * <span [translate]="'some.key'" translateFallback="Default Text"></span>
 * ```
 */
import { Directive, Input, ElementRef, OnInit } from '@angular/core';

@Directive({
  selector: '[translate]',
  standalone: true
})
export class TranslateDirective implements OnInit, OnDestroy {
  private readonly i18nService = inject(I18nService);
  private readonly elementRef = inject(ElementRef);
  private readonly destroyRef = inject(DestroyRef);

  @Input() translate: string = '';
  @Input() translateParams?: Record<string, any>;
  @Input() translateFallback?: string;

  ngOnInit(): void {
    this.updateTranslation();
    this.setupLanguageChangeSubscription();
  }

  ngOnDestroy(): void {
    // Cleanup is handled by takeUntilDestroyed
  }

  /**
   * Update the element's text content with translation
   */
  private updateTranslation(): void {
    if (!this.translate) {
      return;
    }

    const translation = this.i18nService.translate(this.translate, this.translateParams);
    const finalText = translation === this.translate && this.translateFallback
      ? this.translateFallback
      : translation;

    this.elementRef.nativeElement.textContent = finalText;
  }

  /**
   * Set up subscription to language changes
   */
  private setupLanguageChangeSubscription(): void {
    this.i18nService.getLanguageChange$()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.updateTranslation();
      });
  }
}
