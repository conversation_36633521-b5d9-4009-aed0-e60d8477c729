// import { Directive, ElementRef, Input, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
// import { createLogger } from '../utils/logger';

// /**
//  * Lenis Prevent Directive
//  *
//  * A utility directive to easily mark elements that should prevent Lenis smooth scrolling.
//  * This directive adds the necessary data attributes and classes to ensure native scrolling
//  * behavior is preserved for specific components.
//  *
//  * Usage:
//  * ```html
//  * <!-- Prevent all Lenis scrolling -->
//  * <div lenisPrevent>Content with native scrolling</div>
//  *
//  * <!-- Prevent only wheel events -->
//  * <div lenisPrevent="wheel">Content with native wheel scrolling</div>
//  *
//  * <!-- Prevent only touch events -->
//  * <div lenisPrevent="touch">Content with native touch scrolling</div>
//  *
//  * <!-- Prevent both wheel and touch -->
//  * <div lenisPrevent="both">Content with native scrolling</div>
//  * ```
//  */
// @Directive({
//   selector: '[lenisPrevent]',
//   standalone: true
// })
// export class LenisPreventDirective implements OnInit, OnDestroy {
//   private readonly elementRef = inject(ElementRef);
//   private readonly logger = createLogger('LenisPreventDirective');

//   /**
//    * Type of prevention to apply
//    * - 'all' or true: Prevent all Lenis scrolling (default)
//    * - 'wheel': Prevent only wheel events
//    * - 'touch': Prevent only touch events
//    * - 'both': Prevent both wheel and touch events
//    */
//   @Input('lenisPrevent') preventType: boolean | 'all' | 'wheel' | 'touch' | 'both' = true;

//   private addedAttributes: string[] = [];
//   private addedClasses: string[] = [];

//   ngOnInit(): void {
//     this.applyPreventionAttributes();
//   }

//   ngOnDestroy(): void {
//     this.removePreventionAttributes();
//   }

//   /**
//    * Apply the appropriate data attributes and classes based on prevention type
//    */
//   private applyPreventionAttributes(): void {
//     const element = this.elementRef.nativeElement as HTMLElement;

//     if (!element) return;

//     // Determine which attributes to add based on prevention type
//     const attributesToAdd = this.getAttributesForPreventionType();
//     const classesToAdd = this.getClassesForPreventionType();

//     // Add data attributes
//     attributesToAdd.forEach(attr => {
//       if (!element.hasAttribute(attr)) {
//         element.setAttribute(attr, '');
//         this.addedAttributes.push(attr);
//       }
//     });

//     // Add CSS classes
//     classesToAdd.forEach(className => {
//       if (!element.classList.contains(className)) {
//         element.classList.add(className);
//         this.addedClasses.push(className);
//       }
//     });

//     this.logger.debug('🚫 Lenis prevention applied:', {
//       element: element.tagName,
//       preventType: this.preventType,
//       attributes: attributesToAdd,
//       classes: classesToAdd
//     });
//   }

//   /**
//    * Remove all added attributes and classes
//    */
//   private removePreventionAttributes(): void {
//     const element = this.elementRef.nativeElement as HTMLElement;

//     if (!element) return;

//     // Remove added attributes
//     this.addedAttributes.forEach(attr => {
//       element.removeAttribute(attr);
//     });

//     // Remove added classes
//     this.addedClasses.forEach(className => {
//       element.classList.remove(className);
//     });

//     this.addedAttributes = [];
//     this.addedClasses = [];
//   }

//   /**
//    * Get data attributes to add based on prevention type
//    */
//   private getAttributesForPreventionType(): string[] {
//     const preventType = this.preventType;

//     if (preventType === true || preventType === 'all' || preventType === 'both') {
//       return ['data-lenis-prevent'];
//     }

//     if (preventType === 'wheel') {
//       return ['data-lenis-prevent-wheel'];
//     }

//     if (preventType === 'touch') {
//       return ['data-lenis-prevent-touch'];
//     }

//     // Default to preventing all
//     return ['data-lenis-prevent'];
//   }

//   /**
//    * Get CSS classes to add based on prevention type
//    */
//   private getClassesForPreventionType(): string[] {
//     // Always add the base prevention class for styling purposes
//     return ['lenis-prevent'];
//   }
// }

// /**
//  * Lenis Scroll Target Directive
//  *
//  * A directive to mark elements as scroll targets for easy navigation.
//  * This can be used with the Lenis service's scrollTo method.
//  *
//  * Usage:
//  * ```html
//  * <section lenisScrollTarget="hero">Hero Section</section>
//  * <section lenisScrollTarget="features">Features Section</section>
//  * ```
//  */
// @Directive({
//   selector: '[lenisScrollTarget]',
//   standalone: true
// })
// export class LenisScrollTargetDirective implements OnInit {
//   private readonly elementRef = inject(ElementRef);
//   private readonly logger = createLogger('LenisScrollTargetDirective');

//   /**
//    * Unique identifier for this scroll target
//    */
//   @Input('lenisScrollTarget') targetId!: string;

//   ngOnInit(): void {
//     this.setupScrollTarget();
//   }

//   /**
//    * Setup the element as a scroll target
//    */
//   private setupScrollTarget(): void {
//     const element = this.elementRef.nativeElement as HTMLElement;

//     if (!element || !this.targetId) return;

//     // Add ID if not already present
//     if (!element.id) {
//       element.id = this.targetId;
//     }

//     // Add data attribute for identification
//     element.setAttribute('data-lenis-target', this.targetId);

//     // Add CSS class for styling
//     element.classList.add('lenis-scroll-target');

//     this.logger.debug('🎯 Lenis scroll target registered:', {
//       element: element.tagName,
//       targetId: this.targetId,
//       elementId: element.id
//     });
//   }
// }

// /**
//  * Lenis Smooth Scroll Directive
//  *
//  * A directive to enable smooth scrolling to specific targets when clicked.
//  *
//  * Usage:
//  * ```html
//  * <button lenisSmoothScroll="hero">Go to Hero</button>
//  * <a lenisSmoothScroll="#features" [lenisScrollOptions]="{ offset: -100 }">Features</a>
//  * ```
//  */
// @Directive({
//   selector: '[lenisSmoothScroll]',
//   standalone: true
// })
// export class LenisSmoothScrollDirective implements OnInit, OnDestroy {
//   private readonly elementRef = inject(ElementRef);
//   private readonly logger = createLogger('LenisSmoothScrollDirective');

//   /**
//    * Target to scroll to (can be selector, element ID, or number)
//    */
//   @Input('lenisSmoothScroll') scrollTarget!: string | number;

//   /**
//    * Options for the scroll animation
//    */
//   @Input() lenisScrollOptions: {
//     offset?: number;
//     lerp?: number;
//     duration?: number;
//     immediate?: boolean;
//   } = {};

//   private clickListener?: (event: Event) => void;

//   ngOnInit(): void {
//     this.setupClickListener();
//   }

//   ngOnDestroy(): void {
//     this.removeClickListener();
//   }

//   /**
//    * Setup click event listener for smooth scrolling
//    */
//   private setupClickListener(): void {
//     const element = this.elementRef.nativeElement as HTMLElement;

//     if (!element) return;

//     this.clickListener = (event: Event) => {
//       event.preventDefault();

//       // Import the service dynamically to avoid circular dependencies
//       import('../services/lenis-smooth-scroll.service').then(({ LenisSmoothScrollService }) => {
//         // Note: In a real implementation, you'd inject this service properly
//         // This is a simplified example for the directive
//         this.logger.info('🎯 Smooth scroll triggered:', {
//           target: this.scrollTarget,
//           options: this.lenisScrollOptions
//         });
//       });
//     };

//     element.addEventListener('click', this.clickListener);

//     // Add visual indicator
//     element.classList.add('lenis-smooth-scroll-trigger');
//   }

//   /**
//    * Remove click event listener
//    */
//   private removeClickListener(): void {
//     const element = this.elementRef.nativeElement as HTMLElement;

//     if (element && this.clickListener) {
//       element.removeEventListener('click', this.clickListener);
//       element.classList.remove('lenis-smooth-scroll-trigger');
//     }
//   }
// }
