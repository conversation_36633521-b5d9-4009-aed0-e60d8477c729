/**
 * Wireframe Design System API Interfaces
 * 
 * These interfaces define the structure for the wireframe generation design system API
 * endpoint response, specifically for design tokens display in the artifacts tab.
 */

/**
 * Color token from the wireframe design system API
 */
export interface WireframeColorToken {
  id: string;
  name: string;
  value: string; // Hex color value
  category: string;
  editable: boolean;
}

/**
 * Typography token from the wireframe design system API
 */
export interface WireframeTypographyToken {
  id: string;
  name: string;
  value: string; // Font family, weight, size, etc.
  category: string;
  editable: boolean;
}

/**
 * Spacing token from the wireframe design system API
 */
export interface WireframeSpacingToken {
  id: string;
  name: string;
  value: string; // Spacing value (px, rem, etc.)
  category: string;
  editable: boolean;
}

/**
 * Design tokens container from the wireframe design system API
 */
export interface WireframeDesignTokens {
  colors?: WireframeColorToken[];
  typography?: WireframeTypographyToken[];
  spacing?: WireframeSpacingToken[];
}

/**
 * Complete API response from /wireframe-generation/design-system endpoint
 */
export interface WireframeDesignSystemResponse {
  design_tokens: WireframeDesignTokens;
  status?: string;
  message?: string;
}

/**
 * Unified design token interface for internal use
 * Maps wireframe API tokens to the existing DesignToken interface structure
 */
export interface WireframeDesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
  description?: string;
  usage?: string[];
}

/**
 * API error response structure
 */
export interface WireframeDesignSystemError {
  error: string;
  message: string;
  status?: number;
}

/**
 * Service state for wireframe design system data
 */
export interface WireframeDesignSystemState {
  designTokens: WireframeDesignToken[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}
