/**
 * Mock data interfaces for wireframe generation UI enhancements
 * Used to simulate realistic generation process with hardcoded data
 */

export interface DesignToken {
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'border' | 'shadow' | 'size';
  category: string;
  description?: string;
}

export interface DesignTokenCategory {
  name: string;
  description: string;
  tokens: DesignToken[];
}

export interface MockDesignTokens {
  version: string;
  lastUpdated: string;
  categories: DesignTokenCategory[];
}

export interface ApplicationLogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug' | 'success';
  message: string;
  component?: string;
  details?: string;
  duration?: number;
}

export interface WireframeGenerationStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  duration?: number;
  timestamp?: string;
}

export interface WireframeGenerationProgress {
  currentStep: number;
  totalSteps: number;
  steps: WireframeGenerationStep[];
  isComplete: boolean;
  hasError: boolean;
  errorMessage?: string;
}

/**
 * Mock design tokens data for wireframe generation
 */
export const MOCK_DESIGN_TOKENS: MockDesignTokens = {
  version: "1.0.0",
  lastUpdated: new Date().toISOString(),
  categories: [
    {
      name: "Colors",
      description: "Primary color palette and semantic color tokens",
      tokens: [
        {
          name: "primary-50",
          value: "#f0f9ff",
          type: "color",
          category: "primary",
          description: "Lightest primary color"
        },
        {
          name: "primary-500",
          value: "#3b82f6",
          type: "color",
          category: "primary",
          description: "Main primary color"
        },
        {
          name: "primary-900",
          value: "#1e3a8a",
          type: "color",
          category: "primary",
          description: "Darkest primary color"
        },
        {
          name: "neutral-50",
          value: "#f9fafb",
          type: "color",
          category: "neutral",
          description: "Lightest neutral color"
        },
        {
          name: "neutral-500",
          value: "#6b7280",
          type: "color",
          category: "neutral",
          description: "Main neutral color"
        },
        {
          name: "neutral-900",
          value: "#111827",
          type: "color",
          category: "neutral",
          description: "Darkest neutral color"
        },
        {
          name: "success-500",
          value: "#10b981",
          type: "color",
          category: "semantic",
          description: "Success state color"
        },
        {
          name: "error-500",
          value: "#ef4444",
          type: "color",
          category: "semantic",
          description: "Error state color"
        },
        {
          name: "warning-500",
          value: "#f59e0b",
          type: "color",
          category: "semantic",
          description: "Warning state color"
        }
      ]
    },
    {
      name: "Typography",
      description: "Font sizes, weights, and line heights for consistent text styling",
      tokens: [
        {
          name: "font-size-xs",
          value: "0.75rem",
          type: "typography",
          category: "size",
          description: "Extra small text size"
        },
        {
          name: "font-size-sm",
          value: "0.875rem",
          type: "typography",
          category: "size",
          description: "Small text size"
        },
        {
          name: "font-size-base",
          value: "1rem",
          type: "typography",
          category: "size",
          description: "Base text size"
        },
        {
          name: "font-size-lg",
          value: "1.125rem",
          type: "typography",
          category: "size",
          description: "Large text size"
        },
        {
          name: "font-size-xl",
          value: "1.25rem",
          type: "typography",
          category: "size",
          description: "Extra large text size"
        },
        {
          name: "font-size-2xl",
          value: "1.5rem",
          type: "typography",
          category: "size",
          description: "2X large text size"
        },
        {
          name: "font-weight-normal",
          value: "400",
          type: "typography",
          category: "weight",
          description: "Normal font weight"
        },
        {
          name: "font-weight-medium",
          value: "500",
          type: "typography",
          category: "weight",
          description: "Medium font weight"
        },
        {
          name: "font-weight-semibold",
          value: "600",
          type: "typography",
          category: "weight",
          description: "Semi-bold font weight"
        },
        {
          name: "font-weight-bold",
          value: "700",
          type: "typography",
          category: "weight",
          description: "Bold font weight"
        },
        {
          name: "line-height-tight",
          value: "1.25",
          type: "typography",
          category: "line-height",
          description: "Tight line height"
        },
        {
          name: "line-height-normal",
          value: "1.5",
          type: "typography",
          category: "line-height",
          description: "Normal line height"
        },
        {
          name: "line-height-relaxed",
          value: "1.75",
          type: "typography",
          category: "line-height",
          description: "Relaxed line height"
        }
      ]
    },
    {
      name: "Spacing",
      description: "Consistent spacing values for margins, padding, and gaps",
      tokens: [
        {
          name: "space-1",
          value: "0.25rem",
          type: "spacing",
          category: "base",
          description: "Extra small spacing"
        },
        {
          name: "space-2",
          value: "0.5rem",
          type: "spacing",
          category: "base",
          description: "Small spacing"
        },
        {
          name: "space-4",
          value: "1rem",
          type: "spacing",
          category: "base",
          description: "Base spacing"
        },
        {
          name: "space-6",
          value: "1.5rem",
          type: "spacing",
          category: "base",
          description: "Medium spacing"
        },
        {
          name: "space-8",
          value: "2rem",
          type: "spacing",
          category: "base",
          description: "Large spacing"
        },
        {
          name: "space-12",
          value: "3rem",
          type: "spacing",
          category: "base",
          description: "Extra large spacing"
        },
        {
          name: "space-16",
          value: "4rem",
          type: "spacing",
          category: "base",
          description: "2X large spacing"
        },
        {
          name: "space-24",
          value: "6rem",
          type: "spacing",
          category: "base",
          description: "3X large spacing"
        }
      ]
    },
    {
      name: "Border Radius",
      description: "Border radius values for consistent rounded corners",
      tokens: [
        {
          name: "border-radius-none",
          value: "0",
          type: "border",
          category: "radius",
          description: "No border radius"
        },
        {
          name: "border-radius-sm",
          value: "0.125rem",
          type: "border",
          category: "radius",
          description: "Small border radius"
        },
        {
          name: "border-radius-base",
          value: "0.25rem",
          type: "border",
          category: "radius",
          description: "Base border radius"
        },
        {
          name: "border-radius-md",
          value: "0.375rem",
          type: "border",
          category: "radius",
          description: "Medium border radius"
        },
        {
          name: "border-radius-lg",
          value: "0.5rem",
          type: "border",
          category: "radius",
          description: "Large border radius"
        },
        {
          name: "border-radius-xl",
          value: "0.75rem",
          type: "border",
          category: "radius",
          description: "Extra large border radius"
        },
        {
          name: "border-radius-full",
          value: "9999px",
          type: "border",
          category: "radius",
          description: "Full border radius (circular)"
        }
      ]
    },
    {
      name: "Shadows",
      description: "Box shadow definitions for depth and elevation",
      tokens: [
        {
          name: "shadow-sm",
          value: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
          type: "shadow",
          category: "elevation",
          description: "Small shadow"
        },
        {
          name: "shadow-base",
          value: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
          type: "shadow",
          category: "elevation",
          description: "Base shadow"
        },
        {
          name: "shadow-md",
          value: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
          type: "shadow",
          category: "elevation",
          description: "Medium shadow"
        },
        {
          name: "shadow-lg",
          value: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
          type: "shadow",
          category: "elevation",
          description: "Large shadow"
        },
        {
          name: "shadow-xl",
          value: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
          type: "shadow",
          category: "elevation",
          description: "Extra large shadow"
        }
      ]
    }
  ]
};

/**
 * Mock wireframe generation steps for stepper component
 */
export const MOCK_WIREFRAME_STEPS: WireframeGenerationStep[] = [
  {
    id: "step-1",
    title: "Analyzing wireframe requirements...",
    description: "Processing user input and understanding project requirements",
    status: "pending"
  },
  {
    id: "step-2",
    title: "Creating component structure...",
    description: "Defining component hierarchy and layout structure",
    status: "pending"
  },
  {
    id: "step-3",
    title: "Generating design tokens...",
    description: "Creating consistent design system tokens and variables",
    status: "pending"
  },
  {
    id: "step-4",
    title: "Building application architecture...",
    description: "Establishing routing, state management, and data flow",
    status: "pending"
  },
  {
    id: "step-5",
    title: "Finalizing wireframe design...",
    description: "Optimizing layout and preparing final wireframe structure",
    status: "pending"
  },
  {
    id: "step-6",
    title: "Generating...",
    description: "Completing wireframe generation and preparing output",
    status: "pending"
  }
];

/**
 * Mock application log entries for artifacts tab
 */
export const MOCK_APPLICATION_LOGS: ApplicationLogEntry[] = [
  {
    id: "log-1",
    timestamp: new Date().toISOString(),
    level: "info",
    message: "Wireframe generation process initiated",
    component: "WireframeGenerator",
    details: "Starting analysis of user requirements and project specifications"
  },
  {
    id: "log-2",
    timestamp: new Date(Date.now() + 5000).toISOString(),
    level: "info",
    message: "Component structure analysis completed",
    component: "ComponentAnalyzer",
    details: "Identified 8 main components and 15 sub-components",
    duration: 4.2
  },
  {
    id: "log-3",
    timestamp: new Date(Date.now() + 10000).toISOString(),
    level: "success",
    message: "Design tokens generated successfully",
    component: "DesignTokenGenerator",
    details: "Created 45 design tokens across 5 categories",
    duration: 2.8
  },
  {
    id: "log-4",
    timestamp: new Date(Date.now() + 15000).toISOString(),
    level: "info",
    message: "Building application routing structure",
    component: "RoutingBuilder",
    details: "Configuring navigation paths and route guards"
  },
  {
    id: "log-5",
    timestamp: new Date(Date.now() + 20000).toISOString(),
    level: "info",
    message: "Optimizing component relationships",
    component: "ComponentOptimizer",
    details: "Analyzing component dependencies and data flow patterns"
  },
  {
    id: "log-6",
    timestamp: new Date(Date.now() + 25000).toISOString(),
    level: "success",
    message: "Layout structure finalized",
    component: "LayoutBuilder",
    details: "Responsive layout patterns applied successfully",
    duration: 3.5
  },
  {
    id: "log-7",
    timestamp: new Date(Date.now() + 30000).toISOString(),
    level: "info",
    message: "Preparing wireframe output files",
    component: "FileGenerator",
    details: "Generating component files and documentation"
  },
  {
    id: "log-8",
    timestamp: new Date(Date.now() + 35000).toISOString(),
    level: "success",
    message: "Wireframe generation completed successfully",
    component: "WireframeGenerator",
    details: "All components generated and ready for preview",
    duration: 35.2
  }
];

/**
 * Function to generate additional log entries during the generation process
 */
export function generateProgressLogEntry(stepIndex: number, timestamp: Date): ApplicationLogEntry {
  const progressLogs = [
    {
      message: "Analyzing user interface requirements",
      component: "RequirementAnalyzer",
      details: "Processing layout specifications and user interaction patterns"
    },
    {
      message: "Mapping component hierarchy",
      component: "HierarchyMapper",
      details: "Establishing parent-child relationships and component nesting"
    },
    {
      message: "Applying design system principles",
      component: "DesignSystemApplier",
      details: "Ensuring consistency with established design tokens and patterns"
    },
    {
      message: "Configuring responsive breakpoints",
      component: "ResponsiveBuilder",
      details: "Setting up mobile, tablet, and desktop layout variations"
    },
    {
      message: "Validating accessibility standards",
      component: "AccessibilityValidator",
      details: "Checking WCAG compliance and semantic HTML structure"
    },
    {
      message: "Optimizing performance characteristics",
      component: "PerformanceOptimizer",
      details: "Implementing lazy loading and code splitting strategies"
    }
  ];

  const logTemplate = progressLogs[stepIndex % progressLogs.length];

  return {
    id: `progress-log-${stepIndex}-${Date.now()}`,
    timestamp: timestamp.toISOString(),
    level: "info",
    message: logTemplate.message,
    component: logTemplate.component,
    details: logTemplate.details,
    duration: Math.random() * 3 + 1 // Random duration between 1-4 seconds
  };
}
