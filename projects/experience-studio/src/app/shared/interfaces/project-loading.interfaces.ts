// Extended interfaces for project loading data processing and mapping

export interface ParsedProjectData {
  // Project Details Section
  projectId: string;
  projectName: string;
  projectDescription: string;
  userPrompt: string; // Mapped from project_description for chat-window

  // Project Settings Section
  device: string;
  framework: string;
  designSystem: string; // tech stack
  generationType: string; // should be "app_generation"

  // Repository Details Section
  cloneUrl: string; // for VSCode clone functionality
  deployedUrl: string; // cleaned for preview iframe

  // SSE Events Data (for first generation)
  metadata: string; // JSON string containing SSE events

  // Code Files Data
  initialGenerationFiles: FileData[]; // Files from metadata BUILD event
  regenerationFiles: FileData[]; // Files from conversation ui_metadata

  // UI Design Data (for wireframe_generation projects)
  uiDesignPages: Array<{
    fileName: string;
    content: string;
  }>; // UI Design pages from conversation ui_metadata

  // Artifacts Data (from metadata SSE events)
  artifactsData: Array<{
    name: string;
    type: 'text' | 'json' | 'markdown' | 'image' | 'component' | 'ref_code' | 'files' | 'repository' | 'artifact';
    content: any;
    source: string;
    progress: string;
    log: string; // Full log entry from SSE event
    tokens?: any; // For design system artifacts
    layoutKey?: string; // For layout artifacts
    imageUrl?: string; // For layout artifacts
  }>;

  // Log Entries Data (chronological SSE logs)
  logEntries: Array<{
    log: string;
    progress: string;
    status: string;
    timestamp: number;
    progress_description: string;
  }>;

  // Conversation Data
  conversationMessages: ParsedConversationMessage[];
  commitIds: string[]; // extracted from capsule messages

  // Regeneration Analysis
  hasRegenerationData: boolean; // if conversation array is not empty
  regenerationCount: number; // number of regeneration messages
  firstCutMetadata?: FirstCutMetadata;
}

export interface ParsedConversationMessage {
  messageId: string;
  conversationId: string;
  messageType: 'user' | 'assistant' | 'capsule';
  content: string;
  parsedUIMetadata?: any;
  rawUIMetadata?: string; // Keep raw ui_metadata for capsule messages
  createdAt: Date;

  // For capsule messages
  commitId?: string;

  // For assistant messages with file data
  fileData?: FileData[];
}

export interface FileData {
  fileName: string;
  content: string;
  path: string;
  language?: string;
}

export interface FirstCutMetadata {
  logs: string[];
  progressDescription: string;
  stepperProgress: StepperProgressData;
  status: string;
  artifactData: ArtifactData;
}

export interface StepperProgressData {
  currentStep: number;
  totalSteps: number;
  steps: StepData[];
  status: 'IN_PROGRESS' | 'COMPLETED' | 'ERROR';
}

export interface StepData {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  timestamp?: Date;
  duration?: number;
}

export interface ArtifactData {
  type: string;
  data: any;
  timestamp: Date;
  version?: string;
}

// Chat Window Integration Interfaces
export interface ChatWindowMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;

  // For assistant messages with code files
  codeFiles?: ChatCodeFile[];
}

export interface ChatCodeFile {
  name: string;
  content: string;
  language: string;
  path: string;
}

// Version Accordion Integration Interfaces
export interface VersionAccordionData {
  commitId: string;
  version: string;
  timestamp: Date;
  description: string;
  files: string[]; // Array of file names/paths for accordion display
  generationResult?: any; // Store the full GenerationResult for chat window integration
}

export interface VersionFile {
  name: string;
  path: string;
  type: 'file' | 'folder';
  content?: string;
  language?: string;
}

// Stepper Integration Interfaces
export interface StepperInitializationData {
  projectId: string;
  jobId?: string;
  initialSteps: StepperStep[];
  currentProgress: StepperProgress;
  status: StepperStatus;
}

export interface StepperStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  timestamp?: Date;
  duration?: number;
  logs?: string[];
}

export interface StepperProgress {
  currentStepIndex: number;
  totalSteps: number;
  progressPercentage: number;
  description: string;
}

export type StepperStatus = 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'ERROR' | 'CANCELLED';

// UI Design Conversation Data Interface
export interface UIDesignConversationData {
  chatMessages: Array<{ userPrompt: string; assistantResponse: string; commitId?: string }>;
  fileVersions: Map<string, { fileName: string; content: string; commitId: string; generationIndex: number }>;
  latestFiles: Array<{ fileName: string; content: string }>;
  generationCount: number;
}

// Code Window Integration Interfaces
export interface CodeWindowInitializationData {
  mode: 'project-loading';
  projectData: ParsedProjectData;
  chatMessages: ChatWindowMessage[];
  stepperData: StepperInitializationData;
  versionData: VersionAccordionData[];
  repositoryData: RepositoryIntegrationData;
  uiDesignConversationData?: UIDesignConversationData | null;
}

export interface RepositoryIntegrationData {
  cloneUrl: string;
  deployedUrl: string;
  vcsProvider: string;
  deploymentProvider: string;
  projectId: string;
}

// Error Handling Interfaces
export interface ProjectLoadingError {
  type: 'NETWORK_ERROR' | 'PARSING_ERROR' | 'VALIDATION_ERROR' | 'API_ERROR';
  message: string;
  details?: any;
  timestamp: Date;
}

// Utility Types
export type ProjectLoadingState = 'idle' | 'loading' | 'loaded' | 'error';

export interface ProjectLoadingProgress {
  stage: 'fetching-project' | 'parsing-data' | 'loading-templates' | 'initializing-components';
  progress: number; // 0-100
  message: string;
}
