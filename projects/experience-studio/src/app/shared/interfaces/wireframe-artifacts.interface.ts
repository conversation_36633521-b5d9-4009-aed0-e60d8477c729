export interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

export interface ApplicationLog {
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: string;
}

export interface WireframeArtifactState {
  designTokens: DesignToken[];
  applicationLogs: ApplicationLog[];
  isLoading: boolean;
  error: string | null;
}
