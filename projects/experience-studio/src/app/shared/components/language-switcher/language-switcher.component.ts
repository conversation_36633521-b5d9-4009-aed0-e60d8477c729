import { 
  Component, 
  ChangeDetectionStrategy, 
  inject, 
  signal, 
  computed,
  DestroyRef,
  Input,
  Output,
  EventEmitter
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { I18nService, LanguageConfig } from '../../services/i18n/i18n.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { createLogger } from '../../utils/logger';

/**
 * Language switcher display modes
 */
export type LanguageSwitcherMode = 'dropdown' | 'inline' | 'compact';

/**
 * Language Switcher Component
 * 
 * Provides a user interface for switching between supported languages.
 * Uses Angular 19+ patterns with signals and OnPush change detection.
 * 
 * Features:
 * - Multiple display modes (dropdown, inline, compact)
 * - Theme-aware styling
 * - Keyboard navigation support
 * - Loading states and error handling
 * - Accessibility compliant
 * 
 * @example
 * ```html
 * <!-- Dropdown mode (default) -->
 * <app-language-switcher></app-language-switcher>
 * 
 * <!-- Inline mode -->
 * <app-language-switcher mode="inline"></app-language-switcher>
 * 
 * <!-- Compact mode -->
 * <app-language-switcher mode="compact" [showLabels]="false"></app-language-switcher>
 * ```
 */
@Component({
  selector: 'app-language-switcher',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="language-switcher" 
         [ngClass]="[currentTheme(), mode, { 'is-loading': isLoading() }]"
         [attr.aria-label]="'Language selector'">
      
      <!-- Dropdown Mode -->
      <div *ngIf="mode === 'dropdown'" class="dropdown-container">
        <button
          class="language-button dropdown-trigger"
          [class.active]="isDropdownOpen()"
          [disabled]="isLoading()"
          (click)="toggleDropdown()"
          (keydown)="handleKeyDown($event)"
          [attr.aria-expanded]="isDropdownOpen()"
          [attr.aria-haspopup]="true"
          [attr.aria-label]="'Current language: ' + currentLanguage().nativeName">
          
          <span class="language-flag">{{ currentLanguage().flag }}</span>
          <span *ngIf="showLabels" class="language-name">{{ currentLanguage().nativeName }}</span>
          <span class="dropdown-arrow" [class.rotated]="isDropdownOpen()">▼</span>
          
          <!-- Loading indicator -->
          <div *ngIf="isLoading()" class="loading-spinner" aria-hidden="true"></div>
        </button>

        <!-- Dropdown menu -->
        <div *ngIf="isDropdownOpen()" 
             class="dropdown-menu"
             role="listbox"
             [attr.aria-label]="'Available languages'">
          <button
            *ngFor="let language of supportedLanguages(); trackBy: trackByLanguageCode"
            class="language-option"
            [class.selected]="language.code === currentLanguage().code"
            [disabled]="isLoading()"
            (click)="selectLanguage(language)"
            (keydown)="handleOptionKeyDown($event, language)"
            role="option"
            [attr.aria-selected]="language.code === currentLanguage().code"
            [attr.aria-label]="'Switch to ' + language.nativeName">
            
            <span class="language-flag">{{ language.flag }}</span>
            <span class="language-name">{{ language.nativeName }}</span>
            <span *ngIf="language.code === currentLanguage().code" class="selected-indicator">✓</span>
          </button>
        </div>
      </div>

      <!-- Inline Mode -->
      <div *ngIf="mode === 'inline'" class="inline-container" role="radiogroup" [attr.aria-label]="'Language selection'">
        <button
          *ngFor="let language of supportedLanguages(); trackBy: trackByLanguageCode"
          class="language-button inline-option"
          [class.selected]="language.code === currentLanguage().code"
          [disabled]="isLoading()"
          (click)="selectLanguage(language)"
          (keydown)="handleKeyDown($event)"
          role="radio"
          [attr.aria-checked]="language.code === currentLanguage().code"
          [attr.aria-label]="'Switch to ' + language.nativeName">
          
          <span class="language-flag">{{ language.flag }}</span>
          <span *ngIf="showLabels" class="language-name">{{ language.nativeName }}</span>
        </button>
      </div>

      <!-- Compact Mode -->
      <div *ngIf="mode === 'compact'" class="compact-container">
        <button
          class="language-button compact-trigger"
          [disabled]="isLoading()"
          (click)="cycleLanguage()"
          (keydown)="handleKeyDown($event)"
          [attr.aria-label]="'Current language: ' + currentLanguage().nativeName + '. Click to cycle through languages'">
          
          <span class="language-flag">{{ currentLanguage().flag }}</span>
          <span *ngIf="showLabels" class="language-code">{{ currentLanguage().code.toUpperCase() }}</span>
          
          <!-- Loading indicator -->
          <div *ngIf="isLoading()" class="loading-spinner" aria-hidden="true"></div>
        </button>
      </div>

      <!-- Error state -->
      <div *ngIf="hasError()" class="error-message" role="alert">
        {{ errorMessage() || 'Failed to load language' }}
      </div>
    </div>
  `,
  styleUrls: ['./language-switcher.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LanguageSwitcherComponent {
  private readonly i18nService = inject(I18nService);
  private readonly themeService = inject(ThemeService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('LanguageSwitcherComponent');

  // Input properties
  @Input() mode: LanguageSwitcherMode = 'dropdown';
  @Input() showLabels: boolean = true;
  @Input() disabled: boolean = false;

  // Output events
  @Output() languageChanged = new EventEmitter<LanguageConfig>();
  @Output() languageChangeError = new EventEmitter<Error>();

  // Internal state using Angular 19+ signals
  private readonly dropdownOpen = signal<boolean>(false);
  private readonly isChangingLanguage = signal<boolean>(false);

  // Computed properties from i18n service
  readonly currentLanguage = computed(() => this.i18nService.currentLanguage());
  readonly supportedLanguages = computed(() => this.i18nService.supportedLanguages());
  readonly isLoading = computed(() => 
    this.i18nService.isLoading() || this.isChangingLanguage()
  );
  readonly hasError = computed(() => this.i18nService.hasError());
  readonly errorMessage = computed(() => this.i18nService.errorMessage());

  // Theme-aware computed property
  readonly currentTheme = computed(() => this.themeService.getCurrentTheme());

  // Computed for dropdown state
  readonly isDropdownOpen = computed(() => this.dropdownOpen() && !this.disabled);

  constructor() {
    this.setupEventListeners();
  }

  /**
   * Set up event listeners for component lifecycle
   */
  private setupEventListeners(): void {
    // Listen for language changes from the service
    this.i18nService.getLanguageChange$()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (languageCode) => {
          const language = this.supportedLanguages().find(lang => lang.code === languageCode);
          if (language) {
            this.languageChanged.emit(language);
            this.logger.info(`🌐 Language changed to: ${language.nativeName}`);
          }
          this.isChangingLanguage.set(false);
        },
        error: (error) => {
          this.languageChangeError.emit(error);
          this.logger.error('❌ Language change failed:', error);
          this.isChangingLanguage.set(false);
        }
      });

    // Close dropdown when clicking outside
    if (typeof document !== 'undefined') {
      document.addEventListener('click', this.handleDocumentClick.bind(this));
    }
  }

  /**
   * Toggle dropdown visibility
   */
  toggleDropdown(): void {
    if (this.disabled || this.isLoading()) {
      return;
    }
    this.dropdownOpen.set(!this.dropdownOpen());
  }

  /**
   * Select a specific language
   * @param language Language configuration to select
   */
  selectLanguage(language: LanguageConfig): void {
    if (this.disabled || this.isLoading() || language.code === this.currentLanguage().code) {
      return;
    }

    this.logger.info(`🔄 Selecting language: ${language.nativeName}`);
    this.isChangingLanguage.set(true);
    this.dropdownOpen.set(false);

    this.i18nService.changeLanguage(language.code)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.logger.info(`✅ Language changed successfully to: ${language.nativeName}`);
        },
        error: (error) => {
          this.logger.error(`❌ Failed to change language to ${language.nativeName}:`, error);
          this.languageChangeError.emit(error);
          this.isChangingLanguage.set(false);
        }
      });
  }

  /**
   * Cycle to the next language (for compact mode)
   */
  cycleLanguage(): void {
    if (this.disabled || this.isLoading()) {
      return;
    }

    const languages = this.supportedLanguages();
    const currentIndex = languages.findIndex(lang => lang.code === this.currentLanguage().code);
    const nextIndex = (currentIndex + 1) % languages.length;
    const nextLanguage = languages[nextIndex];

    this.selectLanguage(nextLanguage);
  }

  /**
   * Handle keyboard navigation
   * @param event Keyboard event
   */
  handleKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (this.mode === 'dropdown') {
          this.toggleDropdown();
        } else if (this.mode === 'compact') {
          this.cycleLanguage();
        }
        break;
      case 'Escape':
        if (this.dropdownOpen()) {
          event.preventDefault();
          this.dropdownOpen.set(false);
        }
        break;
      case 'ArrowDown':
        if (this.mode === 'dropdown' && !this.dropdownOpen()) {
          event.preventDefault();
          this.dropdownOpen.set(true);
        }
        break;
    }
  }

  /**
   * Handle keyboard navigation for dropdown options
   * @param event Keyboard event
   * @param language Language option
   */
  handleOptionKeyDown(event: KeyboardEvent, language: LanguageConfig): void {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.selectLanguage(language);
        break;
      case 'Escape':
        event.preventDefault();
        this.dropdownOpen.set(false);
        break;
    }
  }

  /**
   * Handle clicks outside the component to close dropdown
   * @param event Mouse event
   */
  private handleDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const component = target.closest('.language-switcher');
    
    if (!component && this.dropdownOpen()) {
      this.dropdownOpen.set(false);
    }
  }

  /**
   * TrackBy function for language list optimization
   * @param index Array index
   * @param language Language configuration
   * @returns Unique identifier for tracking
   */
  trackByLanguageCode(index: number, language: LanguageConfig): string {
    return language.code;
  }
}
