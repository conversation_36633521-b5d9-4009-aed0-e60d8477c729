// Language Switcher Component Styles
// Supports multiple themes and display modes

.language-switcher {
  position: relative;
  display: inline-block;
  font-family: 'Mulish', sans-serif;

  // Loading state
  &.is-loading {
    .language-button {
      opacity: 0.7;
      cursor: wait;
    }
  }

  // Common button styles
  .language-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid transparent;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    outline: none;
    position: relative;

    &:focus-visible {
      outline: 2px solid var(--focus-color, #007bff);
      outline-offset: 2px;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .language-flag {
      font-size: 16px;
      line-height: 1;
    }

    .language-name {
      white-space: nowrap;
    }

    .language-code {
      font-size: 12px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  // Loading spinner
  .loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Error message
  .error-message {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 4px;
    padding: 6px 8px;
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 4px;
    font-size: 12px;
    color: #c33;
    z-index: 1001;
  }

  // Dropdown Mode Styles
  &.dropdown {
    .dropdown-container {
      position: relative;
    }

    .dropdown-trigger {
      min-width: 120px;
      justify-content: space-between;

      .dropdown-arrow {
        font-size: 10px;
        transition: transform 0.2s ease;
        margin-left: auto;

        &.rotated {
          transform: rotate(180deg);
        }
      }

      &.active {
        border-color: var(--border-active, #007bff);
      }
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      margin-top: 4px;
      background: var(--dropdown-bg, #fff);
      border: 1px solid var(--dropdown-border, #ddd);
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      max-height: 200px;
      overflow-y: auto;

      .language-option {
        width: 100%;
        padding: 10px 12px;
        border: none;
        background: transparent;
        text-align: left;
        justify-content: flex-start;

        &:hover:not(:disabled) {
          background: var(--option-hover-bg, #f5f5f5);
        }

        &.selected {
          background: var(--option-selected-bg, #e3f2fd);
          color: var(--option-selected-color, #1976d2);

          .selected-indicator {
            margin-left: auto;
            color: var(--option-selected-color, #1976d2);
            font-weight: 600;
          }
        }

        &:first-child {
          border-top-left-radius: 6px;
          border-top-right-radius: 6px;
        }

        &:last-child {
          border-bottom-left-radius: 6px;
          border-bottom-right-radius: 6px;
        }
      }
    }
  }

  // Inline Mode Styles
  &.inline {
    .inline-container {
      display: flex;
      gap: 4px;
      padding: 4px;
      background: var(--inline-bg, #f8f9fa);
      border-radius: 8px;
    }

    .inline-option {
      min-width: 60px;
      justify-content: center;

      &.selected {
        background: var(--inline-selected-bg, #fff);
        border-color: var(--inline-selected-border, #007bff);
        color: var(--inline-selected-color, #007bff);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      &:hover:not(.selected):not(:disabled) {
        background: var(--inline-hover-bg, #e9ecef);
      }
    }
  }

  // Compact Mode Styles
  &.compact {
    .compact-trigger {
      min-width: 50px;
      justify-content: center;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      padding: 0;

      &:hover:not(:disabled) {
        transform: scale(1.05);
      }

      &:active:not(:disabled) {
        transform: scale(0.95);
      }
    }
  }

  // Light Theme
  &.light {
    --dropdown-bg: #ffffff;
    --dropdown-border: #e0e0e0;
    --border-active: #1976d2;
    --option-hover-bg: #f5f5f5;
    --option-selected-bg: #e3f2fd;
    --option-selected-color: #1976d2;
    --inline-bg: #f8f9fa;
    --inline-selected-bg: #ffffff;
    --inline-selected-border: #1976d2;
    --inline-selected-color: #1976d2;
    --inline-hover-bg: #e9ecef;
    --focus-color: #1976d2;

    .language-button {
      color: #333;
      border-color: #e0e0e0;

      &:hover:not(:disabled) {
        border-color: #bbb;
        background: #f9f9f9;
      }
    }
  }

  // Dark Theme
  &.dark {
    --dropdown-bg: #2d2d2d;
    --dropdown-border: #444;
    --border-active: #64b5f6;
    --option-hover-bg: #3d3d3d;
    --option-selected-bg: #1e3a5f;
    --option-selected-color: #64b5f6;
    --inline-bg: #2d2d2d;
    --inline-selected-bg: #1e3a5f;
    --inline-selected-border: #64b5f6;
    --inline-selected-color: #64b5f6;
    --inline-hover-bg: #3d3d3d;
    --focus-color: #64b5f6;

    .language-button {
      color: #e0e0e0;
      border-color: #444;

      &:hover:not(:disabled) {
        border-color: #666;
        background: #3d3d3d;
      }
    }

    .dropdown-menu {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }

  // RTL Support
  &[dir="rtl"] {
    .dropdown-trigger {
      .dropdown-arrow {
        margin-left: 0;
        margin-right: auto;
      }
    }

    .language-option {
      .selected-indicator {
        margin-left: 0;
        margin-right: auto;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    &.dropdown {
      .dropdown-trigger {
        min-width: 100px;
      }

      .dropdown-menu {
        left: -50%;
        right: -50%;
      }
    }

    &.inline {
      .inline-container {
        flex-wrap: wrap;
        gap: 2px;
      }

      .inline-option {
        min-width: 50px;
        font-size: 12px;
      }
    }
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    .language-button {
      border-width: 2px;
    }

    .dropdown-menu {
      border-width: 2px;
    }

    &.light {
      --dropdown-border: #000;
      --border-active: #000;
    }

    &.dark {
      --dropdown-border: #fff;
      --border-active: #fff;
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    .language-button,
    .dropdown-arrow,
    .loading-spinner {
      transition: none;
      animation: none;
    }

    &.compact .compact-trigger {
      &:hover:not(:disabled) {
        transform: none;
      }

      &:active:not(:disabled) {
        transform: none;
      }
    }
  }
}
