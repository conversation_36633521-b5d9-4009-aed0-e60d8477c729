import { Component, ChangeDetectionStrategy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-info-icon',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <svg 
      [attr.width]="size" 
      [attr.height]="size" 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      [class.active]="isActive"
      class="info-icon"
      role="img"
      [attr.aria-label]="ariaLabel">

      <circle
        cx="12"
        cy="12"
        r="10"
        class="icon-circle"
        [attr.stroke]="strokeColor"
        [attr.fill]="fillColor"
        stroke-width="1.5"/>

      <circle
        cx="12"
        cy="8"
        r="1.5"
        class="icon-dot"
        [attr.fill]="iconColor"/>

      <path
        d="M12 12v4"
        class="icon-line"
        [attr.stroke]="iconColor"
        stroke-width="2"
        stroke-linecap="round"/>

      <defs>
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
    </svg>
  `,
  styleUrls: ['./info-icon.component.scss']
})
export class InfoIconComponent {
  @Input() size: number = 20;
  @Input() isActive: boolean = false;
  @Input() ariaLabel: string = 'Information icon';

  get strokeColor(): string {
    return 'var(--info-icon-stroke, currentColor)';
  }

  get fillColor(): string {
    return 'var(--info-icon-fill, transparent)';
  }

  get iconColor(): string {
    return 'var(--info-icon-color, currentColor)';
  }
}
