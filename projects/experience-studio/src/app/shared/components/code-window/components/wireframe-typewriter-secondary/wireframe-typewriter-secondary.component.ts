import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  inject,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { interval, map, startWith, switchMap, of, EMPTY, delay } from 'rxjs';
import { WireframeMockDataService } from '../../../../services/wireframe-mock-data.service';
import { WireframeGenerationStateService } from '../../../../services/wireframe-generation-state.service';
import { WireframeGenerationStep } from '../../../../interfaces/wireframe-mock-data.interface';
import { ThemeService } from '../../../../services/theme-service/theme.service';

interface SecondaryTypewriterState {
  currentText: string;
  isComplete: boolean;
  currentStep: WireframeGenerationStep | null;
}

interface SecondaryScrollingTextState {
  currentMessageIndex: number;
  isActive: boolean;
}

@Component({
  selector: 'app-wireframe-typewriter-secondary',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="wireframe-combined-animation-secondary"
         [class.dark-theme]="isDarkTheme()"
         role="status"
         [attr.aria-label]="displayText() + ' - Additional generation details'">

      <!-- Typewriter Animation (Top) -->
      <div class="typewriter-container">
        <div class="typewriter-content">
          <span class="typewriter-icon" aria-hidden="true">⚡</span>
          <span class="typewriter-text">{{ displayText() }}</span>
          <span class="typewriter-cursor"
                [class.blinking]="!isTyping()"
                aria-hidden="true">|</span>
        </div>
      </div>

      <!-- Horizontal Scrolling Animation (Bottom) -->
      <div class="scrolling-container">
        <div class="scrolling-content">
          <span class="scrolling-icon" aria-hidden="true">🔧</span>
          <span class="scrolling-text">{{ currentMessage() }}</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .wireframe-combined-animation-secondary {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 0;
      font-family: 'Mulish', -apple-system, BlinkMacSystemFont, sans-serif;
      opacity: 0;
      animation: fadeIn 0.8s ease-in-out 0.5s forwards;
      text-align: center;
      gap: 12px;
    }

    /* Typewriter Animation Styles */
    .typewriter-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
    }

    .typewriter-content {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 24px;
      position: relative;
      gap: 6px;
    }

    .typewriter-icon {
      font-size: 16px;
      animation: pulse 2s ease-in-out infinite;
      margin-right: 2px;
      opacity: 0.8;
    }

    .typewriter-text {
      font-size: 16px;
      font-weight: 500;
      color: var(--code-viewer-text, #6b7280);
      letter-spacing: 0.025em;
      transition: color 0.3s ease;
      opacity: 0.9;
      line-height: 1.4;
    }

    .typewriter-cursor {
      font-size: 16px;
      font-weight: 500;
      color: var(--secondary-color, #e84393);
      margin-left: 2px;
      animation: none;
      transition: color 0.3s ease;
      display: inline-block;
      line-height: 1;
    }

    .typewriter-cursor.blinking {
      animation: blink 1.5s infinite;
    }

    /* Horizontal Scrolling Animation Styles */
    .scrolling-container {
      width: 100%;
      max-width: 500px;
      overflow: hidden;
      position: relative;
      height: 28px;
      display: flex;
      align-items: center;
    }

    .scrolling-content {
      display: flex;
      align-items: center;
      gap: 6px;
      white-space: nowrap;
      animation: scrollHorizontal 18s linear infinite;
      will-change: transform;
    }

    .scrolling-icon {
      font-size: 14px;
      animation: pulse 2.5s ease-in-out infinite;
      flex-shrink: 0;
      opacity: 0.7;
    }

    .scrolling-text {
      font-size: 14px;
      font-weight: 400;
      color: var(--code-viewer-text, #6b7280);
      letter-spacing: 0.025em;
      transition: color 0.3s ease;
      opacity: 0.8;
      line-height: 1.4;
      flex-shrink: 0;
    }

    /* Dark theme styles */
    .wireframe-combined-animation-secondary.dark-theme .typewriter-text {
      color: var(--code-viewer-text, #9ca3af);
    }

    .wireframe-combined-animation-secondary.dark-theme .typewriter-cursor {
      color: var(--secondary-color, #e84393);
    }

    .wireframe-combined-animation-secondary.dark-theme .scrolling-text {
      color: var(--code-viewer-text, #9ca3af);
    }

    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    @keyframes scrollHorizontal {
      0% {
        transform: translateX(100%);
      }
      100% {
        transform: translateX(-100%);
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(15px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 0.6;
        transform: scale(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.1);
      }
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .wireframe-combined-animation-secondary {
        gap: 10px;
      }

      .typewriter-icon {
        font-size: 14px;
      }

      .typewriter-text {
        font-size: 14px;
      }

      .typewriter-cursor {
        font-size: 14px;
      }

      .scrolling-container {
        max-width: 350px;
        height: 26px;
      }

      .scrolling-icon {
        font-size: 12px;
      }

      .scrolling-text {
        font-size: 12px;
      }
    }

    @media (max-width: 480px) {
      .wireframe-combined-animation-secondary {
        gap: 8px;
      }

      .typewriter-icon {
        font-size: 12px;
      }

      .typewriter-text {
        font-size: 13px;
      }

      .typewriter-cursor {
        font-size: 13px;
      }

      .scrolling-container {
        max-width: 250px;
        height: 24px;
      }

      .scrolling-icon {
        font-size: 11px;
      }

      .scrolling-text {
        font-size: 11px;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WireframeTypewriterSecondaryComponent implements OnInit, OnDestroy {
  private readonly wireframeMockDataService = inject(WireframeMockDataService);
  private readonly wireframeGenerationStateService = inject(WireframeGenerationStateService);
  private readonly themeService = inject(ThemeService);

  // Enhanced secondary text messages that provide additional context
  private readonly secondaryMessages = [
    "Analyzing user requirements and design patterns...",
    "Building responsive component architecture...",
    "Optimizing layout structure and hierarchy...",
    "Applying accessibility standards and best practices...",
    "Generating interactive wireframe elements...",
    "Finalizing design system integration...",
    "Preparing wireframe for preview..."
  ];

  // Signals for reactive state management
  private readonly typewriterState = signal<SecondaryTypewriterState>({
    currentText: '',
    isComplete: false,
    currentStep: null
  });

  private readonly scrollingState = signal<SecondaryScrollingTextState>({
    currentMessageIndex: 0,
    isActive: true
  });

  // Theme signal
  private readonly currentTheme = signal<'light' | 'dark'>(this.themeService.getCurrentTheme());

  // Computed signals for typewriter
  readonly displayText = computed(() => this.typewriterState().currentText);
  readonly isTyping = computed(() => !this.typewriterState().isComplete);

  // Computed signals for scrolling
  readonly currentMessage = computed(() => {
    const state = this.scrollingState();
    return this.secondaryMessages[state.currentMessageIndex] || this.secondaryMessages[0];
  });

  readonly isDarkTheme = computed(() => this.currentTheme() === 'dark');

  // Animation configuration
  private readonly TYPING_SPEED = 60; // milliseconds per character (slightly slower than primary)
  private readonly DELAY_BEFORE_START = 1500; // milliseconds delay after primary starts
  private readonly MESSAGE_ROTATION_INTERVAL = 3500; // milliseconds

  ngOnInit(): void {
    this.initializeTypewriterAnimation();
    this.initializeScrollingAnimation();
    this.initializeThemeSubscription();
  }

  ngOnDestroy(): void {
    // Cleanup handled by takeUntilDestroyed
  }

  private initializeThemeSubscription(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed())
      .subscribe(theme => {
        this.currentTheme.set(theme);
      });
  }

  private initializeTypewriterAnimation(): void {
    // Subscribe to current step changes from wireframe mock data service
    this.wireframeMockDataService.currentStep$
      .pipe(
        takeUntilDestroyed(),
        switchMap(step => {
          if (!step) return EMPTY;

          // Update current step
          this.typewriterState.update(state => ({
            ...state,
            currentStep: step,
            isComplete: false
          }));

          // Get secondary message based on step
          const secondaryText = this.getSecondaryMessage(step);

          // Start typewriter animation with delay
          return of(null).pipe(
            delay(this.DELAY_BEFORE_START),
            switchMap(() => this.createTypewriterAnimation(secondaryText))
          );
        })
      )
      .subscribe(text => {
        if (text !== null) {
          this.typewriterState.update(state => ({
            ...state,
            currentText: text,
            isComplete: text === this.getSecondaryMessage(state.currentStep!)
          }));
        }
      });

    // Handle generation completion
    this.wireframeGenerationStateService.isGenerating$
      .pipe(takeUntilDestroyed())
      .subscribe(isGenerating => {
        if (!isGenerating) {
          // Reset typewriter state when generation stops
          this.typewriterState.update(state => ({
            ...state,
            currentText: '',
            isComplete: true,
            currentStep: null
          }));
        }
      });
  }

  private initializeScrollingAnimation(): void {
    // Start message rotation interval with slight delay from primary
    setTimeout(() => {
      interval(this.MESSAGE_ROTATION_INTERVAL)
        .pipe(takeUntilDestroyed())
        .subscribe(() => {
          this.scrollingState.update(state => ({
            ...state,
            currentMessageIndex: (state.currentMessageIndex + 1) % this.secondaryMessages.length
          }));
        });
    }, 500); // 500ms delay to offset from primary component

    // Handle generation completion
    this.wireframeGenerationStateService.isGenerating$
      .pipe(takeUntilDestroyed())
      .subscribe(isGenerating => {
        this.scrollingState.update(state => ({
          ...state,
          isActive: isGenerating
        }));
      });
  }

  private getSecondaryMessage(step: WireframeGenerationStep): string {
    // Map step IDs to secondary messages
    const stepIndex = parseInt(step.id.split('-')[1]) - 1;
    return this.secondaryMessages[stepIndex] || this.secondaryMessages[this.secondaryMessages.length - 1];
  }

  private createTypewriterAnimation(targetText: string) {
    if (!targetText || targetText.length === 0) {
      return of('');
    }

    const chars = targetText.split('');

    return interval(this.TYPING_SPEED).pipe(
      startWith(0),
      map(index => {
        if (index >= chars.length) {
          return targetText;
        }
        return chars.slice(0, index + 1).join('');
      }),
      takeUntilDestroyed()
    );
  }
}
