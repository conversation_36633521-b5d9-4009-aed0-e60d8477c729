import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  ChangeDetectionStrategy,
  inject,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { WireframeGenerationStateService } from '../../../../services/wireframe-generation-state.service';
import { ThemeService } from '../../../../services/theme-service/theme.service';

interface TypewriterState {
  currentMessageIndex: number;
  isActive: boolean;
  isTyping: boolean;
  displayText: string;
  fullText: string;
  currentCharIndex: number;
}

@Component({
  selector: 'app-wireframe-typewriter',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="wireframe-loading-display"
         [class.dark-theme]="isDarkTheme()"
         role="status"
         [attr.aria-label]="currentMessage() + ' - Wireframe generation in progress'">

      <!-- Main Content Container -->
      <div class="content-container">

        <!-- Typewriter Message Display -->
        <div class="primary-message-container">
          <div class="primary-message"
               [class.typing]="isTyping()">
            {{ currentMessage() }}<span class="typewriter-cursor"
                                        [class.blinking]="!isTyping()"
                                        aria-hidden="true"></span>
          </div>
        </div>

      </div>
    </div>
  `,
  styles: [`
    .wireframe-loading-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
      font-family: 'Mulish', -apple-system, BlinkMacSystemFont, sans-serif;
      opacity: 0;
      animation: fadeIn 0.8s ease-in-out forwards;
      text-align: center;
      min-height: 200px;
      background:transparent !important;
    }

    .content-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      max-width: 600px;
      width: 100%;
      padding: 0 20px;
    }

    /* Primary Message Styles */
    .primary-message-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      width: 100%;
    }

    .message-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--primary-color, #8c65f7) 0%, var(--secondary-color, #e84393) 100%);
      box-shadow: 0 4px 20px rgba(140, 101, 247, 0.3);
    }

    .icon-animation {
      font-size: 24px;
      animation: pulse 2s ease-in-out infinite;
    }

    .primary-message {
      font-size: 18px;
      font-weight: 600;
      color: var(--code-viewer-text, #374151);
      letter-spacing: 0.025em;
      line-height: 1.5;
      text-align: center;
      max-width: 500px;
      animation: textFadeIn 0.5s ease-out;
      opacity: 1;
      transform: translateY(0);
    }

    .typewriter-cursor {
      font-weight: 400;
      color: var(--primary-color, #8c65f7);
      animation: blink 1s infinite;
      margin-left: 2px;
    }

    .typewriter-cursor.blinking {
      animation: blink 1s infinite;
    }

    /* Secondary Information Styles */
    .secondary-info-container {
      width: 100%;
      max-width: 450px;
    }

    .secondary-message {
      font-size: 14px;
      font-weight: 400;
      color: var(--code-viewer-text, #6b7280);
      text-align: center;
      line-height: 1.4;
      opacity: 0.8;
      transition: color 0.3s ease, opacity 0.3s ease;
      animation: textSlideIn 0.6s ease-out;
    }

    /* Progress Indicator Styles */
    .progress-indicator {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-top: 8px;
    }

    .progress-dots {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--code-viewer-text, #d1d5db);
      opacity: 0.3;
      transition: all 0.3s ease;
    }

    .dot.active {
      background: var(--primary-color, #8c65f7);
      opacity: 1;
      transform: scale(1.2);
    }

    /* Dark theme styles */
    .wireframe-loading-display.dark-theme .primary-message {
      color: var(--code-viewer-text, #e5e7eb);
    }

    .wireframe-loading-display.dark-theme .secondary-message {
      color: var(--code-viewer-text, #9ca3af);
    }

    .wireframe-loading-display.dark-theme .message-icon {
      box-shadow: 0 4px 20px rgba(140, 101, 247, 0.4);
    }

    .wireframe-loading-display.dark-theme .dot {
      background: var(--code-viewer-text, #6b7280);
    }

    .wireframe-loading-display.dark-theme .dot.active {
      background: var(--primary-color, #8c65f7);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes textFadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes textSlideIn {
      from {
        opacity: 0;
        transform: translateY(15px);
      }
      to {
        opacity: 0.8;
        transform: translateY(0);
      }
    }

    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
        opacity: 0.8;
      }
      50% {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .wireframe-loading-display {
        margin-top: 16px;
        min-height: 180px;
      }

      .content-container {
        gap: 16px;
        padding: 0 16px;
      }

      .message-icon {
        width: 50px;
        height: 50px;
      }

      .icon-animation {
        font-size: 20px;
      }

      .primary-message {
        font-size: 16px;
        max-width: 400px;
      }

      .secondary-message {
        font-size: 13px;
        max-width: 350px;
      }
    }

    @media (max-width: 480px) {
      .wireframe-loading-display {
        margin-top: 12px;
        min-height: 160px;
      }

      .content-container {
        gap: 12px;
        padding: 0 12px;
      }

      .message-icon {
        width: 45px;
        height: 45px;
      }

      .icon-animation {
        font-size: 18px;
      }

      .primary-message {
        font-size: 15px;
        max-width: 320px;
      }

      .secondary-message {
        font-size: 12px;
        max-width: 280px;
      }

      .dot {
        width: 6px;
        height: 6px;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WireframeTypewriterComponent implements OnInit, OnDestroy {
  private readonly wireframeGenerationStateService = inject(WireframeGenerationStateService);
  private readonly themeService = inject(ThemeService);

  // Subject for manual cleanup
  private readonly destroy$ = new Subject<void>();

  // Enhanced wireframe-related messages with detailed information
  private readonly wireframeMessages = [
    'Analyzing wireframe structure and layout patterns...',
    'Processing UI components and design elements...',
    'Generating responsive layout configurations...',
    'Optimizing component hierarchy and relationships...',
    'Creating interactive element specifications...',
    'Finalizing wireframe design and structure...',
    'Establishing component architecture and data flow...',
    'Applying design system tokens and variables...',
    'Building accessible navigation structures...',
    'Implementing responsive breakpoint strategies...',
    'Configuring state management patterns...',
    'Optimizing performance and loading strategies...',
    'Generating documentation and style guides...',
    'Validating cross-browser compatibility...',
    'Testing user interaction workflows...',
    'Preparing deployment configurations...'
  ];

  // Signals for reactive state management
  private readonly typewriterState = signal<TypewriterState>({
    currentMessageIndex: 0,
    isActive: true,
    isTyping: false,
    displayText: '',
    fullText: this.wireframeMessages[0],
    currentCharIndex: 0
  });

  // Theme signal
  private readonly currentTheme = signal<'light' | 'dark'>(this.themeService.getCurrentTheme());

  // Computed signals for unified typewriter system
  readonly currentMessage = computed(() => this.typewriterState().displayText);
  readonly isTyping = computed(() => this.typewriterState().isTyping);
  readonly currentMessageIndex = computed(() => this.typewriterState().currentMessageIndex);
  readonly isActive = computed(() => this.typewriterState().isActive);

  readonly isDarkTheme = computed(() => this.currentTheme() === 'dark');

  // Animation configuration
  private readonly TYPING_SPEED = 50; // milliseconds per character
  private readonly MESSAGE_ROTATION_INTERVAL = 3500; // milliseconds - time to display each message

  ngOnInit(): void {
    this.initializeTypewriterSystem();
    this.initializeThemeSubscription();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeThemeSubscription(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.currentTheme.set(theme);
      });
  }

  private initializeTypewriterSystem(): void {
    // Handle generation state changes
    this.wireframeGenerationStateService.isGenerating$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isGenerating => {
        this.typewriterState.update(state => ({
          ...state,
          isActive: isGenerating
        }));

        if (isGenerating) {
          // Start the typewriter cycle
          this.startTypewriterCycle();
        }
      });
  }

  private startTypewriterCycle(): void {
    // Start with the first message
    this.startTypingMessage(0);
  }

  private startTypingMessage(messageIndex: number): void {
    const message = this.wireframeMessages[messageIndex];

    // Reset state for new message
    this.typewriterState.update(state => ({
      ...state,
      currentMessageIndex: messageIndex,
      fullText: message,
      displayText: '',
      currentCharIndex: 0,
      isTyping: true
    }));

    // Start character-by-character typing
    this.typeCharacters(message, 0);
  }

  private typeCharacters(fullText: string, charIndex: number): void {
    if (charIndex >= fullText.length) {
      // Typing complete for this message
      this.typewriterState.update(state => ({
        ...state,
        isTyping: false
      }));

      // Wait for display duration, then move to next message
      setTimeout(() => {
        const nextIndex = (this.typewriterState().currentMessageIndex + 1) % this.wireframeMessages.length;
        this.startTypingMessage(nextIndex);
      }, this.MESSAGE_ROTATION_INTERVAL - (fullText.length * this.TYPING_SPEED));

      return;
    }

    // Add next character
    const displayText = fullText.substring(0, charIndex + 1);
    this.typewriterState.update(state => ({
      ...state,
      displayText,
      currentCharIndex: charIndex + 1
    }));

    // Schedule next character
    setTimeout(() => {
      if (this.typewriterState().isActive) {
        this.typeCharacters(fullText, charIndex + 1);
      }
    }, this.TYPING_SPEED);
  }
}
