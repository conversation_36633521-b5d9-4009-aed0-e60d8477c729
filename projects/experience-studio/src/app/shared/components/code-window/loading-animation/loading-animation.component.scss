// Common variables
$accent-color: #FF4081;
$accent-color-light: rgba(255, 64, 129, 0.2);
$accent-color-glow: rgba(255, 64, 129, 0.5);
$accent-color-shadow: rgba(255, 64, 129, 0.7);

// Base styles for the loading container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh !important;
  padding: 2rem;
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
  background: var(--code-viewer-bg);
}

// Dark theme styles
.dark-theme {
  background-color:var(--code-viewer-bg);
  color: #ffffff;

  .loader {
    --logo-color: #FF4081;
    --background: linear-gradient(
      0deg,
      rgba(50, 50, 50, 0.2) 0%,
      rgba(100, 100, 100, 0.2) 100%
    );
  }

  .ui-abstergo {
    --primary: #FF4081;
    --secondary: rgba(255, 64, 129, 0.3);
  }

  .message {
    color: #ffffff;
    text-shadow: 0 0 10px $accent-color-shadow;
  }

  &::before {
    background: linear-gradient(90deg, transparent, $accent-color-light, transparent);
  }
}

// Light theme styles
.light-theme {
  background-color: var(--code-viewer-bg);
  color: #333333;

  .loader {
    --logo-color: #6566CD;
    --background: linear-gradient(
      0deg,
      rgba(200, 200, 200, 0.2) 0%,
      rgba(230, 230, 230, 0.2) 100%
    );
  }

  .ui-abstergo {
    --primary: #6566CD;
    --secondary: rgba(101, 102, 205, 0.3);
  }

  .message {
    color: #333333;
    text-shadow: 0 0 10px rgba(101, 102, 205, 0.3);
  }

  &::before {
    background: linear-gradient(90deg, transparent, rgba(101, 102, 205, 0.1), transparent);
  }
}

// New loader styles
.loader {
  --size: 200px;
  --duration: 2s;
  height: var(--size);
  aspect-ratio: 1;
  position: relative;
  margin-bottom: 0; /* Removed bottom margin since we're using margin-top on message container */
}

.loader .box {
  position: absolute;
  background: rgba(100, 100, 100, 0.15);
  background: var(--background);
  border-radius: 50%;
  border-top: 1px solid rgba(100, 100, 100, 1);
  box-shadow: rgba(0, 0, 0, 0.3) 0px 10px 10px -0px;
  backdrop-filter: blur(5px);
  animation: ripple var(--duration) infinite ease-in-out;
}

.loader .box:nth-child(1) {
  inset: 40%;
  z-index: 99;
}

.loader .box:nth-child(2) {
  inset: 30%;
  z-index: 98;
  border-color: rgba(100, 100, 100, 0.8);
  animation-delay: 0.2s;
}

.loader .box:nth-child(3) {
  inset: 20%;
  z-index: 97;
  border-color: rgba(100, 100, 100, 0.6);
  animation-delay: 0.4s;
}

.loader .box:nth-child(4) {
  inset: 10%;
  z-index: 96;
  border-color: rgba(100, 100, 100, 0.4);
  animation-delay: 0.6s;
}

.loader .box:nth-child(5) {
  inset: 0%;
  z-index: 95;
  border-color: rgba(100, 100, 100, 0.2);
  animation-delay: 0.8s;
}

.loader .logo {
  position: absolute;
  inset: 0;
  display: grid;
  place-content: center;
  padding: 30%;
}

// Message container styles
.message-container {
  text-align: center;
  max-width: 80%;
  margin-top: 6rem;
}

// Message styles
.message {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  opacity: 0;
  animation: pulseText var(--duration, 2s) ease-in-out infinite;
  position: relative;
}

// Dots animation
.message-dots {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-top: 4px;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: dotPulse var(--duration, 2s) infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.3s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
}

/* Animation for the loading message to match the ripple effect */
@keyframes pulseText {
  0% {
    opacity: 0.3;
    transform: scale(0.95);
    letter-spacing: normal;
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
    letter-spacing: 0.5px;
  }
  100% {
    opacity: 0.3;
    transform: scale(0.95);
    letter-spacing: normal;
  }
}

/* Animation for the dots to match the ripple effect */
@keyframes dotPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

/* Add a subtle flowing animation in the background */
.loading-container::before {
  content: '';
  position: absolute;
  bottom: -50px;
  left: 0;
  width: 100%;
  height: 50px;
  animation: flow 3s linear infinite;
}

@keyframes flow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Ripple animation for the loader boxes */
@keyframes ripple {
  0% {
    transform: scale(1);
    box-shadow: rgba(0, 0, 0, 0.3) 0px 10px 10px -0px;
  }
  50% {
    transform: scale(1.3);
    box-shadow: rgba(0, 0, 0, 0.3) 0px 30px 20px -0px;
  }
  100% {
    transform: scale(1);
    box-shadow: rgba(0, 0, 0, 0.3) 0px 10px 10px -0px;
  }
}

/* Abstergo loader styles */
.ui-abstergo {
  --shadow-blur: 3px;
  --text-shadow-blur: 3px;
  --animation-duration: 2s;
  --size: 0.20;
  display: flex;
  flex-direction: column;
  align-items: center;
  scale: var(--size);
}

.abstergo-loader * {
  box-sizing: content-box;
}

.abstergo-loader {
  width: 103px;
  height: 90px;
  position: relative;
}

.abstergo-loader div {
  width: 50px;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
  border-top: 21px solid var(--primary);
  position: absolute;
  filter: drop-shadow(0 0 var(--shadow-blur) var(--secondary));
}

.abstergo-loader div:nth-child(1) {
  top: 27px;
  left: 7px;
  rotate: -60deg;
  animation: line1 var(--animation-duration) linear infinite alternate;
}

.abstergo-loader div:nth-child(2) {
  bottom: 2px;
  left: 0;
  rotate: 180deg;
  animation: line2 var(--animation-duration) linear infinite alternate;
}

.abstergo-loader div:nth-child(3) {
  bottom: 16px;
  right: -9px;
  rotate: 60deg;
  animation: line3 var(--animation-duration) linear infinite alternate;
}

@keyframes line1 {
  0%,
  40% {
    top: 27px;
    left: 7px;
    rotate: -60deg;
  }

  60%,
  100% {
    top: 22px;
    left: 14px;
    rotate: 60deg;
  }
}

@keyframes line2 {
  0%,
  40% {
    bottom: 2px;
    left: 0;
    rotate: 180deg;
  }

  60%,
  100% {
    bottom: 5px;
    left: -8px;
    rotate: 300deg;
  }
}

@keyframes line3 {
  0%,
  40% {
    bottom: 16px;
    right: -9px;
    rotate: 60deg;
  }

  60%,
  100% {
    bottom: 7px;
    right: -11px;
    rotate: 180deg;
  }
}

/* Typewriter animation styles */
.typewriter-container {
  margin-top: 16px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.typewriter-message {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 2px;
}

.typewriter-cursor {
  display: inline-block;
  width: 2px;
  height: 18px;
  background-color: var(--primary);
  margin-left: 2px;
  opacity: 1;

  &.blinking {
    animation: blink 1s infinite;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Theme-specific styles for typewriter */
.light-theme {
  .typewriter-message {
    color: var(--text-primary-light, #333);
  }

  .typewriter-cursor {
    background-color: var(--primary-light, #007bff);
  }
}

.dark-theme {
  .typewriter-message {
    color: var(--text-primary-dark, #fff);
  }

  .typewriter-cursor {
    background-color: var(--primary-dark, #4dabf7);
  }
}
