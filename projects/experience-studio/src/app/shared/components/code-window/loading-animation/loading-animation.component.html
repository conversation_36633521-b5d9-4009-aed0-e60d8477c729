<div class="loading-container" [ngClass]="theme + '-theme'">
  <div class="loader">
    <div class="box">
      <div class="logo">
        <div class="ui-abstergo">
          <div class="abstergo-loader">
            <div></div>
            <div></div>
            <div></div>
          </div>
        </div>
      </div>
    </div>
    <div class="box"></div>
    <div class="box"></div>
    <div class="box"></div>
    <div class="box"></div>
  </div>
  <div class="message-container">
    <!-- Regular message display -->
    <p *ngIf="!enableTypewriter || !isWireframeGeneration" class="message">{{ currentMessage$ | async }}</p>

    <!-- Typewriter animation for wireframe generation -->
    <div *ngIf="enableTypewriter && isWireframeGeneration" class="typewriter-container">
      <p class="typewriter-message">
        {{ typewriterText() }}
        <span class="typewriter-cursor" [class.blinking]="isTyping()">|</span>
      </p>
    </div>
  </div>
</div>
