import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { FileModel } from './code-window-file-manager.service';
import { CodeWindowCoreStateService } from './code-window-core-state.service';
import { HttpClient } from '@angular/common/http';
import { ToastService } from '../../../services/toast.service';

export interface FileOperationResult {
  success: boolean;
  files?: FileModel[];
  error?: any;
}

export interface FileDownloadOptions {
  projectId: string;
  format?: 'zip' | 'tar';
  includeNodeModules?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowFileOperationsService {
  private fileOperationInProgress$ = new BehaviorSubject<boolean>(false);
  private downloadInProgress$ = new BehaviorSubject<boolean>(false);
  private cloneInProgress$ = new BehaviorSubject<boolean>(false);
  
  // Events
  private fileProcessed$ = new Subject<FileOperationResult>();
  private downloadCompleted$ = new Subject<{ success: boolean; url?: string; error?: any }>();
  
  // Public observables
  readonly fileOperationInProgress = this.fileOperationInProgress$.asObservable();
  readonly downloadInProgress = this.downloadInProgress$.asObservable();
  readonly cloneInProgress = this.cloneInProgress$.asObservable();
  readonly fileProcessed = this.fileProcessed$.asObservable();
  readonly downloadCompleted = this.downloadCompleted$.asObservable();

  // Angular 19+ Dependency Injection
  private readonly coreStateService = inject(CodeWindowCoreStateService);
  private readonly http = inject(HttpClient);
  private readonly toastService = inject(ToastService);
  
  // Process code files from service response
  processCodeFromService(files: any[]): FileModel[] {
    this.fileOperationInProgress$.next(true);
    
    try {
      if (!files || !Array.isArray(files)) {
        throw new Error('Invalid file data received');
      }
      
      const fileModels: FileModel[] = files.map(file => ({
        name: this.getFileNameFromPath(file.path || ''),
        content: file.content || '',
        path: file.path || '',
        language: this.getLanguageFromPath(file.path || ''),
        fileName: this.getFileNameFromPath(file.path || ''),
        type: 'file'
      }));
      
      // Sort files by technology and importance
      const sortedFiles = this.sortFilesByTechnology(fileModels);
      
      // Update core state
      this.coreStateService.setFiles(sortedFiles);
      
      // Emit success event
      this.fileProcessed$.next({
        success: true,
        files: sortedFiles
      });
      
      return sortedFiles;
    } catch (error) {
      // Emit failure event
      this.fileProcessed$.next({
        success: false,
        error
      });
      
      return [];
    } finally {
      this.fileOperationInProgress$.next(false);
    }
  }
  
  // Get current code files
  getCurrentCodeFiles(): FileModel[] {
    return this.coreStateService.getCurrentFiles();
  }
  
  // Download project from server
  downloadProjectFromServer(options: FileDownloadOptions): Observable<any> {
    this.downloadInProgress$.next(true);
    
    const { projectId, format = 'zip', includeNodeModules = false } = options;
    
    const downloadUrl = `/api/projects/${projectId}/download?format=${format}&includeNodeModules=${includeNodeModules}`;
    
    return new Observable(observer => {
      this.http.get(downloadUrl, { responseType: 'blob' }).subscribe({
        next: (blob: Blob) => {
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `project-${projectId}.${format}`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
          
          this.downloadInProgress$.next(false);
          this.downloadCompleted$.next({ success: true, url });
          
          observer.next({ success: true, url });
          observer.complete();
        },
        error: (error) => {
          this.toastService.error('Error downloading project. Please try again.');

          this.downloadInProgress$.next(false);
          this.downloadCompleted$.next({ success: false, error });
          
          observer.error(error);
        }
      });
    });
  }
  
  // Clone repository
  cloneRepository(repoUrl: string): Observable<any> {
    this.cloneInProgress$.next(true);
    
    return new Observable(observer => {
      this.http.post('/api/clone', { repoUrl }).subscribe({
        next: (response: any) => {
          this.cloneInProgress$.next(false);
          observer.next(response);
          observer.complete();
        },
        error: (error) => {
          this.toastService.error('Error cloning repository. Please try again.');

          this.cloneInProgress$.next(false);
          observer.error(error);
        }
      });
    });
  }
  
  // Get language from file path
  getLanguageFromPath(path: string): string {
    if (!path) return 'plaintext';
    
    const extension = path.split('.').pop()?.toLowerCase() || '';
    
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cs': 'csharp',
      'go': 'go',
      'rb': 'ruby',
      'php': 'php',
      'swift': 'swift',
      'kt': 'kotlin',
      'rs': 'rust',
      'sh': 'shell',
      'yaml': 'yaml',
      'yml': 'yaml',
      'xml': 'xml',
      'sql': 'sql',
      'graphql': 'graphql',
      'dockerfile': 'dockerfile'
    };
    
    return languageMap[extension] || 'plaintext';
  }
  
  // Get file name from path
  private getFileNameFromPath(path: string): string {
    if (!path) return 'Unknown';
    
    const parts = path.split('/');
    return parts[parts.length - 1];
  }
  
  // Sort files by technology and importance
  sortFilesByTechnology(files: FileModel[]): FileModel[] {
    // Define file importance by extension
    const importanceOrder: { [key: string]: number } = {
      'html': 1,
      'jsx': 2,
      'tsx': 3,
      'js': 4,
      'ts': 5,
      'css': 6,
      'scss': 7,
      'json': 8,
      'md': 9
    };
    
    // Sort files by importance and then alphabetically
    return [...files].sort((a, b) => {
      const pathA = a.path || a.fileName || a.name || '';
      const pathB = b.path || b.fileName || b.name || '';

      const extA = pathA.split('.').pop()?.toLowerCase() || '';
      const extB = pathB.split('.').pop()?.toLowerCase() || '';

      const importanceA = importanceOrder[extA] || 100;
      const importanceB = importanceOrder[extB] || 100;

      // First sort by importance
      if (importanceA !== importanceB) {
        return importanceA - importanceB;
      }

      // Then sort alphabetically by path
      return pathA.localeCompare(pathB);
    });
  }
  
  // Reset service state
  reset(): void {
    this.fileOperationInProgress$.next(false);
    this.downloadInProgress$.next(false);
    this.cloneInProgress$.next(false);
  }
  


  // Generate app name for download
  generateAppNameForDownload(
    appName?: string,
    projectName?: string,
    promptData?: any
  ): string {
    if (appName) {
      return appName;
    }

    if (projectName) {
      return projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    if (promptData?.prompt) {
      const words = promptData.prompt.split(' ').slice(0, 3);
      return words.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '');
    }

    return 'generated-app';
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.fileProcessed$.complete();
    this.downloadCompleted$.complete();
  }
}
