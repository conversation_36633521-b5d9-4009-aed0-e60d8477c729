import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'exp-leftpanel',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="exp-leftpanel-header" *ngIf="hasHeader">
      <ng-content select="[exp-leftpanel-header]"></ng-content>
    </div>
    <div class="exp-leftpanel-content">
      <ng-content select="[exp-leftpanel-content]"></ng-content>
    </div>
  `,
})
export class LeftPanelComponent {
  @Input() hasHeader: boolean = false;
}
