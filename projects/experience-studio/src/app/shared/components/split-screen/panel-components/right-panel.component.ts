import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'exp-rightpanel',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="exp-rightpanel-header" *ngIf="hasHeader">
      <ng-content select="[exp-rightpanel-header]"></ng-content>
    </div>
    <div class="exp-rightpanel-content">
      <ng-content select="[exp-rightpanel-content]"></ng-content>
    </div>
  `,
})
export class RightPanelComponent {
  @Input() hasHeader: boolean = false;
}
