<div class="exp-splitscreen container-fluid p-0 d-flex flex-column">
  <div class="exp-splitscreen-container d-flex flex-grow-1">
    <!-- Left Panel -->
    <div class="exp-leftpanel d-flex flex-column" [style.width]="fixedLeftPanelWidth || defaultLeftPanelWidth">
      <ng-content select="[exp-leftpanel]"></ng-content>
    </div>

    <!-- Resizer -->
    <div *ngIf="isResizable" class="resizer" (mousedown)="onMouseDown($event)" [class.active]="isResizable"></div>

    <!-- Right Panel -->
    <div class="exp-rightpanel d-flex flex-column" [style.width]="getRightPanelWidth()">
      <ng-content select="[exp-rightpanel]"></ng-content>
    </div>
  </div>
</div>
