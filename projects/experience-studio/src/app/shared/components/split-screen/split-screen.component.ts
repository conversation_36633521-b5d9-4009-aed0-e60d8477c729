import { CommonModule } from '@angular/common';
import { Component, Input, ViewEncapsulation, ElementRef, OnInit } from '@angular/core';

@Component({
  imports: [CommonModule],
  selector: 'exp-splitscreen',
  templateUrl: './split-screen.component.html',
  styleUrls: ['./split-screen.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SplitScreenComponent implements OnInit {
  @Input() isResizable = true;
  @Input() defaultLeftPanelWidth = '50%';
  @Input() defaultRightPanelWidth = '50%';
  @Input() minWidth = '300px';
  @Input() fixedLeftPanelWidth: string | null = null;

  private isDragging = false;
  private initialX = 0;
  private initialLeftWidth = 0;
  private containerWidth = 0;
  private leftPanel!: HTMLElement;
  private rightPanel!: HTMLElement;
  private container!: HTMLElement;
  private animationFrameId: number | null = null;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    this.initializePanels();
    if (this.fixedLeftPanelWidth) {
      this.setFixedPanelWidths();
    }
  }

  private initializePanels(): void {
    this.container = this.elementRef.nativeElement.querySelector('.exp-splitscreen-container');
    this.leftPanel = this.elementRef.nativeElement.querySelector('.exp-leftpanel');
    this.rightPanel = this.elementRef.nativeElement.querySelector('.exp-rightpanel');
  }

  private setFixedPanelWidths(): void {
    if (!this.fixedLeftPanelWidth) return;

    const leftWidth = this.fixedLeftPanelWidth;
    const rightWidth = `${100 - parseFloat(this.fixedLeftPanelWidth)}%`;

    if (this.leftPanel && this.rightPanel) {
      this.leftPanel.style.width = leftWidth;
      this.rightPanel.style.width = rightWidth;
    }
  }

  onMouseDown(event: MouseEvent): void {
    if (!this.isResizable) return;

    this.isDragging = true;
    this.initialX = event.clientX;
    this.containerWidth = this.container.getBoundingClientRect().width;
    const leftPanelRect = this.leftPanel.getBoundingClientRect();
    this.initialLeftWidth = (leftPanelRect.width / this.containerWidth) * 100;

    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);

    // Prevent text selection during drag
    event.preventDefault();
  }

  private onMouseMove = (event: MouseEvent): void => {
    if (!this.isDragging) return;

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    this.animationFrameId = requestAnimationFrame(() => {
      const deltaX = event.clientX - this.initialX;
      const deltaPercentage = (deltaX / this.containerWidth) * 100;
      const newLeftWidth = this.initialLeftWidth + deltaPercentage;
      const newRightWidth = 100 - newLeftWidth;

      const minWidthPx = parseInt(this.minWidth);
      const leftPixels = (newLeftWidth / 100) * this.containerWidth;
      const rightPixels = (newRightWidth / 100) * this.containerWidth;

      if (leftPixels >= minWidthPx && rightPixels >= minWidthPx) {
        this.leftPanel.style.width = `${newLeftWidth}%`;
        this.rightPanel.style.width = `${newRightWidth}%`;
      }
    });
  };

  private onMouseUp = (): void => {
    this.isDragging = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
  };

  dockToLeft(): void {
    if (this.leftPanel && this.rightPanel) {
      this.leftPanel.style.width = '0%';
      this.rightPanel.style.width = '100%';
    }
  }

  dockToRight(): void {
    if (this.leftPanel && this.rightPanel) {
      this.leftPanel.style.width = '100%';
      this.rightPanel.style.width = '0%';
    }
  }

  parseFloatValue(value: string): number {
    return parseFloat(value);
  }

  getRightPanelWidth(): string {
    if (!this.fixedLeftPanelWidth) return this.defaultRightPanelWidth;
    return `${100 - this.parseFloatValue(this.fixedLeftPanelWidth)}%`;
  }
}
