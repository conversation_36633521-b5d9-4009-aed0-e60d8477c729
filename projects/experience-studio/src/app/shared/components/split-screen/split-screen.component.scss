//Utility classes
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 15px;
  padding-left: 15px;
}

.p-0 {
  padding: 0 !important;
}

.d-flex {
  display: flex !important;
}

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -webkit-flex-direction: column !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}

.justify-content-start {
  -webkit-box-pack: start !important;
  -webkit-justify-content: flex-start !important;
      -ms-flex-pack: start !important;
          justify-content: flex-start !important;
}

.align-items-center {
  -webkit-box-align: center !important;
  -webkit-align-items: center !important;
      -ms-flex-align: center !important;
          align-items: center !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.exp-splitscreen {
  height: 100%;
  overflow: hidden;
  user-select: none;  // Prevent text selection during drag

  .exp-splitscreen-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
  }

  .exp-leftpanel, .exp-rightpanel {
    height: 100%;
    overflow: auto;
    transition: none;  // Remove transition during drag for better performance
    will-change: width;
    min-width: 0;  // Allow panels to shrink below their content width
    flex-shrink: 0;  // Prevent automatic resizing
  }

  .resizer {
    width: 6px;
    background-color: #e0e0e0;
    cursor: col-resize;
    touch-action: none;
    z-index: 2;
    position: relative;
    flex: 0 0 auto;  // Prevent resizer from growing/shrinking

    &:hover, &.active {
      background-color: #bdbdbd;
    }

    &::after {
      content: '';
      position: absolute;
      left: -4px;
      right: -4px;
      top: 0;
      bottom: 0;
      z-index: 1;
    }
  }

  .exp-leftpanel {
    border-right: 1px solid #e0e0e0;
  }

  .docking-controls {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }
}
