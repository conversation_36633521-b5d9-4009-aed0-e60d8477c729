#main-content-container {
  margin-top: 7%;
  // margin-top: 16%;//todo uncomment for new version
  .studio-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    /* OPTIMIZED: Better spacing and alignment for smaller cards */
    align-items: stretch;
    justify-content: center;
    .studio-card {
      border: 1px solid var(--code-viewer-border) !important;
      background-color: var(--code-viewer-bg) !important;
      /* OPTIMIZED: Smooth transitions for better UX */
      transition: all 0.3s ease;
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }
      &:hover {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1); /* OPTIMIZED: Slightly reduced shadow for smaller cards */
        transform: translateY(-2px); /* Subtle lift effect */
        &::before {
          opacity: 1;
        }
        img {
          transform: scale(1.05); /* Subtle image zoom on hover */
        }
      }
      img {
        width: auto;
        height: auto;
        max-height: 140px; /* OPTIMIZED: Reduced from implicit larger size */
        object-fit: contain;
        transition: transform 0.3s ease; /* Smooth scaling on hover */
      }

      /* ENHANCED: Disabled card styles for health check failures */
      &.card-disabled {
        opacity: 0.5;
        cursor: not-allowed !important;
        pointer-events: none;
        filter: grayscale(0.3);

        /* Additional styles for permanently disabled cards */
        &.always-disabled {
          opacity: 0.6;
          cursor: not-allowed !important;
          pointer-events: none;
          filter: grayscale(0.3);
          background: repeating-linear-gradient(
            45deg,
            var(--code-viewer-bg),
            var(--code-viewer-bg) 10px,
            rgba(0, 0, 0, 0.02) 10px,
            rgba(0, 0, 0, 0.02) 20px
          ) !important;
        }
        &::before {
          display: none; /* Remove gradient border on disabled cards */
        }

        &:hover {
          box-shadow: none; /* Remove hover shadow on disabled cards */
          transform: none; /* Remove hover transform on disabled cards */
        }

        /* Disabled state for text elements */
        h2, p {
          color: var(--disabled-text-color, #999) !important;
        }

        /* Disabled state for images */
        img {
          opacity: 0.6;
          filter: grayscale(0.5);
        }
      }

      /* ENHANCED: Loading state styles during health check */
      &.card-loading {
        opacity: 0.7;
        cursor: wait;
        pointer-events: none;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 20px;
          height: 20px;
          margin: -10px 0 0 -10px;
          border: 2px solid transparent;
          border-top: 2px solid var(--primary-color, #8c65f7);
          border-radius: 50%;
          animation: card-loading-spin 1s linear infinite;
          z-index: 10;
        }
      }
    }
  }

  /* ENHANCED: Loading animation keyframes */
  @keyframes card-loading-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}



/* ENHANCED: Loading spin animation */
@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* OPTIMIZED: Responsive design for studio cards with 25% size reduction */
@media (max-width: 1024px) {
  .studio-cards-grid .studio-card {
    height: 195px; /* Reduced from 260px */
    img {
      max-height: 90px; /* Reduced from 120px */
    }
  }
}

@media (max-width: 767px) {
  .studio-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem; /* Reduced gap for mobile */
    .studio-card {
      height: 180px; /* Reduced from 240px */
      img {
        max-height: 75px; /* Reduced from 100px */
      }
    }
  }
}

@media (max-width: 480px) {
  .studio-cards-grid .studio-card {
    height: 165px; /* Reduced from 220px */
    img {
      max-height: 60px; /* Reduced from 80px */
    }
  }
}

@media (min-width: 1420px) {
  .studio-cards-grid {
    max-width: 900px; /* Reduced from 1200px for better proportions */
    .studio-card {
      /* Maintain consistent proportions on very large screens */
      max-width: 420px;
    }
  }
}

/* ENHANCED: Health check sticky message - positioned like toast in top right */
.health-check-sticky-message {
  background: transparent;
  position: fixed;
  top: 8%; // Offset to avoid overlapping with toasts
  right: 16px;
  z-index: 9998; // Just below toast container (9999)
  max-width: 400px;
  width: auto;
  pointer-events: auto;
  animation: slideInFromRight 0.3s ease-out;

  .health-message-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(8px);

    .info-icon {
      margin-right: 8px;
      display: flex;
      align-items: center;
      color: var(--warning-icon, #f39c12);
    }

    .info-text {
      flex: 1;

      p {
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
        line-height: 1.4;
        word-wrap: break-word;
      }
    }
  }

  /* Light theme specific styles */
  &.light-theme .health-message-content {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Dark theme specific styles */
  &.dark-theme .health-message-content {
    background-color: #2d2a1f;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    top: 70px; // Maintain offset from toasts on mobile
    right: 12px;
    left: 12px;
    max-width: none;

    .health-message-content {
      padding: 10px 12px;

      .info-text p {
        font-size: 0.8rem;
      }
    }
  }
}

/* Animation for health check message */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
