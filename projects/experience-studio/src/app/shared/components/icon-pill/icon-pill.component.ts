import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ElementRef,
  signal,
  HostListener,
  inject
} from '@angular/core';
import { IconsComponent } from '../icons/icons.component';

export interface IconOption {
  name: string;
  icon: string;
  value: string;
  isLocalSvg?: boolean;
}

@Component({
  selector: 'awe-icon-pill',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  templateUrl: './icon-pill.component.html',
  styleUrls: ['./icon-pill.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    'data-component': 'icon-pill-experience-studio'
  }
})
export class IconPillComponent {
  @Input() options: IconOption[] = [
    { name: 'Angular', icon: 'awe_modules', value: 'angular' },
    { name: 'React', icon: 'awe_react', value: 'react' },
    { name: 'Vue', icon: 'awe_toggled_button', value: 'vue' }
  ];

  @Input() set selectedOption(value: IconOption | null) {
    if (value) {
      this._selectedOption = value;
    } else if (this.options.length > 0) {
      this._selectedOption = this.options[0];
    }
  }

  get selectedOption(): IconOption {
    return this._selectedOption;
  }

  @Output() selectionChange = new EventEmitter<IconOption>();

  private _selectedOption: IconOption = { name: 'Angular', icon: 'awe_modules', value: 'angular' };

  // Angular 19+ Dependency Injection
  private readonly elementRef = inject(ElementRef);

  isHovered = signal(false);
  isDropdownOpen = signal(false);

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Close dropdown when clicking outside
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isDropdownOpen.set(false);
    }
  }

  onMouseEnter(): void {
    this.isHovered.set(true);
  }

  onMouseLeave(): void {
    // Always set isHovered to false when mouse leaves
    this.isHovered.set(false);
    // Close dropdown after a small delay to allow moving to dropdown
    setTimeout(() => {
      if (!this.isMouseOverDropdown) {
        this.isDropdownOpen.set(false);
      }
    }, 100);
  }

  // Track if mouse is over dropdown
  isMouseOverDropdown = false;

  onDropdownMouseEnter(): void {
    this.isMouseOverDropdown = true;
  }

  onDropdownMouseLeave(): void {
    this.isMouseOverDropdown = false;
    this.isDropdownOpen.set(false);
    this.isHovered.set(false);
  }

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isDropdownOpen.update(value => !value);
  }

  selectOption(option: IconOption, event: Event): void {
    event.stopPropagation();
    this._selectedOption = option;
    this.selectionChange.emit(option);
    this.isDropdownOpen.set(false);
    this.isHovered.set(false);
  }
}
