// CSS Variables for theming - Updated to match provided HTML design
:root {
  // Light theme variables
  --analyzing-bg-color: #f4f7fa;
  --analyzing-text-color: #333;
  --analyzing-text-secondary: #555;
  --analyzing-border-color: #d1d9e6;
  --analyzing-preview-bg: #ffffff;
  --analyzing-header-color: #e3eaf3;
  --analyzing-body-bg: #f4f7fa;
  --analyzing-sidebar-color: #f0f3f8;
  --analyzing-footer-color: #e3eaf3;
  --analyzing-spinner-color: #6d6d6d;
  --analyzing-mask-gradient: linear-gradient(to right, rgba(244, 247, 250, 0), rgba(244, 247, 250, 1) 15%, rgba(244, 247, 250, 1) 85%, rgba(244, 247, 250, 0));
}

.analyzing-layout-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
  overflow: hidden;
  background: none !important;
  border-radius: 12px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // Light theme
  &.light-theme {
    --analyzing-bg-color: #f4f7fa;
    --analyzing-text-color: #333;
    --analyzing-text-secondary: #555;
    --analyzing-border-color: #d1d9e6;
    --analyzing-preview-bg: #ffffff;
    --analyzing-header-color: #e3eaf3;
    --analyzing-body-bg: #f4f7fa;
    --analyzing-sidebar-color: #f0f3f8;
    --analyzing-footer-color: #e3eaf3;
    --analyzing-spinner-color: #6d6d6d;
    --analyzing-mask-gradient: linear-gradient(to right, rgba(244, 247, 250, 0), rgba(244, 247, 250, 1) 15%, rgba(244, 247, 250, 1) 85%, rgba(244, 247, 250, 0));
  }

  // Dark theme
  &.dark-theme {
    --analyzing-bg-color: #1a1c23;
    --analyzing-text-color: #e0e0e0;
    --analyzing-text-secondary: #aaa;
    --analyzing-border-color: #3a3f4c;
    --analyzing-preview-bg: #20222a;
    --analyzing-header-color: #2a2d37;
    --analyzing-body-bg: #1a1c23;
    --analyzing-sidebar-color: #252831;
    --analyzing-footer-color: #2a2d37;
    --analyzing-spinner-color: #b0b0b0;
    --analyzing-mask-gradient: linear-gradient(to right, rgba(26, 28, 35, 0), rgba(26, 28, 35, 1) 15%, rgba(26, 28, 35, 1) 85%, rgba(26, 28, 35, 0));
  }
}

// Header Section - Updated to match HTML design
.analyzing-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2.5rem;
  text-align: center;

  .analyzing-title {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--analyzing-text-color);
    margin: 0;
  }

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--analyzing-border-color);
    border-top-color: var(--analyzing-spinner-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Carousel Container - Updated to match HTML design
.carousel-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  mask-image: var(--analyzing-mask-gradient);
  -webkit-mask-image: var(--analyzing-mask-gradient);
  flex: 1;
}

.carousel-track {
  display: flex;
  gap: 20px;
  animation: scroll 40s linear infinite;
  will-change: transform;
  height: 100%;
  width: max-content;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-25%);
  }
}

// Carousel Items - Updated to match HTML design
.carousel-item {
  flex-shrink: 0;
  width: 160px;
  height: 180px;
  background: var(--analyzing-preview-bg);
  border: 1px solid var(--analyzing-border-color);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
}

.mini-layout-preview {
  height: 90px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  border: 1px solid var(--analyzing-border-color);
  padding: 4px;
  border-radius: 4px;
}

.layout-name {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--analyzing-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Mini Layout Elements - Updated to match HTML design
.mini-header,
.mini-footer {
  height: 12px;
  flex-shrink: 0;
  background-color: var(--analyzing-header-color);
  border-radius: 2px;
}

.mini-footer {
  margin-top: auto;
  background-color: var(--analyzing-footer-color);
}

.mini-body {
  flex-grow: 1;
  background-color: var(--analyzing-body-bg);
  border-radius: 2px;
}

.mini-content {
  flex-grow: 1;
  display: flex;
  gap: 4px;
}

.mini-sidebar {
  width: 20px;
  flex-shrink: 0;
  background-color: var(--analyzing-sidebar-color);
  border-radius: 2px;
}

// Layout-specific containers - Updated for new structure
.mini-hlsb-content,
.mini-hlsbf-content,
.mini-hbrs-content,
.mini-hbrsf-content,
.mini-hlsbrs-content,
.mini-hlsbrsf-content {
  flex-grow: 1;
  display: flex;
  gap: 4px;
}

// Responsive design
@media (max-width: 768px) {
  .analyzing-layout-container {
    padding: 16px;
    min-height: 300px;
  }
  
  .analyzing-header .analyzing-title {
    font-size: 20px;
  }
  
  .carousel-container {
    height: 200px;
  }
  
  .carousel-item {
    width: 160px;
  }
  
  .mini-layout-preview {
    height: 160px;
  }
}

@media (max-width: 480px) {
  .analyzing-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .carousel-item {
    width: 140px;
  }
  
  .mini-layout-preview {
    height: 140px;
  }
}
