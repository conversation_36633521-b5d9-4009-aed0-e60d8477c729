import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ElementRef,
  signal,
  HostListener,
  ViewChild,
  inject,
  ChangeDetectorRef
} from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { IconsComponent } from '../icons/icons.component';

export interface FileAttachOption {
  name: string;
  icon: string;
  value: string;
}

export interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

type IconColor = 'action' | 'danger' | 'disable' | 'neutralIcon' | 'success' | 'warning' | 'whiteIcon' | 'blue' | '';

@Component({
  selector: 'exp-file-attach-pill',
  standalone: true,
  imports: [CommonModule, IconsComponent, MatTooltipModule],
  templateUrl: './file-attach-pill.component.html',
  styleUrls: ['./file-attach-pill.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    '[class.theme-dark]': 'currentTheme === "dark"',
    '[class.theme-light]': 'currentTheme === "light"'
  }
})
export class FileAttachPillComponent {
  @Input() options: FileAttachOption[] = [
    { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
    // { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
    // { name: 'From URL', icon: 'awe_link', value: 'url' }
  ];

  @Input() mainIcon = 'awe_attach_file';
  @Input() mainText = 'Attach Ref Img';
  @Input() currentTheme: 'light' | 'dark' = 'light';
  @Input() fileType: 'image' | 'document' | 'mixed' = 'image'; // New input to determine file type

  @Output() optionSelected = new EventEmitter<FileAttachOption>();
  @Output() filesSelected = new EventEmitter<File[]>();
  @Output() fileRemoved = new EventEmitter<string>();

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  isHovered = signal(false);
  isDropdownOpen = signal(false);

  // File validation properties
  readonly acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  readonly acceptedDocumentTypes = ['text/plain'];
  @Input() maxAllowedFiles = 1;
  readonly maxFileSize = 5 * 1024 * 1024; // 5MB in bytes

  selectedFiles: SelectedFile[] = [];
  fileError = signal('');

  // Angular 19+ Dependency Injection
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly elementRef = inject(ElementRef);

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isDropdownOpen.set(false);
    }
  }

  onMouseEnter(): void {
    this.isHovered.set(true);
  }

  onMouseLeave(): void {
    // Always set isHovered to false when mouse leaves
    this.isHovered.set(false);
    // Close dropdown after a small delay to allow moving to dropdown
    setTimeout(() => {
      if (!this.isMouseOverDropdown) {
        this.isDropdownOpen.set(false);
      }
    }, 100);
  }

  // Track if mouse is over dropdown
  isMouseOverDropdown = false;

  onDropdownMouseEnter(): void {
    this.isMouseOverDropdown = true;
  }

  onDropdownMouseLeave(): void {
    this.isMouseOverDropdown = false;
    this.isDropdownOpen.set(false);
    this.isHovered.set(false);
  }

  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isDropdownOpen.update(value => !value);
  }

  selectOption(option: FileAttachOption, event: Event): void {
    event.stopPropagation();
    this.optionSelected.emit(option);
    this.isDropdownOpen.set(false);
    this.isHovered.set(false);

    // Trigger file upload if computer option is selected
    if (option.value === 'computer') {
      this.handleFileUpload();
    }
  }

  handleFileUpload(): void {
    if (this.isMaxFilesReached()) {
      const fileTypeText = this.fileType === 'image' ? 'image' : 'document';
      this.fileError.set(`Only ${this.maxAllowedFiles} ${fileTypeText} can be uploaded at a time`);
      this.cdr.detectChanges();
      return;
    }
    this.fileInput.nativeElement.click();
  }

  validateFile(file: File): boolean {
    // Check if it's actually a file and not a folder
    if (!file.type) {
      this.fileError.set('Folders cannot be uploaded');
      return false;
    }

    // Check file type based on the current file type setting
    if (this.fileType === 'image') {
      if (!this.acceptedImageTypes.includes(file.type)) {
        this.fileError.set('Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed');
        return false;
      }
    } else if (this.fileType === 'document') {
      // Strict validation for .txt files - check both MIME type and file extension
      const isValidMimeType = this.acceptedDocumentTypes.includes(file.type);
      const isValidExtension = file.name.toLowerCase().endsWith('.txt');

      if (!isValidMimeType && !isValidExtension) {
        this.fileError.set('Only .txt files are allowed');
        return false;
      }

      // Additional check: if MIME type is not text/plain but extension is .txt, still allow it
      // This handles cases where the browser doesn't set the correct MIME type
      if (!isValidExtension) {
        this.fileError.set('Only .txt files are allowed');
        return false;
      }
    } else if (this.fileType === 'mixed') {
      // Mixed type accepts both images and .txt files (for wireframes)
      const isValidImage = this.acceptedImageTypes.includes(file.type);
      const isValidDocument = this.acceptedDocumentTypes.includes(file.type) || file.name.toLowerCase().endsWith('.txt');

      if (!isValidImage && !isValidDocument) {
        this.fileError.set('Only image files (JPEG, PNG, GIF, WEBP, SVG) or .txt files are allowed');
        return false;
      }
    }

    // Check file size
    if (file.size > this.maxFileSize) {
      this.fileError.set('File size must be less than 5MB');
      return false;
    }

    this.fileError.set('');
    return true;
  }

  isMaxFilesReached(): boolean {
    return this.selectedFiles.length >= this.maxAllowedFiles;
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      // Check if max files already reached
      if (this.isMaxFilesReached()) {
        const fileTypeText = this.fileType === 'image' ? 'image' : 'document';
        this.fileError.set(`Only ${this.maxAllowedFiles} ${fileTypeText} can be uploaded at a time`);
        this.cdr.detectChanges();
        input.value = ''; // Reset input
        return;
      }

      const validFiles: File[] = [];

      Array.from(input.files).forEach(file => {
        if (this.validateFile(file)) {
          validFiles.push(file);
        }
      });

      if (validFiles.length > 0) {
        // Only take the first valid file if multiple are selected
        const filesToAdd = validFiles.slice(0, this.maxAllowedFiles - this.selectedFiles.length);

        const newFiles = filesToAdd.map(file => {
          const url = URL.createObjectURL(file);
          return {
            id: Math.random().toString(36).substring(2, 11),
            name: file.name,
            url: url,
            type: file.type
          };
        });

        this.selectedFiles = [...this.selectedFiles, ...newFiles];
        this.filesSelected.emit(filesToAdd);
      }
      this.cdr.detectChanges();
    }
    input.value = ''; // Reset input
  }

  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    this.fileRemoved.emit(fileId);
    this.cdr.detectChanges();
  }

  get tooltipText(): string {
    if (this.fileType === 'image') {
      return `Upload images (JPEG, PNG, GIF, WEBP, SVG)\nMax size: 5MB\nMax files: ${this.maxAllowedFiles}`;
    } else if (this.fileType === 'document') {
      return `Upload (.txt) files only\nText files with .txt extension\nMax size: 5MB\nMax files: ${this.maxAllowedFiles}`;
    } else if (this.fileType === 'mixed') {
      return `Image, docs from Computer\nImages (JPEG, PNG, GIF, WEBP, SVG) or .txt files\n1 image + 1 text file allowed\nMax size: 5MB per file`;
    } else {
      return `Upload files\nMax size: 5MB\nMax files: ${this.maxAllowedFiles}`;
    }
  }

  /**
   * Get the file input accept attribute based on file type
   * Provides strict filtering for file dialog
   */
  getFileInputAccept(): string {
    if (this.fileType === 'image') {
      return 'image/*';
    } else if (this.fileType === 'document') {
      // Strict .txt file filtering - only accept text/plain MIME type and .txt extension
      return '.txt,text/plain';
    } else if (this.fileType === 'mixed') {
      // Mixed type accepts both images and .txt files
      return 'image/*,.txt,text/plain';
    }
    return '*/*'; // Fallback
  }

  get iconColor(): IconColor {
    return this.currentTheme === 'dark' ? 'whiteIcon' : 'neutralIcon';
  }
}
