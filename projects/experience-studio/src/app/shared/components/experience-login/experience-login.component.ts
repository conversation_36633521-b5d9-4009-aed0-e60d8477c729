import { Component, OnInit, OnDestroy, signal, inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { AuthService } from '@shared/auth/services/auth.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { createLogger } from '../../utils/logger';

export interface SavedAccount {
  email: string;
  profilePic?: string;
  isSelected?: boolean;
}

@Component({
  selector: 'app-experience-login',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
  ],
  templateUrl: './experience-login.component.html',
  styleUrls: ['./experience-login.component.scss'],
})
export class ExperienceLoginComponent implements OnInit, OnDestroy {
  isLoading = signal(false);
  errorMessage = signal<string | null>(null);
  showRouteLoader = signal(false);



  private authService = inject(AuthService);
  private tokenStorage = inject(TokenStorageService);
  private router = inject(Router);
  private readonly logger = createLogger('ExperienceLoginComponent');

  ngOnInit(): void {
    // Component initialization
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  onGetStarted(): void {
    this.isLoading.set(true);
    this.errorMessage.set(null);

    this.authService.loginSSO().subscribe({
      next: () => {
        // Show route loader while navigating
        this.showRouteLoader.set(true);

        // Simulate loading time for next page data
        setTimeout(() => {
          this.isLoading.set(false);
          const redirectUrl = this.authService.getPostLoginRedirectUrl();
          this.router.navigate([redirectUrl]).then(() => {
            // Hide route loader after navigation
            setTimeout(() => {
              this.showRouteLoader.set(false);
            }, 1000);
          });
        }, 2000);
      },
      error: (error) => {
        this.logger.error('Login failed:', error);
        this.errorMessage.set('Failed to initiate login. Please try again.');
        this.isLoading.set(false);
      },
    });
  }
}
