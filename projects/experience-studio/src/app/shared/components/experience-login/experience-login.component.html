<div class="experience-login-container">
  <!-- Loading Overlay for Route Transitions -->
  <div class="loading-overlay" *ngIf="showRouteLoader()">
    <div class="route-loader">
      <div class="experience-loader">
        <!-- Hidden SVG definitions -->
        <svg height="0" width="0" viewBox="0 0 64 64" class="absolute">
          <defs xmlns="http://www.w3.org/2000/svg">
            <linearGradient gradientUnits="userSpaceOnUse" y2="2" x2="0" y1="62" x1="0" id="gradient1">
              <stop stop-color="#667eea"></stop>
              <stop stop-color="#764ba2" offset="1"></stop>
            </linearGradient>
            <linearGradient gradientUnits="userSpaceOnUse" y2="0" x2="0" y1="64" x1="0" id="gradient2">
              <stop stop-color="#f093fb"></stop>
              <stop stop-color="#f5576c" offset="1"></stop>
              <animateTransform repeatCount="indefinite" keySplines=".42,0,.58,1;.42,0,.58,1;.42,0,.58,1;.42,0,.58,1" keyTimes="0; 0.25; 0.5; 0.75; 1" dur="4s" values="0 32 32;-90 32 32;-180 32 32;-270 32 32;-360 32 32" type="rotate" attributeName="gradientTransform"></animateTransform>
            </linearGradient>
            <linearGradient gradientUnits="userSpaceOnUse" y2="2" x2="0" y1="62" x1="0" id="gradient3">
              <stop stop-color="#4facfe"></stop>
              <stop stop-color="#00f2fe" offset="1"></stop>
            </linearGradient>
          </defs>
        </svg>

        <!-- EXPERIENCE text -->
        <div class="word-container">
          <!-- E -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient1)"
                  d="M 10 10 L 10 70 M 10 10 L 60 10 M 10 40 L 50 40 M 10 70 L 60 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- X -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient2)"
                  d="M 10 10 L 60 70 M 60 10 L 10 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- P -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient3)"
                  d="M 10 10 L 10 70 M 10 10 L 50 10 Q 60 10 60 20 L 60 30 Q 60 40 50 40 L 10 40"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- E -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient1)"
                  d="M 10 10 L 10 70 M 10 10 L 60 10 M 10 40 L 50 40 M 10 70 L 60 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- R -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient2)"
                  d="M 10 10 L 10 70 M 10 10 L 50 10 Q 60 10 60 20 L 60 30 Q 60 40 50 40 L 10 40 M 30 40 L 60 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- I -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="30" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient3)"
                  d="M 10 10 L 50 10 M 30 10 L 30 70 M 10 70 L 50 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- E -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient1)"
                  d="M 10 10 L 10 70 M 10 10 L 60 10 M 10 40 L 50 40 M 10 70 L 60 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- N -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient2)"
                  d="M 10 10 L 10 70 M 10 10 L 60 70 M 60 10 L 60 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- C -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient3)"
                  d="M 60 20 Q 60 10 50 10 L 20 10 Q 10 10 10 20 L 10 60 Q 10 70 20 70 L 50 70 Q 60 70 60 60"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- E -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient1)"
                  d="M 10 10 L 10 70 M 10 10 L 60 10 M 10 40 L 50 40 M 10 70 L 60 70"
                  class="dash" pathLength="360"></path>
          </svg>
        </div>

        <!-- Space -->
        <div class="word-space"></div>

        <!-- STUDIO text -->
        <div class="word-container">
          <!-- S -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient2)"
                  d="M 60 20 Q 60 10 50 10 L 20 10 Q 10 10 10 20 Q 10 30 20 30 L 50 30 Q 60 30 60 40 L 60 60 Q 60 70 50 70 L 20 70 Q 10 70 10 60"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- T -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient3)"
                  d="M 10 10 L 60 10 M 35 10 L 35 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- U -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient1)"
                  d="M 10 10 L 10 60 Q 10 70 20 70 L 50 70 Q 60 70 60 60 L 60 10"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- D -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient2)"
                  d="M 10 10 L 10 70 M 10 10 L 40 10 Q 60 10 60 30 L 60 50 Q 60 70 40 70 L 10 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- I -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="30" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient3)"
                  d="M 10 10 L 50 10 M 30 10 L 30 70 M 10 70 L 50 70"
                  class="dash" pathLength="360"></path>
          </svg>

          <!-- O (spinning circle) -->
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 80 80" height="64" width="40" class="letter">
            <path stroke-linejoin="round" stroke-linecap="round" stroke-width="8" stroke="url(#gradient2)"
                  d="M 40 40 m 0 -25 a 25 25 0 1 1 0 50 a 25 25 0 1 1 0 -50"
                  class="spin" pathLength="360"></path>
          </svg>
        </div>
      </div>
      <p class="loading-text">Loading your experience...</p>
    </div>
  </div>

  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-content">
      <div class="hero-header">
        <h1 class="hero-title">Experience Studio</h1>

        <div class="animated-text-card">
          <div class="animated-loader">
            <p>Generate</p>
            <div class="words">
              <span class="word">Wireframe</span>
              <span class="word">Application</span>
            </div>
          </div>
        </div>
      </div>

      <p class="hero-description">
        Experience Studio accelerates your entire design and development
        lifecycle with AI agents. Build interfaces, prototypes,
        and digital experiences, all in one place.
      </p>

      <button
        class="cta-button"
        (click)="onGetStarted()"
        [disabled]="isLoading()"
      >
        <div class="bubble-layer bubble-1"></div>
        <div class="bubble-layer bubble-2"></div>
        <div class="bubble-layer bubble-3"></div>
        <div class="bubble-layer bubble-4"></div>
        <div class="bubble-layer bubble-5"></div>
        <div class="bubble-layer bubble-6"></div>
        <div class="bubble-layer bubble-7"></div>
        <span>{{ isLoading() ? 'Loading...' : 'Try Experience Studio' }}</span>
      </button>

      <!-- Error Message -->
      <div *ngIf="errorMessage()" class="error-message">
        {{ errorMessage() }}
      </div>
    </div>
  </div>
</div>
