@import '../../../../../public/assets/styles/variables';

.experience-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(240, 147, 251, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 2rem;
  color: #1e293b;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.02) 50%, transparent 70%),
      linear-gradient(-45deg, transparent 30%, rgba(240, 147, 251, 0.02) 50%, transparent 70%);
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
  }
}

// Loading Overlay for Route Transitions
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .route-loader {
    text-align: center;
    max-width: 600px;
    padding: 2rem;

    .loading-text {
      margin-top: 2rem;
      font-size: 1.1rem;
      color: #667eea;
      font-weight: 600;
      animation: pulse 2s ease-in-out infinite;
      line-height: 1.5;
    }

    .loading-description {
      margin-top: 1rem;
      font-size: 1.1rem;
      color: #64748b;
      font-weight: 500;
      line-height: 1.6;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

.experience-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.75rem;
  max-width: 1000px;
  margin-bottom: 1rem;
}

.word-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.word-space {
  width: 2rem;
}

.letter {
  display: inline-block;
  margin: 0 0.2rem;
  height: 60px;
  width: auto;
}

.absolute {
  position: absolute;
}

// Hero Section
.hero-section {
  text-align: center;
  max-width: 800px;
  position: relative;
  z-index: 2;

  .hero-content {
    opacity: 1;
    transform: translateY(0);
  }

  .hero-header {
    margin-bottom: 2rem;
  }

  .hero-title {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 800;
    line-height: 1.1;
    color: #1e293b;
    letter-spacing: -0.02em;
    text-align: center;
  }

  .animated-text-card {
    --bg-color: transparent;
    background-color: transparent;
    padding: 0.5rem 1rem;
    border-radius: 1.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    width: fit-content;
  }

  .animated-loader {
    color: #64748b;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    box-sizing: content-box;
    height: 2.5rem;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    border-radius: 8px;

    p {
      margin: 0;
      color: #475569;
      white-space: nowrap;
    }
  }

  .words {
    overflow: hidden;
    position: relative;
    height: 2.5rem;
    width: auto;
    min-width: 12rem;
    max-width: 15rem;
    display: block;
  }

  .words::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      transparent 20%,
      transparent 80%,
      transparent 100%
    );
    z-index: 20;
    pointer-events: none;
  }

  .word {
    display: flex;
    padding-left: 0.7rem;
    height: 2.5rem;
    line-height: 2.5rem;
    color: #667eea;
    font-weight: 600;
    white-space: nowrap;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    animation: wordSlide 3s infinite ease-in-out;
    opacity: 0;
    transform: translateY(100%);
  }

  .word:nth-child(1) {
    animation-delay: 0s;
  }

  .word:nth-child(2) {
    animation-delay: 1.5s;
  }

  @keyframes wordSlide {
    0% {
      transform: translateY(100%);
      opacity: 0;
      filter: blur(3px);
    }

    15% {
      transform: translateY(0);
      opacity: 1;
      filter: blur(0px);
    }

    50% {
      transform: translateY(0);
      opacity: 1;
      filter: blur(0px);
    }

    65% {
      transform: translateY(-100%);
      opacity: 0;
      filter: blur(3px);
    }

    100% {
      transform: translateY(-100%);
      opacity: 0;
      filter: blur(3px);
    }
  }



  .hero-description {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    line-height: 1.6;
    color: #64748b;
    margin: 0 auto 3rem auto;
    max-width: 600px;
    font-weight: 500;
  }
}

// Logo Section
.logo-section {
  text-align: center;
  margin-bottom: 4rem;

  .logo-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    min-height: 160px;

    .ripple-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 140px;
      height: 140px;
      pointer-events: none;

      .ripple {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border: 2px solid;
        border-radius: 50%;
        opacity: 0;
        animation: ripple-animation 4s infinite ease-in-out;

        &.ripple-1 {
          border-color: #667eea;
          animation-delay: 0s;
        }

        &.ripple-2 {
          border-color: #764ba2;
          animation-delay: 1.3s;
        }

        &.ripple-3 {
          border-color: #f093fb;
          animation-delay: 2.6s;
        }
      }
    }

    .experience-logo {
      width: 72px;
      height: 72px;
      position: relative;
      z-index: 10;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .experience-title {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 1.5rem 0;
    letter-spacing: -0.03em;
    line-height: 1.1;
  }

  .experience-subtitle {
    font-size: 1.1rem;
    color: #718096;
    margin: 0;
    font-weight: 500;
    letter-spacing: 0.01em;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
  }
}

// Get Started Section
.get-started-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

// CTA Button
.cta-button {
  position: relative;
  padding: 16px 48px;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50px;
  cursor: pointer;
  overflow: hidden;
  display: inline-block;
  z-index: 1;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.1);

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
  }

  span {
    position: relative;
    z-index: 15;
    font-weight: 700;
  }

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #5a6fd8 0%, #6b5b95 100%);
    box-shadow:
      0 12px 40px rgba(102, 126, 234, 0.4),
      0 6px 20px rgba(0, 0, 0, 0.15);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &::before {
    content: "";
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    border-radius: inherit;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover:not(:disabled)::before {
    opacity: 1;
  }
}

// Bubble Layer Styles
.bubble-layer {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  filter: blur(10px);
  z-index: 0;
}

.bubble-1 {
  background: #ff007f;
  top: -20%;
  left: -10%;
  animation: moveUpRight 6s ease-in-out infinite;
}

.bubble-2 {
  background: #ff6a00;
  top: 0%;
  left: 10%;
  animation: moveDownLeft 5s ease-in-out infinite;
  animation-delay: 1s;
}

.bubble-3 {
  background: #ffcc00;
  top: 20%;
  left: 50%;
  animation: moveRight 4s ease-in-out infinite;
  animation-delay: 2s;
}

.bubble-4 {
  background: #00fff0;
  top: -20%;
  left: 70%;
  animation: moveUpLeft 7s ease-in-out infinite;
  animation-delay: 3s;
}

.bubble-5 {
  background: #9d00ff;
  top: 30%;
  left: -10%;
  animation: moveDownRight 3s ease-in-out infinite;
  animation-delay: 4s;
}

.bubble-6 {
  background: #ff007f;
  top: -10%;
  left: 30%;
  animation: moveLeft 8s ease-in-out infinite;
  animation-delay: 0.5s;
}

.bubble-7 {
  background: #ff6a00;
  top: 40%;
  left: 60%;
  animation: moveUp 6s ease-in-out infinite;
  animation-delay: 1.5s;
}

// Error Message
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #dc2626;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-size: 0.95rem;
  text-align: center;
  font-weight: 500;
  margin-top: 1.5rem;
  backdrop-filter: blur(10px);
  
}

// Ripple Animation
@keyframes ripple-animation {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  50% {
    width: 120px;
    height: 120px;
    opacity: 0.7;
  }
  100% {
    width: 140px;
    height: 140px;
    opacity: 0;
  }
}

// Loader Animations
.dash {
  animation: dashArray 2s ease-in-out infinite,
    dashOffset 2s linear infinite;
}

.spin {
  animation: spinDashArray 2s ease-in-out infinite,
    spin 4s ease-in-out infinite,
    dashOffset 2s linear infinite;
  transform-origin: center;
}

@keyframes dashArray {
  0% {
    stroke-dasharray: 0 1 359 0;
  }
  50% {
    stroke-dasharray: 0 359 1 0;
  }
  100% {
    stroke-dasharray: 359 1 0 0;
  }
}

@keyframes spinDashArray {
  0% {
    stroke-dasharray: 270 90;
  }
  50% {
    stroke-dasharray: 0 360;
  }
  100% {
    stroke-dasharray: 270 90;
  }
}

@keyframes dashOffset {
  0% {
    stroke-dashoffset: 365;
  }
  100% {
    stroke-dashoffset: 5;
  }
}

@keyframes spin {
  0% {
    rotate: 0deg;
  }
  25% {
    rotate: 90deg;
  }
  50% {
    rotate: 180deg;
  }
  75% {
    rotate: 270deg;
  }
  100% {
    rotate: 360deg;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

// Bubble Animations
@keyframes moveUpRight {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(100%, -100%);
  }
  50% {
    transform: translate(-50%, 50%);
  }
  75% {
    transform: translate(50%, -50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes moveDownLeft {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-100%, 100%);
  }
  50% {
    transform: translate(50%, -50%);
  }
  75% {
    transform: translate(-50%, 50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes moveRight {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(100%, 0);
  }
  50% {
    transform: translate(-100%, 50%);
  }
  75% {
    transform: translate(50%, -50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes moveUpLeft {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-100%, -100%);
  }
  50% {
    transform: translate(50%, 50%);
  }
  75% {
    transform: translate(-50%, -50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes moveDownRight {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(100%, 100%);
  }
  50% {
    transform: translate(-50%, -50%);
  }
  75% {
    transform: translate(50%, 50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes moveLeft {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-100%, 0);
  }
  50% {
    transform: translate(100%, -50%);
  }
  75% {
    transform: translate(-50%, 50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes moveUp {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(0, -100%);
  }
  50% {
    transform: translate(50%, 50%);
  }
  75% {
    transform: translate(-50%, -50%);
  }
  100% {
    transform: translate(0, 0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .experience-login-container {
    padding: 1rem;
  }

  .hero-section {
    .hero-title {
      font-size: clamp(2.5rem, 10vw, 4rem);
      gap: 0.3em;
    }

    .hero-description {
      font-size: clamp(1rem, 3vw, 1.2rem);
      margin-bottom: 2.5rem;
    }
  }

  .cta-button {
    padding: 14px 40px;
    font-size: 16px;

    .bubble-layer {
      width: 120px;
      height: 120px;
    }
  }

  .experience-loader {
    flex-direction: column;
    gap: 1rem;

    .letter {
      height: 48px;
    }
  }

  .word-container {
    gap: 0.1rem;
  }
}

@media (max-width: 480px) {
  .experience-login-container {
    padding: 0.5rem;
  }

  .hero-section {
    .hero-title {
      font-size: clamp(2rem, 12vw, 3rem);
      gap: 0.3em;
    }

    .hero-description {
      font-size: clamp(0.9rem, 4vw, 1.1rem);
      margin-bottom: 2rem;
    }
  }

  .cta-button {
    padding: 12px 32px;
    font-size: 15px;

    .bubble-layer {
      width: 100px;
      height: 100px;
    }
  }

  .experience-loader {
    .letter {
      height: 40px;
      width: auto;
    }

    .word-space {
      width: 1rem;
    }
  }
}

@keyframes backgroundShift {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
