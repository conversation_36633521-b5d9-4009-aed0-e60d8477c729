.profile-icon {
  height: 2.5rem;
  width: 2.5rem;

  &.profile-initials {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #a259c6, #f76b6a);
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  &.profile-disabled {
    cursor: default;
    opacity: 0.7;
    filter: grayscale(20%);
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
      transform: none;
    }
  }
}

.profile-flyout {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  background: #fff;
  border-radius: 1rem;
  z-index: 4000;
  color: #222;
}

.gradient-button {
  ::ng-deep button {
    width: 100% !important;
    background: linear-gradient(90deg, #a259c6 0%, #f76b6a 100%);
    font-weight: 600;
    &:hover {
      background: linear-gradient(90deg, #f76b6a 0%, #a259c6 100%);
    }
  }
}

.profile-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;

  &.profile-initials {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #a259c6, #f76b6a);
    color: white;
    font-weight: 600;
    font-size: 1rem;
  }
}

::ng-deep .profile-name .heading.s2 {
  font-size: 1.1rem !important;
}

::ng-deep .profile-email .heading.s2 {
  font-size: 1rem !important;
  color: #888;
}

::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}
