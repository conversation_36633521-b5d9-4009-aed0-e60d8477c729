import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef, inject,
  OnChanges,
  SimpleChanges,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonComponent, HeaderComponent } from '@awe/play-comp-library';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AppConstants } from '../../appConstants';
import { ObserverManager } from '../../utils/subscription-management.util';
import { createLogger } from '../../utils/logger';
import { ToastService } from '../../services/toast.service';
import { AuthService, CentralizedRedirectService, TokenStorageService } from '@shared/index';
import { ProfileModalComponent } from '../profile-modal/profile-modal.component';

@Component({
  selector: 'app-nav-header',
  imports: [HeaderComponent, CommonModule,ProfileModalComponent],
  standalone: true,
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NavHeaderComponent implements OnInit, OnDestroy, OnChanges {

  /**
   * Returns the capitalized initials of the user's display name (e.g., "Sairam Ugge" -> "SU")
   */
  getInitials(): string {
    const name = this.getDisplayName();
    if (!name || name === 'User') {
      // Try to get actual user name from token storage
      const actualName = this.tokenStorageService.getDaName();
      if (actualName && actualName !== 'User') {
        return actualName
          .split(' ')
          .filter((word: string) => word.length > 0)
          .map((word: string) => word[0].toUpperCase())
          .join('');
      }
      return 'U'; // Fallback to 'U' instead of empty string
    }
    return name
      .split(' ')
      .filter((word: string) => word.length > 0)
      .map((word: string) => word[0].toUpperCase())
      .join('');
  }

  showProfileModal = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';
  public redirectUrl = '';

  // Angular 19+ Dependency Injection
  private readonly themeService = inject(ThemeService);
  private readonly authService = inject(AuthService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly tokenStorageService = inject(TokenStorageService);
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly centralizedRedirectService = inject(CentralizedRedirectService);

  private observerManager = new ObserverManager();
  private logger = createLogger('NavHeaderComponent');

  ngOnInit(): void {
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;

    this.logger.info('🎯 NavHeader component initialized');
    this.updateThemeAssets();
    this.observerManager.createMutationObserver(document.body, () => this.updateThemeAssets(), {
      attributes: true,
      attributeFilter: ['class'],
    });

    // Subscribe to auth state changes to update initials when user data becomes available
    this.authService.authState$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.cdr.markForCheck();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['userProfile']) {
      // Force change detection
      this.cdr.markForCheck();
      this.cdr.detectChanges();
    }
  }

  ngOnDestroy(): void {
    this.observerManager.cleanup();
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  getDisplayName() {
    return this.tokenStorageService.getDaName() || 'User';
  }

  toggleProfileModal(): void {
    this.showProfileModal = !this.showProfileModal;
    if (this.showProfileModal) {
      this.logger.info('Profile modal opened');
    } else {
      this.logger.info('Profile modal closed');
    }
    this.cdr.markForCheck();
  }

  closeProfileModal(): void {
    this.showProfileModal = false;
    this.cdr.markForCheck();
  }

  // Handle logout
  onLogout() {
    if (this.tokenStorageService.getLoginType() === 'basic') {
      this.authService.basicLogout().pipe(
        takeUntilDestroyed(this.destroyRef)
      ).subscribe({
        next: () => {
          // Redirect to centralized marketing login
          this.centralizedRedirectService.redirectToMarketingLogin();
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          this.logger.error('Basic logout failed:', error);
          // Still redirect to marketing login even if logout fails
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).pipe(
        takeUntilDestroyed(this.destroyRef)
      ).subscribe({
        next: () => {
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          this.logger.error('SSO logout failed:', error);
        },
      });
    }
  }

  onLogin(): void {
    try {
      this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.loginSSO(currentUrl).pipe(
        takeUntilDestroyed(this.destroyRef)
      ).subscribe({
        next: () => this.logger.debug('Login successful'),
        error: error => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      this.logger.error('Error during login:', error);
      this.toastService.error('Login failed');
    }
  }

  getProfileImage(): string {
    return `${AppConstants.AssetsPath}/user-avatar.svg`;
  }

  getEmail(): string {
    return this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `${AppConstants.AssetsPath}/ascendion-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `${AppConstants.AssetsPath}/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `${AppConstants.AssetsPath}/menu-${currentTheme}.svg`;
    this.cdr.markForCheck();
  }
}
