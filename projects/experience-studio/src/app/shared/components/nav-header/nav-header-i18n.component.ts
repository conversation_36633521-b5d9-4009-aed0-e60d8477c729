import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  computed,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';
import { ThemeService } from '../../services/theme-service/theme.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';
import { createLogger } from '../../utils/logger';
import { ToastService } from '../../services/toast.service';
import { TokenStorageService } from '@shared/index';
import { ProfileModalComponent } from '../profile-modal/profile-modal.component';
import { LanguageSwitcherComponent } from '../language-switcher/language-switcher.component';
import { I18nService, LanguageConfig } from '../../services/i18n/i18n.service';
import { TranslatePipe } from '../../pipes/translate.pipe';

/**
 * Enhanced Navigation Header Component with I18n Support
 *
 * This is an example of Phase 1 migration - adding i18n support to existing components
 * while maintaining zero breaking changes to functionality.
 *
 * Changes made:
 * 1. Added language switcher component
 * 2. Translated accessibility labels
 * 3. Added reactive language state management
 * 4. Maintained all existing functionality
 */
@Component({
  selector: 'app-nav-header-i18n',
  imports: [
    HeaderComponent,
    CommonModule,
    ProfileModalComponent,
    LanguageSwitcherComponent,
    TranslatePipe
  ],
  standalone: true,
  template: `
    <awe-header theme="light" class="py-1">
      <div left-content class="mt-4">
        <img [src]="logoSrc" class="px-2" [alt]="logoAltText()" loading="eager" />
      </div>

      <div right-content class="gap-4 d-flex align-items-center">
        <!-- Language Switcher - NEW -->
        <div class="language-switcher-container">
          <app-language-switcher
            mode="compact"
            [showLabels]="false"
            (languageChanged)="onLanguageChanged($event)"
            (languageChangeError)="onLanguageChangeError($event)">
          </app-language-switcher>
        </div>

        <!-- Theme Toggle - Enhanced with i18n -->
        <div
          class="d-flex justify-content-center align-items-center cursor-pointer"
          (click)="toggleTheme()"
          [attr.aria-label]="themeToggleLabel()"
          [attr.title]="themeToggleLabel()">
          <img [src]="themeToggleIcon" [alt]="themeToggleLabel()" loading="lazy" />
        </div>

        <!-- Profile Section - Enhanced with i18n -->
        <div>
          <span
            class="cursor-pointer profile-icon profile-initials"
            (click)="toggleProfileModal()"
            [attr.aria-label]="profileAriaLabel()"
            [attr.title]="profileAriaLabel()">
            {{ getInitials() }}
          </span>
        </div>
      </div>
    </awe-header>

    <!-- Profile Modal - Existing functionality maintained -->
    <app-profile-modal
      [isVisible]="showProfileModal"
      (closeModal)="closeProfileModal()">
    </app-profile-modal>
  `,
  styleUrls: ['./nav-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NavHeaderI18nComponent implements OnInit, OnDestroy {
  // Existing services (maintained for backward compatibility)
  private readonly themeService = inject(ThemeService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly logger = createLogger('NavHeaderComponent');
  private readonly toastService = inject(ToastService);
  private readonly tokenStorageService = inject(TokenStorageService);
  private readonly subscriptionManager = new SubscriptionManager();

  // NEW: I18n services
  private readonly i18nService = inject(I18nService);

  // Existing state (maintained)
  showProfileModal = false;
  logoSrc = '';
  themeToggleIcon = '';

  // NEW: Translated accessibility labels
  readonly logoAltText = computed(() =>
    this.i18nService.translate('common.accessibility.navigateHome')
  );

  readonly themeToggleLabel = computed(() =>
    this.i18nService.translate('common.accessibility.toggleTheme')
  );

  readonly profileAriaLabel = computed(() => {
    const displayName = this.getDisplayName();
    return this.i18nService.translate('common.accessibility.currentLanguage', { language: displayName });
  });

  ngOnInit(): void {
    this.logger.info('🚀 NavHeader component initialized with i18n support');

    // Existing initialization logic (maintained)
    this.initializeTheme();
    this.setupThemeSubscription();

    // NEW: Setup language change notifications
    this.setupLanguageChangeNotifications();
  }

  ngOnDestroy(): void {
    // No manual cleanup needed - SubscriptionManager uses DestroyRef for automatic cleanup
    this.logger.info('🧹 NavHeader component destroyed');
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  getInitials(): string {
    const name = this.getDisplayName();
    if (!name || name === 'User') {
      const actualName = this.tokenStorageService.getDaName();
      if (actualName && actualName !== 'User') {
        return actualName
          .split(' ')
          .filter((word: string) => word.length > 0)
          .map((word: string) => word[0].toUpperCase())
          .join('');
      }
      return 'U';
    }
    return name
      .split(' ')
      .filter((word: string) => word.length > 0)
      .map((word: string) => word[0].toUpperCase())
      .join('');
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  getDisplayName(): string {
    try {
      const daName = this.tokenStorageService.getDaName();
      if (daName && daName !== 'User') {
        return daName;
      }

      return 'User';
    } catch (error) {
      this.logger.error('Error getting display name:', error);
      return 'User';
    }
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  getEmail(): string {
    try {
      const email = this.tokenStorageService.getDaUsername();
      return email || '<EMAIL>';
    } catch (error) {
      this.logger.error('Error getting email:', error);
      return '<EMAIL>';
    }
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.logger.info('🎨 Theme toggled');
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  toggleProfileModal(): void {
    this.showProfileModal = !this.showProfileModal;
    this.cdr.detectChanges();
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  closeProfileModal(): void {
    this.showProfileModal = false;
    this.cdr.detectChanges();
  }

  /**
   * NEW: Handle language change events
   */
  onLanguageChanged(language: LanguageConfig): void {
    this.logger.info(`🌐 Language changed to: ${language.nativeName}`);

    // Show success toast with translated message
    const message = this.i18nService.translate('common.messages.success');
    this.toastService.success(`${message}: ${language.nativeName}`);

    // Trigger change detection to update all translated content
    this.cdr.detectChanges();
  }

  /**
   * NEW: Handle language change errors
   */
  onLanguageChangeError(error: Error): void {
    this.logger.error('❌ Language change failed:', error);

    // Show error toast with translated message
    const errorMessage = this.i18nService.translate('common.messages.error');
    this.toastService.error(`${errorMessage}: ${error.message}`);
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  private initializeTheme(): void {
    this.updateThemeAssets();
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  private setupThemeSubscription(): void {
    this.subscriptionManager.subscribe(
      this.themeService.themeObservable,
      () => {
        this.updateThemeAssets();
        this.cdr.detectChanges();
      }
    );
  }

  /**
   * NEW: Setup language change notifications
   */
  private setupLanguageChangeNotifications(): void {
    this.subscriptionManager.subscribe(
      this.i18nService.getLanguageChange$(),
      (languageCode) => {
        this.logger.info(`🔄 Language changed to: ${languageCode}`);
        // Force change detection to update all translated content
        this.cdr.detectChanges();
      }
    );
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();

    // Update logo based on theme
    this.logoSrc = currentTheme === 'dark'
      ? 'ascendionAAVA-logo-dark.svg'
      : 'ascendionAAVA-logo-light.svg';

    // Update theme toggle icon
    this.themeToggleIcon = currentTheme === 'dark'
      ? 'assets/icons/awe_light_mode.svg'
      : 'assets/icons/awe_dark_mode.svg';
  }
}
