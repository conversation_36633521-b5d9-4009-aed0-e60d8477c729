import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastComponent, ToastType } from './toast.component';
import { ToastService } from '../../services/toast.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

export interface Toast {
  id: number;
  message: string;
  type: ToastType;
  duration: number;
}

@Component({
  selector: 'exp-toast-container',
  standalone: true,
  imports: [CommonModule, ToastComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="toast-container" [ngClass]="position">
      <exp-toast
        *ngFor="let toast of toasts"
        [message]="toast.message"
        [type]="toast.type"
        [duration]="toast.duration"
        (closed)="removeToast(toast.id)">
      </exp-toast>
    </div>
  `,
  styles: [`
    .toast-container {
      position: fixed;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      pointer-events: none;
      gap: 12px;
      max-width: 400px;
      width: 100%;
      padding: 16px;

      & > * {
        pointer-events: auto;
      }
    }

    .top-right {
      top: 0;
      right: 0;
      align-items: flex-end;
    }

    .top-left {
      top: 0;
      left: 0;
      align-items: flex-start;
    }

    .bottom-right {
      bottom: 0;
      right: 0;
      align-items: flex-end;
    }

    .bottom-left {
      bottom: 0;
      left: 0;
      align-items: flex-start;
    }
  `]
})
export class ToastContainerComponent implements OnInit {
  toasts: Toast[] = [];
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'bottom-right';
  private nextId = 0;

  // Angular 19+ Dependency Injection
  private readonly toastService = inject(ToastService);
  private readonly cdr = inject(ChangeDetectorRef);

  private subscriptionManager = new SubscriptionManager();

  ngOnInit(): void {
    this.subscriptionManager.subscribe(
      this.toastService.toastEvents,
      event => {
        if (event.action === 'add' && event.toast) {
          this.addToast(event.toast);
        } else if (event.action === 'clear') {
          this.clearToasts();
        }

        if (event.position) {
          this.position = event.position;
        }

        this.cdr.markForCheck(); // Trigger change detection for OnPush
      }
    );
  }

  addToast(toast: Omit<Toast, 'id'>): void {
    // Clear all existing toasts to show only one at a time
    this.toasts = [];

    const id = this.nextId++;
    this.toasts.push({ ...toast, id });
    this.cdr.markForCheck(); // Trigger change detection for OnPush
  }

  removeToast(id: number): void {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
    this.cdr.markForCheck(); // Trigger change detection for OnPush
  }

  clearToasts(): void {
    this.toasts = [];
    this.cdr.markForCheck(); // Trigger change detection for OnPush
  }
}
