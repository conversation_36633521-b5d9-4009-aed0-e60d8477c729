// Profile modal overlay container - ensures no layout impact when hidden
.profile-modal__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
  pointer-events: auto;

  // Ensure modal doesn't affect page scrolling or layout
  overflow: hidden;

  // Responsive adjustments for mobile devices
  @media (max-width: 768px) {
    justify-content: center;
    align-items: center;
  }
}

// Modal backdrop for click-outside functionality
.profile-modal__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

// Main modal content container
.profile-modal__content {
  position: relative;
  // Match landing page card colors for consistency
  background: var(--code-viewer-bg);
  border: 1px solid var(--code-viewer-border);
  border-radius: 1.5rem;
  padding: 2rem;
  min-width: 320px;
  max-width: 400px;
  width: 100%;
  margin-top: 4rem;
  margin-right: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: profile-modal-slide-in 0.3s ease-out;

  // Enhanced shadow for dark theme
  &.dark-theme {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }

  // Tablet responsive adjustments
  @media (max-width: 768px) {
    margin: 0;
    max-width: 90vw;
  }

  // Mobile responsive adjustments
  @media (max-width: 480px) {
    margin: 1rem;
    padding: 1.5rem;
    min-width: unset;
    border-radius: 1rem;
    max-width: 95vw;
  }
}

// Modal close button
.profile-modal__close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: var(--code-viewer-text);
  opacity: 0.6;
  transition: all 0.2s ease;
  svg {
    width: 20px;
    height: 20px;
  }
}

// Profile header section with user info
.profile-modal__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

// User avatar container
.profile-modal__avatar {
  flex-shrink: 0;

  .profile-modal__initials {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    background: linear-gradient(45deg, #a259c6, #f76b6a);
    color: #ffffff;
    border-radius: 50%;
    font-weight: 600;
    font-size: 1.5rem;
    text-decoration: none;
  }
}

// User information container
.profile-modal__info {
  flex: 1;
  min-width: 0; // Allow text to truncate properly
}

// User display name
.profile-modal__name {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--code-viewer-text);
  line-height: 1.4;
  word-break: break-word;
}

// User email address
.profile-modal__email {
  font-size: 0.95rem;
  color: var(--code-viewer-text);
  opacity: 0.7;
  margin: 0;
  line-height: 1.4;
  word-break: break-all;
}

// Visual divider between sections
.profile-modal__divider {
  height: 1px;
  background: var(--code-viewer-border);
  margin: 1.5rem 0;
}

// Profile modal actions container
.profile-modal__actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

// Minimal sign out button - clean and simple design
.profile-modal__sign-out-btn {
  width: 100%;
  background: linear-gradient(90deg, #a259c6 0%, #f76b6a 100%);
  border: none;
  color: #ffffff;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: opacity 0.15s ease;

  &:hover {
    opacity: 0.9;
  }

  &:active {
    opacity: 0.8;
  }

  &:focus {
    outline: 2px solid rgba(162, 89, 198, 0.5);
    outline-offset: 2px;
  }
}

// Modal entrance animation keyframes
@keyframes profile-modal-slide-in {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Responsive design adjustments for different screen sizes
@media (max-width: 640px) {
  .profile-modal__content {
    margin: 0.5rem;
    padding: 1.5rem;
  }

  .profile-modal__header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .profile-modal__info {
    text-align: center;
  }

  .profile-modal__avatar .profile-modal__initials {
    width: 3.5rem;
    height: 3.5rem;
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .profile-modal__overlay {
    padding: 0.5rem;
  }

  .profile-modal__content {
    padding: 1.25rem;
  }

  .profile-modal__name {
    font-size: 1.125rem;
  }

  .profile-modal__email {
    font-size: 0.875rem;
  }
}
