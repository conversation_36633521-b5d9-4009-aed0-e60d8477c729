import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Input,
  Output,
  EventEmitter,
  HostListener,
  ElementRef,
  inject
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { ThemeService } from '../../services/theme-service/theme.service';
import { ObserverManager } from '../../utils/subscription-management.util';
import { createLogger } from '../../utils/logger';
import { ToastService } from '../../services/toast.service';
import { AuthService, TokenStorageService } from '@shared/index';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-profile-modal',
  imports: [CommonModule],
  standalone: true,
  templateUrl: './profile-modal.component.html',
  styleUrl: './profile-modal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileModalComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Output() closeModal = new EventEmitter<void>();

  // Angular 19+ Dependency Injection
  private readonly themeService = inject(ThemeService);
  private readonly authService = inject(AuthService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly tokenStorageService = inject(TokenStorageService);
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly elementRef = inject(ElementRef);

  private readonly logger = createLogger('ProfileModalComponent');
  public redirectUrl = '';
  currentTheme: 'light' | 'dark' = 'light';
  private destroy$ = new Subject<void>();
  private observerManager = new ObserverManager();

  ngOnInit(): void {
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;
    
    this.logger.info('🎯 ProfileModal component initialized');
    
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.currentTheme = theme;
        this.cdr.markForCheck();
      });

    // Set initial theme
    this.currentTheme = this.themeService.getCurrentTheme();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.observerManager.cleanup();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.isVisible && !this.elementRef.nativeElement.contains(event.target)) {
      this.onClose();
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.isVisible) {
      this.onClose();
    }
  }

  /**
   * Returns the capitalized initials of the user's display name (e.g., "Raghuram Pathmanaban" -> "RP")
   */
  getInitials(): string {
    const name = this.getDisplayName();
    if (!name) return 'U';
    return name
      .split(' ')
      .filter((word: string) => word.length > 0)
      .map((word: string) => word[0].toUpperCase())
      .join('');
  }

  getDisplayName(): string {
    return this.tokenStorageService.getDaName() || 'User';
  }

  getEmail(): string {
    return this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }

  onClose(): void {
    this.closeModal.emit();
  }

  // Handle logout
  onLogout(): void {
    if (this.tokenStorageService.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          this.router.navigate(['/']);
          this.tokenStorageService.deleteCookie('org_path');
          this.onClose();
        },
        error: (error) => {
          this.logger.error('Basic logout failed:', error);
          this.router.navigate(['/']);
          this.onClose();
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorageService.deleteCookie('org_path');
          this.onClose();
        },
        error: (error) => {
          this.logger.error('SSO logout failed:', error);
          this.onClose();
        },
      });
    }
  }
}
