import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  inject,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AuthService, TokenStorageService } from '@shared/index';
import { I18nService } from '../../services/i18n/i18n.service';
import { TranslatePipe } from '../../pipes/translate.pipe';
import { createLogger } from '../../utils/logger';

/**
 * Enhanced Profile Modal Component with I18n Support
 * 
 * This is an example of Phase 1 migration - low-risk text migration
 * for static labels and accessibility content.
 * 
 * Changes made:
 * 1. Translated static text labels
 * 2. Enhanced accessibility labels
 * 3. Maintained all existing functionality
 * 4. Added reactive translation support
 */
@Component({
  selector: 'app-profile-modal-i18n',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  template: `
    <div
      *ngIf="isVisible"
      class="profile-modal__overlay"
      role="dialog"
      aria-modal="true"
      [attr.aria-labelledby]="modalTitleId">

      <!-- Modal backdrop for click-outside functionality -->
      <div class="profile-modal__backdrop" (click)="onClose()"></div>

      <!-- Main modal content container -->
      <div class="profile-modal__content" [class.dark-theme]="currentTheme === 'dark'">
        <!-- Close modal button with translated aria-label -->
        <button
          class="profile-modal__close-btn"
          (click)="onClose()"
          [attr.aria-label]="closeButtonLabel()"
          type="button">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <!-- User profile header section -->
        <div class="profile-modal__header">
          <div class="profile-modal__avatar">
            <span class="profile-modal__initials">{{ getInitials() }}</span>
          </div>
          <div class="profile-modal__info">
            <!-- Translated modal title -->
            <h2 [id]="modalTitleId" class="profile-modal__name">{{ getDisplayName() }}</h2>
            <p class="profile-modal__email">{{ getEmail() }}</p>
          </div>
        </div>

        <!-- Visual divider -->
        <div class="profile-modal__divider"></div>

        <!-- Action buttons section with translated content -->
        <div class="profile-modal__actions">
          <button
            class="profile-modal__sign-out-btn"
            (click)="onLogout()"
            type="button"
            [attr.aria-label]="signOutAriaLabel()">
            {{ signOutButtonText() }}
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./profile-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProfileModalI18nComponent {
  // Existing services (maintained for backward compatibility)
  private readonly themeService = inject(ThemeService);
  private readonly authService = inject(AuthService);
  private readonly tokenStorageService = inject(TokenStorageService);
  private readonly logger = createLogger('ProfileModalComponent');

  // NEW: I18n service
  private readonly i18nService = inject(I18nService);

  // Existing inputs/outputs (maintained)
  @Input() isVisible = false;
  @Output() closeModal = new EventEmitter<void>();

  // Existing properties (maintained)
  currentTheme: string = 'light';
  modalTitleId = 'profile-modal-title';

  // NEW: Reactive translated content using Angular 19+ computed signals
  readonly closeButtonLabel = computed(() => 
    this.i18nService.translate('profile.modal.close')
  );

  readonly signOutButtonText = computed(() => 
    this.i18nService.translate('profile.modal.signOut')
  );

  readonly signOutAriaLabel = computed(() => 
    this.i18nService.translate('profile.modal.signOut')
  );

  constructor() {
    // Existing theme initialization (maintained)
    this.currentTheme = this.themeService.getCurrentTheme();
    
    // Setup theme subscription (existing functionality)
    this.themeService.themeObservable.subscribe((theme: 'light' | 'dark') => {
      this.currentTheme = theme;
    });
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  onClose(): void {
    this.closeModal.emit();
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  onLogout(): void {
    this.logger.info('🚪 User logout initiated');
    this.authService.logout();
    this.closeModal.emit();
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  getInitials(): string {
    const name = this.getDisplayName();
    if (!name || name === 'User') {
      const actualName = this.tokenStorageService.getDaName();
      if (actualName && actualName !== 'User') {
        return actualName
          .split(' ')
          .filter((word: string) => word.length > 0)
          .map((word: string) => word[0].toUpperCase())
          .join('');
      }
      return 'U';
    }
    return name
      .split(' ')
      .filter((word: string) => word.length > 0)
      .map((word: string) => word[0].toUpperCase())
      .join('');
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  getDisplayName(): string {
    try {
      const daName = this.tokenStorageService.getDaName();
      if (daName && daName !== 'User') {
        return daName;
      }

      return 'User';
    } catch (error) {
      this.logger.error('Error getting display name:', error);
      return 'User';
    }
  }

  /**
   * Existing method - maintained for backward compatibility
   */
  getEmail(): string {
    try {
      const daUsername = this.tokenStorageService.getDaUsername();
      return daUsername || '<EMAIL>';
    } catch (error) {
      this.logger.error('Error getting email:', error);
      return '<EMAIL>';
    }
  }
}

