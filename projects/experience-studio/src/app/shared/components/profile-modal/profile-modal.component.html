<div
  *ngIf="isVisible"
  class="profile-modal__overlay"
  role="dialog"
  aria-modal="true"
  aria-labelledby="profile-modal-title">

  <!-- Modal backdrop for click-outside functionality -->
  <div class="profile-modal__backdrop" (click)="onClose()"></div>

  <!-- Main modal content container -->
  <div class="profile-modal__content" [class.dark-theme]="currentTheme === 'dark'">
    <!-- Close modal button -->
    <button
      class="profile-modal__close-btn"
      (click)="onClose()"
      aria-label="Close profile modal"
      type="button">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>

    <!-- User profile header section -->
    <div class="profile-modal__header">
      <div class="profile-modal__avatar">
        <span class="profile-modal__initials">{{ getInitials() }}</span>
      </div>
      <div class="profile-modal__info">
        <h2 id="profile-modal-title" class="profile-modal__name">{{ getDisplayName() }}</h2>
        <p class="profile-modal__email">{{ getEmail() }}</p>
      </div>
    </div>

    <!-- Visual divider -->
    <div class="profile-modal__divider"></div>

    <!-- Action buttons section -->
    <div class="profile-modal__actions">
      <button
        class="profile-modal__sign-out-btn"
        (click)="onLogout()"
        type="button"
        aria-label="Sign out of your account">
        Sign Out
      </button>
    </div>
  </div>
</div>
