import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { EditRequest, EditResponse } from './ui-design-selection.service';
import { environment } from '../../../environments/environment';
import { ApiRetryService } from './api-retry.service';
import { GenerateUIDesignService } from './generate-ui-design.service';
import { UserSignatureService } from './user-signature.service';

@Injectable({
  providedIn: 'root'
})
export class UIDesignEditService {

  private readonly http = inject(HttpClient);
  private readonly apiRetryService = inject(ApiRetryService);
  private readonly generateUIDesignService = inject(GenerateUIDesignService);
  // COMMENTED OUT: userSignature functionality removed
  // private readonly userSignatureService = inject(UserSignatureService);

  // API configuration
  private readonly baseUrl = environment.experienceApiUrl;
  private readonly editEndpoint = 'wireframe-generation/regenerate';
  // Removed requestTimeout to prevent 499 client errors
  constructor() {

  }

  /**
   * Edit UI Design page content
   * 🚨 CRITICAL: NO TIMEOUT - wireframe-generation/regenerate can take longer than 4 minutes
   * Removed all client-side timeouts to prevent 499 errors
   */
  editUIDesignPage(request: EditRequest): Observable<EditResponse> {
    const url = `${this.baseUrl}/${this.editEndpoint}`;

    // Get project_id from the GenerateUIDesignService
    const projectId = this.generateUIDesignService.getProjectId();
    // COMMENTED OUT: userSignature functionality removed
    // const signature = this.userSignatureService.getUserSignatureSync();
    // Build query parameters
    let params = new HttpParams();
    // params = params.set('user_signature', signature);
    if (projectId) {
      params = params.set('project_id', projectId);

    } else {

    }

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
      // Removed 'Connection' and 'Keep-Alive' headers as they are forbidden by browsers
      // Browser automatically manages connection keep-alive for optimal performance
    });

    // Create the HTTP request observable
    const httpRequest = this.http.post<any>(url, request, {
      headers,
      params,
      observe: 'response'
    }).pipe(
      // 🛡️ CRITICAL: NO TIMEOUT to prevent 499 client errors
      // Let the server determine when to timeout, not the client
      // timeout() operator removed completely to prevent client-side timeouts
      map(response => {

        // Validate response structure
        if (!this.isValidEditResponse(response.body)) {
          throw new Error('Invalid response format from edit API');
        }

        return response.body as EditResponse;
      }),
      catchError(error => {

        // Enhanced 499 error handling
        if (error.status === 499) {

          return throwError(() => new Error('Request was cancelled due to timeout. The operation may still be processing on the server.'));
        }

        // Handle specific error cases
        if (error.status === 0) {

          return throwError(() => new Error('Network error. Please check your connection.'));
        }

        if (error.status >= 400 && error.status < 500) {
          return throwError(() => new Error('Invalid request. Please check your input and try again.'));
        }

        if (error.status >= 500) {
          return throwError(() => new Error('Server error. Please try again later.'));
        }

        return throwError(() => new Error(error.message || 'An unexpected error occurred'));
      })
    );

    // Apply retry logic for 499 errors and network failures
    return this.apiRetryService.withRetry(httpRequest, this.apiRetryService.getEditRetryConfig());
  }

  /**
   * Validate edit API response structure (handles both array and stringified array formats)
   */
  private isValidEditResponse(response: any): boolean {
    let parsedResponse: any;

    // Handle stringified JSON response
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response);

      } catch (error) {

        return false;
      }
    } else {
      parsedResponse = response;
    }

    if (!Array.isArray(parsedResponse)) {

      return false;
    }

    if (parsedResponse.length === 0) {

      return false;
    }

    // Validate each code item in the array
    for (const item of parsedResponse) {
      if (!item || typeof item !== 'object') {

        return false;
      }

      if (typeof item.fileName !== 'string' || !item.fileName.trim()) {

        return false;
      }

      if (typeof item.content !== 'string') {

        return false;
      }
    }

    return true;
  }

  /**
   * Extract fileName from request for logging
   */
  private extractFileNameFromRequest(request: EditRequest): string {
    try {
      if (request.code?.[0]?.fileName) {
        return request.code[0].fileName;
      }
    } catch (error) {

    }

    return 'unknown';
  }

  /**
   * Extract user request from request for logging
   */
  private extractUserRequestFromRequest(request: EditRequest): string {
    try {
      if (request.user_request) {
        return request.user_request;
      }
    } catch (error) {

    }

    return 'unknown';
  }

  /**
   * Build edit request with proper format
   */
  buildEditRequest(fileName: string, content: string, userRequest: string): EditRequest {
    const request: EditRequest = {
      code: [{
        fileName: fileName,
        content: content
      }],
      user_request: userRequest
    };

    return request;
  }

  /**
   * Extract updated content from edit response (handles both array and stringified array formats)
   */
  extractUpdatedContent(response: EditResponse | string, fileName: string): string | null {
    let parsedResponse: EditResponse;

    // Handle stringified JSON response
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response);

      } catch (error) {

        return null;
      }
    } else {
      parsedResponse = response;
    }

    const matchingFile = parsedResponse.find((file: { fileName: string; content: string }) =>
      file.fileName === fileName
    );

    if (!matchingFile) {
      // No matching file found in response
      return null;
    }

    return matchingFile.content;
  }

  /**
   * Get service status for debugging
   */
  getServiceStatus(): {
    baseUrl: string;
    editEndpoint: string;
    fullUrl: string;
    timeout: string;
  } {
    return {
      baseUrl: this.baseUrl,
      editEndpoint: this.editEndpoint,
      fullUrl: `${this.baseUrl}/${this.editEndpoint}`,
      timeout: 'No timeout (prevents 499 errors)'
    };
  }
}
