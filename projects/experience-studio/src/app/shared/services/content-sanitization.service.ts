import { Injectable, SecurityContext, inject } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

/**
 * Service for sanitizing content before passing to markdown components
 * Handles HTML content sanitization to prevent Angular sanitization warnings
 */
@Injectable({
  providedIn: 'root'
})
export class ContentSanitizationService {
  // Angular 19+ Dependency Injection
  private readonly sanitizer = inject(DomSanitizer);

  /**
   * Sanitizes content for safe markdown rendering
   * Removes potentially unsafe HTML tags and content that could trigger sanitization warnings
   * @param content The content to sanitize
   * @returns Sanitized content safe for markdown rendering
   */
  sanitizeForMarkdown(content: string): string {
    if (!content) return '';

    let sanitizedContent = content;

    // Remove <mlo_artifact> tags and their content
    sanitizedContent = this.removeMloArtifactTags(sanitizedContent);

    // Remove other potentially problematic HTML tags
    sanitizedContent = this.removeUnsafeHtmlTags(sanitizedContent);

    // Clean up JSON error messages
    sanitizedContent = this.cleanJsonErrorMessages(sanitizedContent);

    // Escape remaining HTML entities to prevent sanitization warnings
    sanitizedContent = this.escapeHtmlEntities(sanitizedContent);

    return sanitizedContent;
  }

  /**
   * Removes <mlo_artifact> tags and their content
   * @param content The content to process
   * @returns Content with mlo_artifact tags removed
   */
  private removeMloArtifactTags(content: string): string {
    // Remove <mlo_artifact> tags and everything after them
    if (content.includes('<mlo_artifact>')) {
      const parts = content.split('<mlo_artifact>');
      return parts[0].trim();
    }
    return content;
  }

  /**
   * Removes unsafe HTML tags that could trigger sanitization warnings
   * @param content The content to process
   * @returns Content with unsafe HTML tags removed
   */
  private removeUnsafeHtmlTags(content: string): string {
    // List of potentially unsafe tags to remove
    const unsafeTags = [
      /<script[^>]*>.*?<\/script>/gis,
      /<iframe[^>]*>.*?<\/iframe>/gis,
      /<object[^>]*>.*?<\/object>/gis,
      /<embed[^>]*>/gi,
      /<form[^>]*>.*?<\/form>/gis,
      /<input[^>]*>/gi,
      /<textarea[^>]*>.*?<\/textarea>/gis,
      /<select[^>]*>.*?<\/select>/gis,
      /<button[^>]*>.*?<\/button>/gis,
      /<link[^>]*>/gi,
      /<meta[^>]*>/gi,
      /<style[^>]*>.*?<\/style>/gis
    ];

    let sanitizedContent = content;
    unsafeTags.forEach(tagRegex => {
      sanitizedContent = sanitizedContent.replace(tagRegex, '');
    });

    return sanitizedContent;
  }

  /**
   * Cleans up JSON error messages to extract readable error text
   * @param content The content to process
   * @returns Content with cleaned error messages
   */
  private cleanJsonErrorMessages(content: string): string {
    // Check if the content looks like a JSON error message
    if (content.includes('{') && content.includes('}') &&
        (content.includes('"message"') || content.includes('error') ||
         content.includes('Error') || content.includes('Unexpected'))) {
      try {
        // Try to parse as JSON and extract error message
        const jsonMatch = content.match(/\{.*\}/s);
        if (jsonMatch) {
          const jsonStr = jsonMatch[0];
          const parsed = JSON.parse(jsonStr);

          // Extract error message from various possible fields
          const errorMessage = parsed.message || parsed.error || parsed.details ||
                              parsed.description || parsed.reason || 'An error occurred';

          // Replace the JSON with the clean error message
          return content.replace(jsonMatch[0], errorMessage);
        }
      } catch (e) {
        // If JSON parsing fails, just return the original content
        return content;
      }
    }
    return content;
  }

  /**
   * Escapes HTML entities to prevent sanitization warnings
   * @param content The content to process
   * @returns Content with HTML entities escaped
   */
  private escapeHtmlEntities(content: string): string {
    // Create a temporary div element to escape HTML
    const div = document.createElement('div');
    div.textContent = content;
    return div.innerHTML;
  }

  /**
   * Sanitizes HTML content using Angular's DomSanitizer
   * Use this for content that needs to be rendered as HTML
   * @param content The HTML content to sanitize
   * @returns SafeHtml that can be used with [innerHTML]
   */
  sanitizeHtml(content: string): SafeHtml {
    const sanitizedContent = this.sanitizeForMarkdown(content);
    return this.sanitizer.sanitize(SecurityContext.HTML, sanitizedContent) || '';
  }

  /**
   * Validates if content is safe for markdown rendering
   * @param content The content to validate
   * @returns True if content is safe, false otherwise
   */
  isContentSafe(content: string): boolean {
    if (!content) return true;

    // Check for potentially unsafe patterns
    const unsafePatterns = [
      /<script/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
      /javascript:/i,
      /data:text\/html/i,
      /vbscript:/i,
      /on\w+\s*=/i // Event handlers like onclick, onload, etc.
    ];

    return !unsafePatterns.some(pattern => pattern.test(content));
  }

  /**
   * Preprocesses content specifically for stepper descriptions
   * Handles special cases for stepper content
   * @param description The stepper description to preprocess
   * @returns Preprocessed description safe for markdown
   */
  preprocessStepperDescription(description: string): string {
    if (!description) return '';

    let processed = this.sanitizeForMarkdown(description);

    // Additional stepper-specific processing
    // Remove any remaining XML-like tags that might be stepper-specific
    processed = processed.replace(/<[^>]+>/g, '');

    // Clean up excessive whitespace
    processed = processed.replace(/\s+/g, ' ').trim();

    // Ensure the content ends properly
    if (processed && !processed.endsWith('.') && !processed.endsWith('!') && !processed.endsWith('?')) {
      processed += '.';
    }

    return processed;
  }
}
