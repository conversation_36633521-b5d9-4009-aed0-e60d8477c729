import { Injectable, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, map, catchError } from 'rxjs';
import { CommunityProject, WorkspaceStateService } from './workspace-state.service';
import { environment } from '../../../environments/environment';
import { UserSignatureService } from './user-signature.service';
import { cacheHelpers } from '../interceptors/cache.interceptor';

export interface ProjectCategory {
  id: string;
  name: string;
  icon: string;
  count: number;
  description: string;
}

export interface FeaturedCreator {
  id: string;
  name: string;
  avatar: string;
  projectCount: number;
  rating: number;
  specialties: string[];
}

export interface AvailableFilters {
  projectTypes: { id: string; name: string; count: number }[];
  technologies: { id: string; name: string; count: number }[];
  difficulties: { id: string; name: string; count: number }[];
  creators: { id: string; name: string; count: number }[];
}

// API Response Interfaces
export interface ProjectAPIResponse {
  status_code: number;
  projects: APIProject[];
  total_count?: number;
  page?: number;
  limit?: number;
}

export interface APIProject {
  project_id: string;
  project_name: string;
  project_description: string;
  project_type: string | null;
  last_modified: string;
  created_at?: string;
  created_by?: string;
  project_state?: string;
  // Community-specific fields (may be null for user projects)
  creator_name?: string;
  creator_avatar?: string;
  rating?: number;
  views?: number;
  tags?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  thumbnail?: string;
}

// Query parameters for community projects
export interface CommunityProjectsQuery {
  user_signature: string;
  scope?: 'user' | 'community' | 'all';
  category?: string;
  search?: string;
  project_types?: string[];
  technologies?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'all';
  creator?: string;
  sort_by?: 'relevance' | 'date' | 'rating' | 'views' | 'name';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Community Projects Service - Manages community project data and operations
 * Provides data for the showcase page and community features
 *
 * Features:
 * - Community project loading and caching
 * - Category and filter management
 * - Trending projects tracking
 * - Featured creators management
 * - Search and filtering logic
 */
@Injectable({
  providedIn: 'root'
})
export class CommunityProjectsService {
  private readonly http = inject(HttpClient);
  private readonly workspaceState = inject(WorkspaceStateService);
  // COMMENTED OUT: userSignature functionality removed
  // private readonly userSignatureService = inject(UserSignatureService);

  private readonly apiUrl = environment.experienceApiUrl;

  // Service state signals
  private readonly _categories = signal<ProjectCategory[]>([]);
  private readonly _availableFilters = signal<AvailableFilters>({
    projectTypes: [],
    technologies: [],
    difficulties: [],
    creators: []
  });
  private readonly _trendingProjects = signal<CommunityProject[]>([]);
  private readonly _featuredCreators = signal<FeaturedCreator[]>([]);

  // Public readonly signals
  readonly categories = this._categories.asReadonly();
  readonly availableFilters = this._availableFilters.asReadonly();
  readonly trendingProjects = this._trendingProjects.asReadonly();
  readonly featuredCreators = this._featuredCreators.asReadonly();

  // Mock data for development
  private readonly mockCommunityProjects: CommunityProject[] = [
    {
      id: '1',
      title: 'SaaS Dashboard Design System',
      description: 'Complete design system for modern SaaS applications with 50+ components',
      type: 'wireframe_generation',
      thumbnail: '/assets/projects/saas-dashboard.jpg',
      creator: {
        id: 'alex-designer',
        name: 'Alex Designer',
        avatar: '/assets/avatars/alex.jpg'
      },
      rating: 4.8,
      views: 12500,
      tags: ['react', 'design-system', 'saas', 'dashboard', 'components'],
      difficulty: 'intermediate',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-20')
    },
    {
      id: '2',
      title: 'E-commerce Mobile App',
      description: 'Full-featured mobile shopping app with payment integration and user management',
      type: 'app_generation',
      thumbnail: '/assets/projects/ecommerce-mobile.jpg',
      creator: {
        id: 'sarah-dev',
        name: 'Sarah Developer',
        avatar: '/assets/avatars/sarah.jpg'
      },
      rating: 4.9,
      views: 18200,
      tags: ['react-native', 'ecommerce', 'mobile', 'payment', 'shopping'],
      difficulty: 'advanced',
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-18')
    },
    {
      id: '3',
      title: 'Social Media Platform',
      description: 'Modern social networking platform with real-time chat and media sharing',
      type: 'app_generation',
      thumbnail: '/assets/projects/social-platform.jpg',
      creator: {
        id: 'mike-fullstack',
        name: 'Mike Fullstack',
        avatar: '/assets/avatars/mike.jpg'
      },
      rating: 4.7,
      views: 9800,
      tags: ['vue', 'social', 'chat', 'media', 'realtime'],
      difficulty: 'advanced',
      createdAt: new Date('2024-01-08'),
      updatedAt: new Date('2024-01-16')
    },
    {
      id: '4',
      title: 'Learning Management System',
      description: 'Educational platform with course management, quizzes, and progress tracking',
      type: 'wireframe_generation',
      thumbnail: '/assets/projects/lms-platform.jpg',
      creator: {
        id: 'lisa-educator',
        name: 'Lisa Educator',
        avatar: '/assets/avatars/lisa.jpg'
      },
      rating: 4.6,
      views: 7300,
      tags: ['angular', 'education', 'learning', 'courses', 'tracking'],
      difficulty: 'intermediate',
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-14')
    },
    {
      id: '5',
      title: 'Fitness Tracking Dashboard',
      description: 'Health and fitness monitoring with workout plans and nutrition tracking',
      type: 'wireframe_generation',
      thumbnail: '/assets/projects/fitness-dashboard.jpg',
      creator: {
        id: 'tom-fitness',
        name: 'Tom Fitness',
        avatar: '/assets/avatars/tom.jpg'
      },
      rating: 4.5,
      views: 5600,
      tags: ['react', 'fitness', 'health', 'tracking', 'dashboard'],
      difficulty: 'beginner',
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-12')
    },
    {
      id: '6',
      title: 'Travel Booking Platform',
      description: 'Complete travel booking system with hotel, flight, and activity reservations',
      type: 'app_generation',
      thumbnail: '/assets/projects/travel-booking.jpg',
      creator: {
        id: 'emma-travel',
        name: 'Emma Travel',
        avatar: '/assets/avatars/emma.jpg'
      },
      rating: 4.8,
      views: 11400,
      tags: ['next.js', 'travel', 'booking', 'hotels', 'flights'],
      difficulty: 'advanced',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-10')
    }
  ];

  private readonly mockCategories: ProjectCategory[] = [
    {
      id: 'all',
      name: 'All Projects',
      icon: 'fas fa-th',
      count: 156,
      description: 'Browse all community projects'
    },
    {
      id: 'wireframe_generation',
      name: 'Wireframes',
      icon: 'fas fa-paint-brush',
      count: 78,
      description: 'UI/UX wireframes and design systems'
    },
    {
      id: 'app_generation',
      name: 'Applications',
      icon: 'fas fa-code',
      count: 78,
      description: 'Full-stack applications and web apps'
    },
    {
      id: 'trending',
      name: 'Trending',
      icon: 'fas fa-fire',
      count: 24,
      description: 'Most popular projects this week'
    },
    {
      id: 'featured',
      name: 'Featured',
      icon: 'fas fa-star',
      count: 12,
      description: 'Hand-picked exceptional projects'
    }
  ];

  private readonly mockFeaturedCreators: FeaturedCreator[] = [
    {
      id: 'alex-designer',
      name: 'Alex Designer',
      avatar: '/assets/avatars/alex.jpg',
      projectCount: 12,
      rating: 4.8,
      specialties: ['UI/UX', 'Design Systems', 'React']
    },
    {
      id: 'sarah-dev',
      name: 'Sarah Developer',
      avatar: '/assets/avatars/sarah.jpg',
      projectCount: 8,
      rating: 4.9,
      specialties: ['Mobile Apps', 'React Native', 'E-commerce']
    },
    {
      id: 'mike-fullstack',
      name: 'Mike Fullstack',
      avatar: '/assets/avatars/mike.jpg',
      projectCount: 15,
      rating: 4.7,
      specialties: ['Full-stack', 'Vue.js', 'Node.js']
    },
    {
      id: 'lisa-educator',
      name: 'Lisa Educator',
      avatar: '/assets/avatars/lisa.jpg',
      projectCount: 6,
      rating: 4.6,
      specialties: ['EdTech', 'Angular', 'Learning Platforms']
    }
  ];

  /**
   * Load community projects from API
   */
  loadCommunityProjects(query?: Partial<CommunityProjectsQuery>): void {
    this.workspaceState.setLoading(true);

    this.getCommunityProjects(query).subscribe({
      next: (response) => {
        const projects = this.mapAPIProjectsToCommunityProjects(response.projects);
        this.workspaceState.setCommunityProjects(projects);
        this.workspaceState.setTotalResults(response.total_count || projects.length);
        this.workspaceState.setLoading(false);
      },
      error: (error) => {

        this.workspaceState.setLoading(false);
        this.workspaceState.setGlobalError('Failed to load community projects');

        // Fallback to mock data in case of API error
        this.loadMockDataAsFallback();
      }
    });
  }

  /**
   * Get community projects from API
   */
  getCommunityProjects(query?: Partial<CommunityProjectsQuery>): Observable<ProjectAPIResponse> {
    // COMMENTED OUT: userSignature functionality removed
    // const signature = this.userSignatureService.getUserSignatureSync();

    let params = new HttpParams()
      // .set('user_signature', signature)
      .set('scope', query?.scope || 'community')
      .set('limit', (query?.limit || 20).toString());

    // Add optional query parameters
    if (query?.category && query.category !== 'all') {
      params = params.set('category', query.category);
    }

    if (query?.search) {
      params = params.set('search', query.search);
    }

    if (query?.project_types?.length) {
      params = params.set('project_types', query.project_types.join(','));
    }

    if (query?.technologies?.length) {
      params = params.set('technologies', query.technologies.join(','));
    }

    if (query?.difficulty && query.difficulty !== 'all') {
      params = params.set('difficulty', query.difficulty);
    }

    if (query?.creator) {
      params = params.set('creator', query.creator);
    }

    if (query?.sort_by) {
      params = params.set('sort_by', query.sort_by);
      params = params.set('sort_order', query.sort_order || 'desc');
    }

    if (query?.page) {
      params = params.set('page', query.page.toString());
    }

    // Set cache for community projects (5 minutes)
    const context = cacheHelpers.setMaxAge(5 * 60 * 1000);

    return this.http.get<ProjectAPIResponse>(`${this.apiUrl}/common/project`, {
      params,
      context
    }).pipe(
      catchError(error => {

        // Return mock data as fallback
        return of({
          status_code: 200,
          projects: this.mockCommunityProjects.map(p => this.mapCommunityProjectToAPI(p)),
          total_count: this.mockCommunityProjects.length
        });
      })
    );
  }

  /**
   * Load project categories
   */
  loadCategories(): void {
    this._categories.set(this.mockCategories);
  }

  /**
   * Load available filters
   */
  loadAvailableFilters(): void {
    const filters: AvailableFilters = {
      projectTypes: [
        { id: 'wireframe_generation', name: 'Wireframe Generation', count: 78 },
        { id: 'app_generation', name: 'App Generation', count: 78 }
      ],
      technologies: [
        { id: 'react', name: 'React', count: 45 },
        { id: 'angular', name: 'Angular', count: 32 },
        { id: 'vue', name: 'Vue.js', count: 28 },
        { id: 'next.js', name: 'Next.js', count: 24 },
        { id: 'react-native', name: 'React Native', count: 18 }
      ],
      difficulties: [
        { id: 'beginner', name: 'Beginner', count: 52 },
        { id: 'intermediate', name: 'Intermediate', count: 68 },
        { id: 'advanced', name: 'Advanced', count: 36 }
      ],
      creators: this.mockFeaturedCreators.map(creator => ({
        id: creator.id,
        name: creator.name,
        count: creator.projectCount
      }))
    };

    this._availableFilters.set(filters);
  }

  /**
   * Load trending projects
   */
  loadTrendingProjects(): void {
    // Get top 5 projects by views
    const trending = [...this.mockCommunityProjects]
      .sort((a, b) => b.views - a.views)
      .slice(0, 5);

    this._trendingProjects.set(trending);
  }

  /**
   * Load featured creators
   */
  loadFeaturedCreators(): void {
    this._featuredCreators.set(this.mockFeaturedCreators);
  }

  /**
   * Search community projects using API
   */
  searchProjects(query: string, filters?: any): Observable<CommunityProject[]> {
    const searchQuery: Partial<CommunityProjectsQuery> = {
      search: query,
      project_types: filters?.projectTypes,
      technologies: filters?.technologies,
      difficulty: filters?.difficulty,
      creator: filters?.creator,
      sort_by: filters?.sortBy || 'relevance',
      sort_order: filters?.sortOrder || 'desc'
    };

    return this.getCommunityProjects(searchQuery).pipe(
      map(response => this.mapAPIProjectsToCommunityProjects(response.projects))
    );
  }

  /**
   * Map API projects to CommunityProject interface
   */
  private mapAPIProjectsToCommunityProjects(apiProjects: APIProject[]): CommunityProject[] {
    return apiProjects.map(project => ({
      id: project.project_id,
      title: project.project_name,
      description: project.project_description,
      type: project.project_type || 'app_generation',
      thumbnail: project.thumbnail || undefined,
      creator: {
        id: project.created_by || 'unknown',
        name: project.creator_name || 'Unknown Creator',
        avatar: project.creator_avatar || '/assets/images/default-avatar.png'
      },
      rating: project.rating || 4.0,
      views: project.views || 0,
      tags: project.tags || this.generateTagsFromProject(project),
      difficulty: project.difficulty || 'intermediate',
      createdAt: new Date(project.created_at || project.last_modified),
      updatedAt: new Date(project.last_modified)
    }));
  }

  /**
   * Map CommunityProject to API format (for fallback)
   */
  private mapCommunityProjectToAPI(project: CommunityProject): APIProject {
    return {
      project_id: project.id,
      project_name: project.title,
      project_description: project.description,
      project_type: project.type,
      last_modified: project.updatedAt.toISOString(),
      created_at: project.createdAt.toISOString(),
      created_by: project.creator.id,
      creator_name: project.creator.name,
      creator_avatar: project.creator.avatar,
      rating: project.rating,
      views: project.views,
      tags: project.tags,
      difficulty: project.difficulty,
      thumbnail: project.thumbnail
    };
  }

  /**
   * Generate tags from project data
   */
  private generateTagsFromProject(project: APIProject): string[] {
    const tags: string[] = [];

    if (project.project_type) {
      tags.push(project.project_type.replace('_', ' '));
    }

    // Add some default tags based on project type
    if (project.project_type === 'wireframe_generation') {
      tags.push('ui', 'design', 'wireframe');
    } else if (project.project_type === 'app_generation') {
      tags.push('app', 'development', 'code');
    }

    return tags;
  }

  /**
   * Load mock data as fallback
   */
  private loadMockDataAsFallback(): void {
    const projects = this.mockCommunityProjects;
    this.workspaceState.setCommunityProjects(projects);
    this.workspaceState.setTotalResults(projects.length);
    this.workspaceState.setLoading(false);
  }

  /**
   * Get project by ID
   */
  getProjectById(id: string): Observable<CommunityProject | null> {
    const project = this.mockCommunityProjects.find(p => p.id === id);
    return of(project || null);
  }

  /**
   * Get projects by creator
   */
  getProjectsByCreator(creatorId: string): Observable<CommunityProject[]> {
    const projects = this.mockCommunityProjects.filter(p => p.creator.id === creatorId);
    return of(projects);
  }

  /**
   * Get similar projects
   */
  getSimilarProjects(projectId: string, limit: number = 4): Observable<CommunityProject[]> {
    const project = this.mockCommunityProjects.find(p => p.id === projectId);
    if (!project) return of([]);

    // Find projects with similar tags or type
    const similar = this.mockCommunityProjects
      .filter(p => p.id !== projectId)
      .filter(p =>
        p.type === project.type ||
        p.tags.some(tag => project.tags.includes(tag))
      )
      .slice(0, limit);

    return of(similar);
  }
}
