import { Injectable, inject, signal, computed } from '@angular/core';
import { Observable, of } from 'rxjs';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { createLogger } from '../../utils/logger';

// REMOVED: HttpClient, catchError, environment imports - no longer needed since API calls are disabled

// Organization hierarchy interfaces - matching console implementation
export interface Team {
  teamId: number;
  teamName: string;
}

export interface Project {
  projectId: number;
  projectName: string;
  teams: Team[];
}

export interface Domain {
  domainId: number;
  domainName: string;
  projects: Project[];
}

export interface Organization {
  orgId: number;
  organizationName: string;
  domains: Domain[];
}

// Mock data for development/fallback
const MOCK_ORGANIZATION_DATA: Organization[] = [
  {
    orgId: 1,
    organizationName: 'Ascendion',
    domains: [
      {
        domainId: 1,
        domainName: 'Engineering',
        projects: [
          {
            projectId: 1,
            projectName: 'Experience Studio',
            teams: [
              { teamId: 1, teamName: 'Frontend Team' },
              { teamId: 2, teamName: 'Backend Team' },
              { teamId: 3, teamName: 'Design Team' }
            ]
          },
          {
            projectId: 2,
            projectName: 'Product Studio',
            teams: [
              { teamId: 4, teamName: 'Product Team' },
              { teamId: 5, teamName: 'Analytics Team' }
            ]
          }
        ]
      },
      {
        domainId: 2,
        domainName: 'Innovation',
        projects: [
          {
            projectId: 3,
            projectName: 'AI Research',
            teams: [
              { teamId: 6, teamName: 'ML Team' },
              { teamId: 7, teamName: 'Research Team' }
            ]
          }
        ]
      }
    ]
  },
  {
    orgId: 2,
    organizationName: 'Client Organization',
    domains: [
      {
        domainId: 3,
        domainName: 'Digital Transformation',
        projects: [
          {
            projectId: 4,
            projectName: 'Web Platform',
            teams: [
              { teamId: 8, teamName: 'Development Team' },
              { teamId: 9, teamName: 'QA Team' }
            ]
          }
        ]
      }
    ]
  }
];

@Injectable({
  providedIn: 'root',
})
export class OrgConfigService {
  private readonly logger = createLogger('OrgConfigService');
  private readonly tokenStorageService = inject(TokenStorageService);

  // Reactive signals for realm data using Angular 19+ patterns
  private readonly _currentRealm = signal<{ orgName: string; domainName: string; projectName: string; teamName: string } | null>(null);
  public readonly currentRealm = computed(() => this._currentRealm());

  // REMOVED: HttpClient injection and environment references - no longer needed since API calls are disabled

  constructor() {
    this.logger.info('OrgConfigService initialized for Experience Studio');
    // Initialize realm data from current context
    this.initializeRealmData();
  }

  /**
   * Initialize realm data from current user context using Angular 19+ patterns
   */
  private initializeRealmData(): void {
    const realmData = this.getCurrentRealm();
    this._currentRealm.set(realmData);
    this.logger.debug('Initialized realm data:', realmData);
  }

  /**
   * Get organization hierarchy data
   * DISABLED: API calls to /organization/hierarchy endpoint are prevented to avoid network requests
   * Returns hardcoded mock data directly using Angular 19+ patterns with RxJS of() operator
   */
  // public getOrganizationHierarchy(): Observable<Organization[]> {
  //   this.logger.info('API calls to /organization/hierarchy disabled - using hardcoded mock data for Experience Studio');

  //   // Return mock data directly as Observable using RxJS of() operator
  //   // This maintains the same interface as API calls but eliminates network requests
  //   return of(MOCK_ORGANIZATION_DATA);
  // }

  // COMMENTED OUT: Original API implementation disabled to prevent network requests
  // public getOrganizationHierarchy(): Observable<Organization[]> {
  //   // Try Experience Studio API first
  //   const experienceStudioUrl = `${environment.experienceApiUrl}/organization/hierarchy`;

  //   return this.http.get<Organization[]>(experienceStudioUrl).pipe(
  //     catchError((experienceStudioError) => {
  //       this.logger.warn('Experience Studio API failed, trying console API:', experienceStudioError);

  //       // Fallback to auth API
  //       const authUrl = `${environment.experianceApiAuthUrl}/organization/hierarchy`;
  //       return this.http.get<Organization[]>(authUrl).pipe(
  //         catchError((authError) => {
  //           this.logger.warn('Auth API also failed, using mock data:', authError);

  //           // Final fallback to mock data
  //           return of(MOCK_ORGANIZATION_DATA);
  //         })
  //       );
  //     })
  //   );
  // }

  /**
   * Get use case identifier for the current organization context
   * @param usecaseCode The use case code to build identifier for
   * @returns The formatted use case identifier
   */
  public getUseCaseIdentifier(usecaseCode: string): string {
    const path = this.tokenStorageService.getCookie('org_path');
    let usecaseIdentifier = '';

    if (path) {
      const orgPath = path?.split('::')[0]?.replace(/ /g, '_')?.toUpperCase();
      usecaseIdentifier = `${usecaseCode}@${orgPath}`;
    } else {
      // Fallback identifier for Experience Studio
      usecaseIdentifier = `${usecaseCode}@ASCENDION@ENGINEERING@EXPERIENCE_STUDIO@FRONTEND_TEAM`;
    }

    this.logger.debug('Generated use case identifier:', usecaseIdentifier);
    return usecaseIdentifier;
  }

  /**
   * Extract realm information from the current user context
   * @returns Realm information extracted from cookies or user context
   */
  public getCurrentRealm(): { orgName: string; domainName: string; projectName: string; teamName: string } | null {
    const orgPath = this.tokenStorageService.getCookie('org_path');

    if (orgPath) {
      try {
        const pathParts = orgPath.split('::');
        const namePath = pathParts[0] || '';
        const names = namePath.split('@');

        return {
          orgName: names[0] || 'Ascendion',
          domainName: names[1] || 'Engineering',
          projectName: names[2] || 'Experience Studio',
          teamName: names[3] || 'Frontend Team'
        };
      } catch (error) {
        this.logger.error('Error parsing org_path cookie:', error);
      }
    }

    // Fallback realm for Experience Studio
    return {
      orgName: 'Ascendion',
      domainName: 'Engineering',
      projectName: 'Experience Studio',
      teamName: 'Frontend Team'
    };
  }

  /**
   * Get the current organization name for display
   * @returns The organization name to display in the header
   */
  public getDisplayOrgName(): string {
    const realm = this.getCurrentRealm();
    return realm?.orgName || 'Ascendion';
  }
}
