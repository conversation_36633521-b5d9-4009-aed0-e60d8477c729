import { Injectable, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, timer, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import {
  MockDesignTokens,
  ApplicationLogEntry,
  WireframeGenerationStep,
  WireframeGenerationProgress,
  MOCK_DESIGN_TOKENS,
  MOCK_WIREFRAME_STEPS,
  MOCK_APPLICATION_LOGS,
  generateProgressLogEntry
} from '../interfaces/wireframe-mock-data.interface';
import { createLogger } from '../utils/logger';

@Injectable({
  providedIn: 'root'
})
export class WireframeMockDataService {
  private readonly logger = createLogger('WireframeMockDataService');
  private readonly destroyRef = inject(DestroyRef);

  // State subjects
  private readonly designTokensSubject = new BehaviorSubject<MockDesignTokens | null>(null);
  private readonly applicationLogsSubject = new BehaviorSubject<ApplicationLogEntry[]>([]);
  private readonly generationProgressSubject = new BehaviorSubject<WireframeGenerationProgress>({
    currentStep: 0,
    totalSteps: MOCK_WIREFRAME_STEPS.length,
    steps: [...MOCK_WIREFRAME_STEPS],
    isComplete: false,
    hasError: false
  });

  // Control subjects
  private readonly stopGeneration$ = new Subject<void>();
  private isGenerationActive = false;
  private logUpdateInterval: any;
  private stepUpdateInterval: any;

  // Public observables
  public readonly designTokens$ = this.designTokensSubject.asObservable();
  public readonly applicationLogs$ = this.applicationLogsSubject.asObservable();
  public readonly generationProgress$ = this.generationProgressSubject.asObservable();

  // Computed observables
  public readonly currentStep$ = this.generationProgress$.pipe(
    map(progress => progress.steps[progress.currentStep]),
    takeUntilDestroyed(this.destroyRef)
  );

  public readonly isGenerationComplete$ = this.generationProgress$.pipe(
    map(progress => progress.isComplete),
    takeUntilDestroyed(this.destroyRef)
  );

  constructor() {
    this.logger.info('🎯 WireframeMockDataService initialized');
  }

  /**
   * Start the wireframe generation mock process
   * This coordinates all the timing and data updates
   */
  startWireframeGeneration(): void {
    if (this.isGenerationActive) {
      this.logger.warn('⚠️ Generation already active, ignoring start request');
      return;
    }

    this.logger.info('🚀 Starting wireframe generation mock process');
    this.isGenerationActive = true;
    this.resetState();

    // ENHANCED: Provide design tokens immediately for artifacts tab
    this.logger.info('📋 Adding design tokens to artifacts immediately');
    this.designTokensSubject.next(MOCK_DESIGN_TOKENS);

    // Start application logs updates every 5 seconds
    this.startApplicationLogsUpdates();

    // Start stepper updates every 10 seconds
    this.startStepperUpdates();
  }

  /**
   * Complete the wireframe generation process
   * This should be called when the actual /wireframe-generation/generate API completes
   */
  completeWireframeGeneration(): void {
    this.logger.info('✅ Completing wireframe generation mock process');
    this.isGenerationActive = false;
    this.stopGeneration$.next();

    // Clear intervals
    if (this.logUpdateInterval) {
      clearInterval(this.logUpdateInterval);
      this.logUpdateInterval = null;
    }
    if (this.stepUpdateInterval) {
      clearInterval(this.stepUpdateInterval);
      this.stepUpdateInterval = null;
    }

    // Mark generation as complete
    const currentProgress = this.generationProgressSubject.value;
    const updatedSteps = currentProgress.steps.map(step => ({
      ...step,
      status: 'completed' as const,
      timestamp: new Date().toISOString()
    }));

    this.generationProgressSubject.next({
      ...currentProgress,
      currentStep: currentProgress.totalSteps - 1,
      steps: updatedSteps,
      isComplete: true
    });

    // Add final completion log
    const completionLog: ApplicationLogEntry = {
      id: `completion-${Date.now()}`,
      timestamp: new Date().toISOString(),
      level: 'success',
      message: 'Wireframe generation process completed successfully',
      component: 'WireframeGenerator',
      details: 'All components and artifacts have been generated and are ready for use',
      duration: 45.0
    };

    const currentLogs = this.applicationLogsSubject.value;
    this.applicationLogsSubject.next([...currentLogs, completionLog]);
  }

  /**
   * Stop the wireframe generation process (for cleanup or errors)
   */
  stopWireframeGeneration(): void {
    this.logger.info('🛑 Stopping wireframe generation mock process');
    this.isGenerationActive = false;
    this.stopGeneration$.next();

    // Clear intervals
    if (this.logUpdateInterval) {
      clearInterval(this.logUpdateInterval);
      this.logUpdateInterval = null;
    }
    if (this.stepUpdateInterval) {
      clearInterval(this.stepUpdateInterval);
      this.stepUpdateInterval = null;
    }
  }

  /**
   * Reset all state to initial values
   */
  private resetState(): void {
    this.designTokensSubject.next(null);
    this.applicationLogsSubject.next([]);
    this.generationProgressSubject.next({
      currentStep: 0,
      totalSteps: MOCK_WIREFRAME_STEPS.length,
      steps: MOCK_WIREFRAME_STEPS.map(step => ({ ...step, status: 'pending' })),
      isComplete: false,
      hasError: false
    });
  }

  /**
   * Start updating application logs every 5 seconds
   */
  private startApplicationLogsUpdates(): void {
    let logIndex = 0;
    const initialLogs = [...MOCK_APPLICATION_LOGS];

    // Add initial log immediately
    this.applicationLogsSubject.next([initialLogs[0]]);
    logIndex = 1;

    this.logUpdateInterval = setInterval(() => {
      if (!this.isGenerationActive) return;

      const currentLogs = this.applicationLogsSubject.value;

      if (logIndex < initialLogs.length) {
        // Add predefined logs
        const newLog = {
          ...initialLogs[logIndex],
          timestamp: new Date().toISOString()
        };
        this.applicationLogsSubject.next([...currentLogs, newLog]);
        logIndex++;
      } else {
        // Generate additional progress logs
        const progressLog = generateProgressLogEntry(logIndex - initialLogs.length, new Date());
        this.applicationLogsSubject.next([...currentLogs, progressLog]);
      }
    }, 5000);
  }

  /**
   * Start updating stepper every 10 seconds
   */
  private startStepperUpdates(): void {
    let currentStepIndex = 0;

    this.stepUpdateInterval = setInterval(() => {
      if (!this.isGenerationActive || currentStepIndex >= MOCK_WIREFRAME_STEPS.length) return;

      const currentProgress = this.generationProgressSubject.value;
      const updatedSteps = [...currentProgress.steps];

      // Mark current step as active
      if (currentStepIndex < updatedSteps.length) {
        updatedSteps[currentStepIndex] = {
          ...updatedSteps[currentStepIndex],
          status: 'active',
          timestamp: new Date().toISOString()
        };

        // Mark previous step as completed
        if (currentStepIndex > 0) {
          updatedSteps[currentStepIndex - 1] = {
            ...updatedSteps[currentStepIndex - 1],
            status: 'completed',
            duration: Math.random() * 8 + 2 // Random duration between 2-10 seconds
          };
        }

        this.generationProgressSubject.next({
          ...currentProgress,
          currentStep: currentStepIndex,
          steps: updatedSteps
        });

        this.logger.info(`📈 Step ${currentStepIndex + 1}/${MOCK_WIREFRAME_STEPS.length}: ${updatedSteps[currentStepIndex].title}`);
        currentStepIndex++;
      }
    }, 10000);
  }

  /**
   * Get current design tokens (for immediate access)
   */
  getCurrentDesignTokens(): MockDesignTokens | null {
    return this.designTokensSubject.value;
  }

  /**
   * Get current application logs (for immediate access)
   */
  getCurrentApplicationLogs(): ApplicationLogEntry[] {
    return this.applicationLogsSubject.value;
  }

  /**
   * Get current generation progress (for immediate access)
   */
  getCurrentGenerationProgress(): WireframeGenerationProgress {
    return this.generationProgressSubject.value;
  }

  /**
   * Check if generation is currently active
   */
  isActive(): boolean {
    return this.isGenerationActive;
  }

  /**
   * Handle error during wireframe generation
   */
  handleGenerationError(error: any): void {
    this.logger.error('❌ Wireframe generation error:', error);
    this.isGenerationActive = false;
    this.stopGeneration$.next();

    // Clear intervals
    if (this.logUpdateInterval) {
      clearInterval(this.logUpdateInterval);
      this.logUpdateInterval = null;
    }
    if (this.stepUpdateInterval) {
      clearInterval(this.stepUpdateInterval);
      this.stepUpdateInterval = null;
    }

    // Mark generation as failed
    const currentProgress = this.generationProgressSubject.value;
    const updatedSteps = currentProgress.steps.map((step, index) => ({
      ...step,
      status: index <= currentProgress.currentStep ? 'error' as const : 'pending' as const,
      timestamp: new Date().toISOString()
    }));

    this.generationProgressSubject.next({
      ...currentProgress,
      steps: updatedSteps,
      isComplete: false,
      hasError: true,
      errorMessage: error.message || 'An error occurred during wireframe generation'
    });

    // Add error log
    const errorLog: ApplicationLogEntry = {
      id: `error-${Date.now()}`,
      timestamp: new Date().toISOString(),
      level: 'error',
      message: 'Wireframe generation failed',
      component: 'WireframeGenerator',
      details: error.message || 'Unknown error occurred during generation process'
    };

    const currentLogs = this.applicationLogsSubject.value;
    this.applicationLogsSubject.next([...currentLogs, errorLog]);
  }

  /**
   * Reset generation state for cleanup
   */
  resetGenerationState(): void {
    this.logger.info('🔄 Resetting wireframe generation state');
    this.stopWireframeGeneration();
    this.resetState();
  }
}
