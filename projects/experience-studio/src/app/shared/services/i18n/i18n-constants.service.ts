import { Injectable, inject, computed } from '@angular/core';
import { I18nService } from './i18n.service';

/**
 * I18n Constants Service for Experience Studio
 * 
 * Provides translated versions of application constants and dynamic content
 * that were previously hardcoded. This service maintains backward compatibility
 * while enabling internationalization of dynamic content like animated texts,
 * button labels, and configuration options.
 * 
 * Features:
 * - Reactive translated constants using Angular 19+ signals
 * - Backward compatibility with existing constant usage
 * - Dynamic content translation for animations and prompts
 * - Type-safe constant access
 * - Automatic updates when language changes
 */
@Injectable({
  providedIn: 'root'
})
export class I18nConstantsService {
  private readonly i18nService = inject(I18nService);

  /**
   * Animated texts for image-to-application flow
   */
  readonly imageToAppAnimatedTexts = computed(() => {
    const texts = this.i18nService.translate('constants.animatedTexts.imageToApp');
    return Array.isArray(texts) ? texts : [
      this.i18nService.translate('constants.animatedTexts.imageToApp.0'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.1'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.2'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.3'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.4'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.5'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.6'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.7'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.8'),
      this.i18nService.translate('constants.animatedTexts.imageToApp.9')
    ].filter(text => text && !text.startsWith('constants.'));
  });

  /**
   * Animated texts for UI design generation
   */
  readonly uiDesignAnimatedTexts = computed(() => {
    return [
      this.i18nService.translate('constants.animatedTexts.uiDesign.0'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.1'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.2'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.3'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.4'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.5'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.6'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.7'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.8'),
      this.i18nService.translate('constants.animatedTexts.uiDesign.9')
    ].filter(text => text && !text.startsWith('constants.'));
  });

  /**
   * Animated texts for prompt-to-application flow
   */
  readonly promptToAppAnimatedTexts = computed(() => {
    return [
      this.i18nService.translate('constants.animatedTexts.promptToApp.0'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.1'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.2'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.3'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.4'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.5'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.6'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.7'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.8'),
      this.i18nService.translate('constants.animatedTexts.promptToApp.9')
    ].filter(text => text && !text.startsWith('constants.'));
  });

  /**
   * Technology options with translated labels
   */
  readonly techOptions = computed(() => [
    {
      name: this.i18nService.translate('constants.techOptions.react'),
      icon: 'assets/icons/awe_react.svg',
      value: 'react',
      isLocalSvg: true
    },
    {
      name: this.i18nService.translate('constants.techOptions.angular'),
      icon: 'assets/icons/awe_angular.svg',
      value: 'angular',
      isLocalSvg: true,
      disabled: true
    },
    {
      name: this.i18nService.translate('constants.techOptions.vue'),
      icon: 'assets/icons/awe_vue.svg',
      value: 'vue',
      isLocalSvg: true,
      disabled: true
    }
  ]);

  /**
   * Prefilled prompt for prompt-to-application with document upload
   */
  readonly promptToAppPrefilledPrompt = computed(() => 
    this.i18nService.translate('constants.prefilledPrompts.promptToApp')
  );

  /**
   * Studio cards configuration with translated content
   */
  readonly studioCards = computed(() => [
    {
      id: 1,
      title: this.i18nService.translate('landing.cards.generateWireframes.title'),
      description: this.i18nService.translate('landing.cards.generateWireframes.description'),
      image: 'assets/cards-images/ui_design.svg',
      path: 'prompt',
      type: 'generate-ui-design',
      disabled: true,
      priority: 'high' as const
    },
    {
      id: 2,
      title: this.i18nService.translate('landing.cards.imageToApplication.title'),
      description: this.i18nService.translate('landing.cards.imageToApplication.description'),
      image: 'assets/cards-images/app_generation.svg',
      path: 'prompt',
      type: 'image-to-application',
      disabled: true,
      priority: 'high' as const
    },
    {
      id: 3,
      title: this.i18nService.translate('landing.cards.promptToApplication.title'),
      description: this.i18nService.translate('landing.cards.promptToApplication.description'),
      image: 'assets/cards-images/prompt-to-code.svg',
      path: 'prompt',
      type: 'prompt-to-application',
      disabled: true,
      priority: 'high' as const
    },
    {
      id: 4,
      title: this.i18nService.translate('landing.cards.designAccessibility.title'),
      description: this.i18nService.translate('landing.cards.designAccessibility.description'),
      image: 'assets/cards-images/design_analysis.svg',
      path: '#',
      type: 'design-accessibility',
      disabled: true,
      priority: 'low' as const,
      alwaysDisabled: true
    }
  ]);

  /**
   * Navigation items with translated labels
   */
  readonly navigationItems = computed(() => [
    {
      route: '/image-to-code',
      displayText: this.i18nService.translate('landing.cards.imageToApplication.title'),
      icon: 'assets/icons/awe_i2c.svg',
      description: this.i18nService.translate('landing.cards.imageToApplication.description')
    },
    {
      route: '/prompt-to-code',
      displayText: this.i18nService.translate('landing.cards.promptToApplication.title'),
      icon: 'assets/icons/awe_p2c.svg',
      description: this.i18nService.translate('landing.cards.promptToApplication.description')
    },
    {
      route: '/design-analysis',
      displayText: this.i18nService.translate('landing.cards.designAccessibility.title'),
      icon: 'assets/icons/awe_accessibility.svg',
      description: this.i18nService.translate('landing.cards.designAccessibility.description')
    }
  ]);

  /**
   * Toast messages for different application states
   */
  readonly toastMessages = {
    startingGeneration: (type: string) => 
      this.i18nService.translate('toast.messages.startingGeneration', { type }),
    featureInBuild: () => 
      this.i18nService.translate('toast.messages.featureInBuild'),
    waitingForServices: () => 
      this.i18nService.translate('toast.messages.waitingForServices'),
    contactAdmin: () => 
      this.i18nService.translate('toast.messages.contactAdmin'),
    applicationReady: () => 
      this.i18nService.translate('toast.messages.applicationReady'),
    changesSaved: () => 
      this.i18nService.translate('toast.messages.changesSaved'),
    savingChanges: () => 
      this.i18nService.translate('toast.messages.savingChanges'),
    loadingResources: () => 
      this.i18nService.translate('toast.messages.loadingResources')
  };

  /**
   * Placeholder texts for different input contexts
   */
  readonly placeholders = {
    default: () => this.i18nService.translate('promptBar.placeholders.default'),
    imageToApp: () => this.i18nService.translate('promptBar.placeholders.imageToApp'),
    promptToApp: () => this.i18nService.translate('promptBar.placeholders.promptToApp'),
    chatDefault: () => this.i18nService.translate('chatWindow.placeholders.default'),
    chatImageUpload: () => this.i18nService.translate('chatWindow.placeholders.imageUpload'),
    chatPromptToApp: () => this.i18nService.translate('chatWindow.placeholders.promptToApp'),
    chatWireframe: () => this.i18nService.translate('chatWindow.placeholders.wireframe')
  };

  /**
   * Validation messages with parameter support
   */
  readonly validationMessages = {
    required: () => this.i18nService.translate('common.validation.required'),
    email: () => this.i18nService.translate('common.validation.email'),
    minLength: (min: number) => this.i18nService.translate('common.validation.minLength', { min }),
    maxLength: (max: number) => this.i18nService.translate('common.validation.maxLength', { max }),
    pattern: () => this.i18nService.translate('common.validation.pattern'),
    numeric: () => this.i18nService.translate('common.validation.numeric'),
    alphanumeric: () => this.i18nService.translate('common.validation.alphanumeric'),
    fileSize: () => this.i18nService.translate('promptBar.validation.fileSize'),
    fileType: () => this.i18nService.translate('promptBar.validation.fileType'),
    folderUpload: () => this.i18nService.translate('promptBar.validation.folderUpload'),
    maxFiles: (max: number) => this.i18nService.translate('promptBar.validation.maxFiles', { max }),
    empty: () => this.i18nService.translate('promptBar.validation.empty'),
    tooLong: () => this.i18nService.translate('promptBar.validation.tooLong')
  };

  /**
   * Button labels for common actions
   */
  readonly buttonLabels = {
    submit: () => this.i18nService.translate('common.buttons.submit'),
    cancel: () => this.i18nService.translate('common.buttons.cancel'),
    save: () => this.i18nService.translate('common.buttons.save'),
    delete: () => this.i18nService.translate('common.buttons.delete'),
    edit: () => this.i18nService.translate('common.buttons.edit'),
    close: () => this.i18nService.translate('common.buttons.close'),
    back: () => this.i18nService.translate('common.buttons.back'),
    next: () => this.i18nService.translate('common.buttons.next'),
    previous: () => this.i18nService.translate('common.buttons.previous'),
    retry: () => this.i18nService.translate('common.buttons.retry'),
    refresh: () => this.i18nService.translate('common.buttons.refresh'),
    export: () => this.i18nService.translate('common.buttons.export'),
    loading: () => this.i18nService.translate('common.buttons.loading'),
    tryExperienceStudio: () => this.i18nService.translate('common.buttons.tryExperienceStudio'),
    getStarted: () => this.i18nService.translate('common.buttons.getStarted'),
    signOut: () => this.i18nService.translate('common.buttons.signOut'),
    retryGeneration: () => this.i18nService.translate('common.buttons.retryGeneration')
  };

  /**
   * Tab labels for code window and other tabbed interfaces
   */
  readonly tabLabels = {
    preview: () => this.i18nService.translate('tabs.preview'),
    code: () => this.i18nService.translate('tabs.code'),
    artifacts: () => this.i18nService.translate('tabs.artifacts'),
    overview: () => this.i18nService.translate('tabs.overview'),
    history: () => this.i18nService.translate('tabs.history'),
    logs: () => this.i18nService.translate('tabs.logs'),
    settings: () => this.i18nService.translate('tabs.settings')
  };

  /**
   * Status and progress messages
   */
  readonly statusMessages = {
    generating: () => this.i18nService.translate('chatWindow.stepper.generating'),
    building: () => this.i18nService.translate('chatWindow.stepper.building'),
    deploying: () => this.i18nService.translate('chatWindow.stepper.deploying'),
    completed: () => this.i18nService.translate('chatWindow.stepper.completed'),
    failed: () => this.i18nService.translate('chatWindow.stepper.failed'),
    loading: () => this.i18nService.translate('common.messages.loading'),
    saving: () => this.i18nService.translate('common.messages.saving'),
    saved: () => this.i18nService.translate('common.messages.saved'),
    error: () => this.i18nService.translate('common.messages.error'),
    success: () => this.i18nService.translate('common.messages.success')
  };

  /**
   * Get random animated texts for a specific context
   * @param context The context for animated texts
   * @param count Number of texts to return
   * @returns Array of random animated texts
   */
  getRandomAnimatedTexts(context: 'imageToApp' | 'uiDesign' | 'promptToApp', count: number = 3): string[] {
    let texts: string[] = [];
    
    switch (context) {
      case 'imageToApp':
        texts = this.imageToAppAnimatedTexts();
        break;
      case 'uiDesign':
        texts = this.uiDesignAnimatedTexts();
        break;
      case 'promptToApp':
        texts = this.promptToAppAnimatedTexts();
        break;
    }

    if (texts.length === 0) {
      return [];
    }

    // Shuffle and return requested count
    const shuffled = [...texts].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, texts.length));
  }

  /**
   * Get translated error message for error page letters
   * @param letter The error letter (e, r1, r2, o, r3)
   * @returns Translated error message
   */
  getErrorLetterMessage(letter: 'e' | 'r1' | 'r2' | 'o' | 'r3'): string {
    return this.i18nService.translate(`errors.page.letters.${letter}`);
  }

  /**
   * Get translated file upload tooltip based on context
   * @param context Upload context
   * @returns Translated tooltip text
   */
  getFileUploadTooltip(context: 'image' | 'documents' | 'mixed'): string {
    switch (context) {
      case 'image':
        return this.i18nService.translate('promptBar.fileUpload.attachImage');
      case 'documents':
        return this.i18nService.translate('promptBar.fileUpload.attachDocuments');
      case 'mixed':
        return this.i18nService.translate('promptBar.fileUpload.imageDocsFromComputer');
      default:
        return this.i18nService.translate('promptBar.fileUpload.attachImage');
    }
  }
}
