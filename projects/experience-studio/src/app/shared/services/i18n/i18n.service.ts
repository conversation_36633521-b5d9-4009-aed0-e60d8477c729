import { Injectable, inject, signal, computed, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { map, catchError, tap, shareReplay } from 'rxjs/operators';
import { createLogger } from '../../utils/logger';

/**
 * Supported languages configuration
 */
export interface LanguageConfig {
  readonly code: string;
  readonly name: string;
  readonly nativeName: string;
  readonly flag: string;
  readonly rtl?: boolean;
}

/**
 * Translation key structure for type safety
 */
export interface TranslationKeys {
  readonly [key: string]: string | TranslationKeys;
}

/**
 * Language loading state
 */
export interface LanguageLoadingState {
  readonly isLoading: boolean;
  readonly hasError: boolean;
  readonly errorMessage?: string;
}

/**
 * I18n service configuration
 */
export interface I18nConfig {
  readonly defaultLanguage: string;
  readonly fallbackLanguage: string;
  readonly supportedLanguages: readonly LanguageConfig[];
  readonly translationsPath: string;
  readonly storageKey: string;
  readonly enableDynamicLoading: boolean;
}

/**
 * Default configuration for Experience Studio i18n
 */
const DEFAULT_I18N_CONFIG: I18nConfig = {
  defaultLanguage: 'en',
  fallbackLanguage: 'en',
  supportedLanguages: [
    { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
    { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
    { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
    { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' }
  ],
  translationsPath: 'assets/i18n',
  storageKey: 'experience-studio-language',
  enableDynamicLoading: true
} as const;

/**
 * Experience Studio I18n Service
 * 
 * Provides comprehensive internationalization support using Angular 19+ patterns:
 * - Signal-based reactive state management
 * - Dynamic language loading for bundle optimization
 * - Type-safe translation keys
 * - Fallback mechanism for missing translations
 * - Zero breaking changes to existing functionality
 * 
 * @example
 * ```typescript
 * // In component
 * private readonly i18n = inject(I18nService);
 * 
 * // Reactive translation
 * readonly welcomeMessage = computed(() => 
 *   this.i18n.translate('landing.hero.title')
 * );
 * 
 * // Direct translation
 * getButtonLabel(): string {
 *   return this.i18n.translate('common.buttons.submit');
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class I18nService {
  private readonly http = inject(HttpClient);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('I18nService');

  // Configuration
  private readonly config: I18nConfig = DEFAULT_I18N_CONFIG;

  // Angular 19+ Signals for reactive state management
  private readonly currentLanguageCode = signal<string>(this.config.defaultLanguage);
  private readonly translations = signal<TranslationKeys>({});
  private readonly loadingState = signal<LanguageLoadingState>({
    isLoading: false,
    hasError: false
  });

  // Cache for loaded translations to optimize performance
  private readonly translationCache = new Map<string, TranslationKeys>();
  
  // Observable for language changes (for backward compatibility)
  private readonly languageChange$ = new BehaviorSubject<string>(this.config.defaultLanguage);

  // Computed properties for reactive access
  readonly currentLanguage = computed(() => {
    const code = this.currentLanguageCode();
    return this.config.supportedLanguages.find(lang => lang.code === code) || 
           this.config.supportedLanguages[0];
  });

  readonly isLoading = computed(() => this.loadingState().isLoading);
  readonly hasError = computed(() => this.loadingState().hasError);
  readonly errorMessage = computed(() => this.loadingState().errorMessage);

  // Supported languages as computed signal
  readonly supportedLanguages = computed(() => this.config.supportedLanguages);

  // Current language direction (for RTL support)
  readonly isRTL = computed(() => this.currentLanguage().rtl || false);

  constructor() {
    this.initializeService();
  }

  /**
   * Initialize the i18n service
   */
  private initializeService(): void {
    this.logger.info('🌐 Initializing I18n Service');
    
    // Load saved language preference
    const savedLanguage = this.getSavedLanguage();
    if (savedLanguage && this.isLanguageSupported(savedLanguage)) {
      this.currentLanguageCode.set(savedLanguage);
    }

    // Load initial translations
    this.loadTranslations(this.currentLanguageCode())
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => this.logger.info(`✅ Initial translations loaded for: ${this.currentLanguageCode()}`),
        error: (error) => this.logger.error('❌ Failed to load initial translations:', error)
      });

    // Set up language change subscription for cleanup
    this.languageChange$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(language => {
        this.logger.info(`🔄 Language changed to: ${language}`);
      });
  }

  /**
   * Change the current language
   * @param languageCode The language code to switch to
   * @returns Observable that completes when language is loaded
   */
  changeLanguage(languageCode: string): Observable<void> {
    if (!this.isLanguageSupported(languageCode)) {
      const error = `Unsupported language: ${languageCode}`;
      this.logger.error(error);
      return throwError(() => new Error(error));
    }

    if (languageCode === this.currentLanguageCode()) {
      return of(void 0);
    }

    this.logger.info(`🔄 Changing language to: ${languageCode}`);
    
    return this.loadTranslations(languageCode).pipe(
      tap(() => {
        this.currentLanguageCode.set(languageCode);
        this.saveLanguagePreference(languageCode);
        this.languageChange$.next(languageCode);
      }),
      map(() => void 0)
    );
  }

  /**
   * Get translation for a key with interpolation support
   * @param key Translation key (dot notation supported)
   * @param params Optional parameters for interpolation
   * @returns Translated string or key if not found
   */
  translate(key: string, params?: Record<string, any>): string {
    const translation = this.getTranslationValue(key);
    
    if (!translation) {
      this.logger.warn(`🔍 Translation not found for key: ${key}`);
      return key; // Return key as fallback
    }

    // Handle interpolation
    if (params && typeof translation === 'string') {
      return this.interpolateString(translation, params);
    }

    return typeof translation === 'string' ? translation : key;
  }

  /**
   * Get translation as a computed signal for reactive updates
   * @param key Translation key
   * @param params Optional parameters for interpolation
   * @returns Computed signal with translated value
   */
  translateSignal(key: string, params?: Record<string, any>) {
    return computed(() => this.translate(key, params));
  }

  /**
   * Check if a language is supported
   * @param languageCode Language code to check
   * @returns True if language is supported
   */
  isLanguageSupported(languageCode: string): boolean {
    return this.config.supportedLanguages.some(lang => lang.code === languageCode);
  }

  /**
   * Get the current language code
   * @returns Current language code
   */
  getCurrentLanguage(): string {
    return this.currentLanguageCode();
  }

  /**
   * Get observable for language changes (for backward compatibility)
   * @returns Observable that emits when language changes
   */
  getLanguageChange$(): Observable<string> {
    return this.languageChange$.asObservable();
  }

  /**
   * Preload translations for multiple languages
   * @param languageCodes Array of language codes to preload
   * @returns Observable that completes when all languages are loaded
   */
  preloadLanguages(languageCodes: string[]): Observable<void> {
    const validCodes = languageCodes.filter(code => this.isLanguageSupported(code));
    
    if (validCodes.length === 0) {
      return of(void 0);
    }

    this.logger.info(`📦 Preloading languages: ${validCodes.join(', ')}`);
    
    const loadPromises = validCodes.map(code => 
      this.loadTranslations(code).toPromise()
    );

    return new Observable(observer => {
      Promise.all(loadPromises)
        .then(() => {
          this.logger.info('✅ All languages preloaded successfully');
          observer.next();
          observer.complete();
        })
        .catch(error => {
          this.logger.error('❌ Failed to preload languages:', error);
          observer.error(error);
        });
    });
  }

  /**
   * Load translations for a specific language
   * @param languageCode Language code to load
   * @returns Observable with loaded translations
   */
  private loadTranslations(languageCode: string): Observable<TranslationKeys> {
    // Check cache first
    const cached = this.translationCache.get(languageCode);
    if (cached) {
      this.translations.set(cached);
      return of(cached);
    }

    // Set loading state
    this.loadingState.set({ isLoading: true, hasError: false });

    const translationUrl = `${this.config.translationsPath}/${languageCode}.json`;
    
    return this.http.get<TranslationKeys>(translationUrl).pipe(
      tap(translations => {
        // Cache the translations
        this.translationCache.set(languageCode, translations);
        this.translations.set(translations);
        
        // Clear loading state
        this.loadingState.set({ isLoading: false, hasError: false });
      }),
      catchError(error => {
        this.logger.error(`❌ Failed to load translations for ${languageCode}:`, error);
        
        // Set error state
        this.loadingState.set({
          isLoading: false,
          hasError: true,
          errorMessage: `Failed to load translations for ${languageCode}`
        });

        // Try fallback language if not already trying it
        if (languageCode !== this.config.fallbackLanguage) {
          this.logger.info(`🔄 Falling back to ${this.config.fallbackLanguage}`);
          return this.loadTranslations(this.config.fallbackLanguage);
        }

        return throwError(() => error);
      }),
      shareReplay(1)
    );
  }

  /**
   * Get translation value from nested object using dot notation
   * @param key Translation key with dot notation
   * @returns Translation value or null if not found
   */
  private getTranslationValue(key: string): string | null {
    const keys = key.split('.');
    let current: any = this.translations();

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }

    return typeof current === 'string' ? current : null;
  }

  /**
   * Interpolate string with parameters
   * @param template String template with {{param}} placeholders
   * @param params Parameters to interpolate
   * @returns Interpolated string
   */
  private interpolateString(template: string, params: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  /**
   * Save language preference to localStorage
   * @param languageCode Language code to save
   */
  private saveLanguagePreference(languageCode: string): void {
    try {
      localStorage.setItem(this.config.storageKey, languageCode);
    } catch (error) {
      this.logger.warn('Failed to save language preference:', error);
    }
  }

  /**
   * Get saved language preference from localStorage
   * @returns Saved language code or null
   */
  private getSavedLanguage(): string | null {
    try {
      return localStorage.getItem(this.config.storageKey);
    } catch (error) {
      this.logger.warn('Failed to get saved language preference:', error);
      return null;
    }
  }
}
