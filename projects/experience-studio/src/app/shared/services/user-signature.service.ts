import { Injectable , inject  } from '@angular/core';
// import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { TokenStorageService } from '@shared';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class UserSignatureService {
  private readonly fallbackEmail = '<EMAIL>';
  private tokenStorageService = inject(TokenStorageService);

  constructor() {
    // No need for subscription with token storage service
  }

  /**
   * Gets the user's email from token storage
   * Falls back to a default email if the user is not authenticated
   * @returns An Observable with the user's email
   */
  getUserSignature(): Observable<string> {
    const email = this.tokenStorageService.getDaUsername();
    return of(email || this.fallbackEmail);
  }

  /**
   * Gets the user's email synchronously (with fallback)
   * This should only be used when an Observable is not practical
   * @returns The user's email or fallback email
   */
  getUserSignatureSync(): string {
    const email = this.tokenStorageService.getDaUsername();
    return email || this.fallbackEmail;
  }
}
