import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

// Define interfaces for the application state
export interface ProjectState {
  projectId: string | null;
  jobId: string | null;
  userId: string | null; // Added user ID
  prompt: string | null;
  application: string | null;
  technology: string | null;
  designLibrary: string | null;
  imageUrl: string | null;
  imageDataUri: string | null;
  type: string | null;
  codeGenerated: boolean;
  generatedCode: any;
}

export interface AppState {
  project: ProjectState;
}

// Initial state values
const initialProjectState: ProjectState = {
  projectId: null,
  jobId: null,
  userId: null, // Added user ID
  prompt: null,
  application: null,
  technology: null,
  designLibrary: null,
  imageUrl: null,
  imageDataUri: null,
  type: null,
  codeGenerated: false,
  generatedCode: null
};

const initialAppState: AppState = {
  project: initialProjectState
};

@Injectable({
  providedIn: 'root'
})
export class AppStateService {
  // Private BehaviorSubject to hold the current state
  private state = new BehaviorSubject<AppState>(this.loadStateFromStorage() || initialAppState);

  // Public Observable for components to subscribe to
  public state$ = this.state.asObservable();


  // Project state as an Observable
  public project$ = this.state$.pipe(
    map(state => state.project)
  );

  constructor() {
    // Subscribe to state changes to persist to sessionStorage
    this.state$.subscribe(state => {
      this.saveStateToStorage(state);
    });
  }

  /**
   * Get the current state value
   */
  public getState(): AppState {
    return this.state.getValue();
  }

  /**
   * Get the current project state
   */
  public getProjectState(): ProjectState {
    return this.getState().project;
  }

  /**
   * Update the project state
   * @param projectState Partial project state to update
   */
  public updateProjectState(projectState: Partial<ProjectState>): void {
    const currentState = this.getState();
    this.state.next({
      ...currentState,
      project: {
        ...currentState.project,
        ...projectState
      }
    });
  }

  /**
   * Set project ID and job ID
   * @param projectId Project ID
   * @param jobId Job ID
   */
  public setProjectAndJobId(projectId: string, jobId: string): void {
    this.updateProjectState({ projectId, jobId });
  }

  /**
   * Set user ID
   * @param userId User ID
   */
  public setUserId(userId: string): void {
    this.updateProjectState({ userId });
  }

  /**
   * Set generated code
   * @param code Generated code
   */
  public setGeneratedCode(code: any): void {
    this.updateProjectState({
      generatedCode: code,
      codeGenerated: true
    });
  }

  /**
   * Set complete project selections
   * @param selections Complete project selections
   */
  public setCompleteSelections(selections: any): void {
    // Get the current state to preserve the user ID if it's not provided in selections
    const currentState = this.getProjectState();

    this.updateProjectState({
      projectId: selections.projectId || null,
      jobId: selections.jobId || null,
      userId: selections.userId || currentState.userId, // Preserve user ID if not provided
      prompt: selections.prompt || null,
      application: selections.application || null,
      technology: selections.technology || null,
      designLibrary: selections.designLibrary || null,
      imageUrl: selections.imageUrl || null,
      imageDataUri: selections.imageDataUri || null,
      type: selections.type || null,
      codeGenerated: selections.codeGenerated || false,
      generatedCode: selections.generatedCode || null
    });
  }

  /**
   * Reset the project state to initial values
   */
  public resetProjectState(): void {
    this.updateProjectState(initialProjectState);
  }

  /**
   * Save state to sessionStorage
   * @param state Current application state
   */
  private saveStateToStorage(state: AppState): void {
    try {
      // Store project state in sessionStorage for backward compatibility
      sessionStorage.setItem('completeSelections', JSON.stringify(state.project));

      // Also store the full state for future use
      sessionStorage.setItem('appState', JSON.stringify(state));
    } catch (e) {

    }
  }

  /**
   * Load state from sessionStorage
   * @returns Loaded application state or null if not found
   */
  private loadStateFromStorage(): AppState | null {
    try {
      // Try to load the full state first
      const storedState = sessionStorage.getItem('appState');
      if (storedState) {
        return JSON.parse(storedState);
      }

      // Fall back to loading just the project state for backward compatibility
      const storedSelections = sessionStorage.getItem('completeSelections');
      if (storedSelections) {
        const projectState = JSON.parse(storedSelections);
        return {
          project: {
            ...initialProjectState,
            ...projectState
          }
        };
      }
    } catch (e) {

    }

    return null;
  }
}
