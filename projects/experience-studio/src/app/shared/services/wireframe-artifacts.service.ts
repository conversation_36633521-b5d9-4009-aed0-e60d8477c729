import { Injectable, inject, DestroyRef, computed, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, interval, Subscription } from 'rxjs';
import { DesignToken, ApplicationLog, WireframeArtifactState } from '../interfaces/wireframe-artifacts.interface';
import { WireframeDesignSystemApiService } from './wireframe-design-system-api.service';
import { WireframeDesignToken } from '../interfaces/wireframe-design-system.interface';
import { createLogger } from '../utils/logger';

const MOCK_DESIGN_TOKENS: DesignToken[] = [
  {
    id: 'color-primary',
    name: 'Primary Color',
    value: '#8c65f7',
    type: 'color',
    category: 'colors',
    editable: true
  },
  {
    id: 'color-secondary',
    name: 'Secondary Color',
    value: '#e84393',
    type: 'color',
    category: 'colors',
    editable: true
  },
  {
    id: 'font-primary',
    name: 'Primary Font',
    value: 'Mulish',
    type: 'typography',
    category: 'typography',
    editable: true
  },
  {
    id: 'spacing-base',
    name: 'Base Spacing',
    value: '16px',
    type: 'spacing',
    category: 'spacing',
    editable: true
  }
];

const MOCK_LOG_MESSAGES = [
  {
    phase: 'initialization',
    message: 'Initializing wireframe generation process...',
    level: 'info' as const,
    progress: 10
  },
  {
    phase: 'analysis',
    message: 'Analyzing design requirements and constraints...',
    level: 'info' as const,
    progress: 20
  },
  {
    phase: 'layout',
    message: 'Identifying layout patterns from the prompt...',
    level: 'info' as const,
    progress: 30
  },
  {
    phase: 'grid',
    message: 'Generating responsive grid system...',
    level: 'info' as const,
    progress: 40
  },
  {
    phase: 'design-tokens',
    message: 'Applying design tokens to wireframe elements...',
    level: 'info' as const,
    progress: 50
  },
  {
    phase: 'components',
    message: 'Creating component hierarchy...',
    level: 'info' as const,
    progress: 60
  },
  {
    phase: 'responsive',
    message: 'Optimizing wireframe for responsive design...',
    level: 'info' as const,
    progress: 70
  },
  {
    phase: 'accessibility',
    message: 'Validating accessibility guidelines...',
    level: 'info' as const,
    progress: 80
  },
  {
    phase: 'finalization',
    message: 'Finalizing wireframe generation...',
    level: 'info' as const,
    progress: 90
  },
  {
    phase: 'documentation',
    message: 'Preparing design system documentation...',
    level: 'info' as const,
    progress: 100
  }
];

@Injectable({
  providedIn: 'root'
})
export class WireframeArtifactsService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly wireframeDesignSystemApi = inject(WireframeDesignSystemApiService);
  private readonly logger = createLogger('WireframeArtifactsService');

  private currentLogIndex = 0;
  private logInterval$ = new BehaviorSubject<number>(5000); // 5 seconds default
  private logUpdateSubscription?: Subscription;
  private isWireframeMode = signal<boolean>(false);

  // State management using signals
  private readonly state = signal<WireframeArtifactState>({
    designTokens: [],
    applicationLogs: [],
    isLoading: false,
    error: null
  });

  // Exposed computed signals for components
  readonly designTokens = computed(() => this.state().designTokens);
  readonly applicationLogs = computed(() => this.state().applicationLogs);
  readonly isLoading = computed(() => this.state().isLoading);
  readonly error = computed(() => this.state().error);

  // Expose isWireframeMode as a computed signal
  readonly isWireframeGenerationMode = computed(() => this.isWireframeMode());

  constructor() {
    // No automatic initialization - wait for explicit start
  }

  private initializeMockDataStream(): void {
    // Set initial design tokens
    this.updateState({ designTokens: MOCK_DESIGN_TOKENS });

    // Cleanup any existing subscription
    this.cleanupLogSubscription();

    // Start new log updates
    this.logUpdateSubscription = interval(this.logInterval$.value)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        if (this.isWireframeMode()) {
          this.addMockLog();
        }
      });
  }

  private cleanupLogSubscription(): void {
    if (this.logUpdateSubscription) {
      this.logUpdateSubscription.unsubscribe();
      this.logUpdateSubscription = undefined;
    }
  }

  private addMockLog(): void {
    const mockLogData = MOCK_LOG_MESSAGES[this.currentLogIndex % MOCK_LOG_MESSAGES.length];

    const newLog: ApplicationLog = {
      timestamp: new Date().toISOString(),
      level: mockLogData.level,
      message: mockLogData.message,
      details: `Phase: ${mockLogData.phase}, Progress: ${mockLogData.progress}%`
    };

    this.updateState({
      applicationLogs: [...this.state().applicationLogs, newLog]
    });

    this.currentLogIndex++;
  }

  private updateState(partialState: Partial<WireframeArtifactState>): void {
    this.state.update(currentState => ({
      ...currentState,
      ...partialState
    }));
  }

  /**
   * Fetch design tokens from the wireframe design system API
   * @param projectId Optional project ID for context
   */
  private fetchDesignTokensFromApi(projectId?: string): void {
    this.logger.info('🎨 Fetching design tokens from wireframe API', { projectId });

    this.wireframeDesignSystemApi.fetchDesignTokens(projectId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (tokens: WireframeDesignToken[]) => {
          this.logger.info(`✅ Received ${tokens.length} design tokens from API`);

          // Convert WireframeDesignToken to DesignToken format
          const convertedTokens: DesignToken[] = tokens.map(token => ({
            id: token.id,
            name: token.name,
            value: token.value,
            type: token.type,
            category: token.category,
            editable: token.editable
          }));

          // Update state with real tokens
          this.updateState({
            designTokens: convertedTokens,
            isLoading: false
          });
        },
        error: (error) => {
          this.logger.error('❌ Failed to fetch design tokens from API', error);

          // Fallback to mock data on API failure
          this.logger.info('🔄 Falling back to mock design tokens');
          this.updateState({
            designTokens: MOCK_DESIGN_TOKENS,
            isLoading: false,
            error: 'Failed to load design tokens from API, using fallback data'
          });
        }
      });
  }

  startArtifactsGeneration(projectId?: string): void {
    this.logger.info('🚀 Starting wireframe artifacts generation', { projectId });
    this.isWireframeMode.set(true);
    this.updateState({
      isLoading: true,
      error: null,
      applicationLogs: [],
      designTokens: [] // Start with empty array, will be populated by API
    });
    this.currentLogIndex = 0;

    // Fetch real design tokens from API
    this.fetchDesignTokensFromApi(projectId);

    // Start mock data stream for logs
    this.initializeMockDataStream();
  }

  stopArtifactsGeneration(): void {
    this.isWireframeMode.set(false);
    this.updateState({ isLoading: false });
    this.cleanupLogSubscription();
  }

  setError(error: string): void {
    this.updateState({
      error,
      isLoading: false
    });
    // Don't stop the wireframe mode or cleanup - let the caller decide
  }

  reset(): void {
    this.isWireframeMode.set(false);
    this.updateState({
      designTokens: [],
      applicationLogs: [],
      isLoading: false,
      error: null
    });
    this.currentLogIndex = 0;
    this.cleanupLogSubscription();
  }

  setLogInterval(interval: number): void {
    this.logInterval$.next(interval);
    // Restart the log stream if we're in wireframe mode
    if (this.isWireframeMode()) {
      this.initializeMockDataStream();
    }
  }
}
