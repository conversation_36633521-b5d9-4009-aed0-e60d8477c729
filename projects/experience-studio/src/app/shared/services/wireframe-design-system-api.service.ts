import { Injectable, inject, DestroyRef, signal, computed } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError, retry, delay } from 'rxjs/operators';
import { environment } from 'projects/experience-studio/src/environments/environment';
import { createLogger } from '../utils/logger';
import {
  WireframeDesignSystemResponse,
  WireframeDesignToken,
  WireframeDesignSystemError,
  WireframeDesignSystemState,
  WireframeColorToken,
  WireframeTypographyToken,
  WireframeSpacingToken
} from '../interfaces/wireframe-design-system.interface';

/**
 * Wireframe Design System API Service
 * 
 * Handles API calls to the /wireframe-generation/design-system endpoint
 * using Angular 19+ modern patterns with inject() and signals.
 * Provides design tokens for wireframe generation artifacts display.
 */
@Injectable({
  providedIn: 'root'
})
export class WireframeDesignSystemApiService {
  private readonly http = inject(HttpClient);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('WireframeDesignSystemApiService');

  // API endpoint configuration
  private readonly apiUrl = environment.experienceApiUrl;
  private readonly endpoint = '/wireframe-generation/design-system';

  // State management using signals
  private readonly state = signal<WireframeDesignSystemState>({
    designTokens: [],
    isLoading: false,
    error: null,
    lastUpdated: null
  });

  // Exposed computed signals for components
  readonly designTokens = computed(() => this.state().designTokens);
  readonly isLoading = computed(() => this.state().isLoading);
  readonly error = computed(() => this.state().error);
  readonly lastUpdated = computed(() => this.state().lastUpdated);

  constructor() {
    this.logger.info('🎨 Wireframe Design System API Service initialized');
  }

  /**
   * Fetch design tokens from the wireframe generation API
   * @param projectId Optional project ID for context
   * @returns Observable with design tokens
   */
  fetchDesignTokens(projectId?: string): Observable<WireframeDesignToken[]> {
    this.logger.info('🔄 Fetching wireframe design tokens from API', { projectId });
    
    // Update loading state
    this.updateState({ isLoading: true, error: null });

    // Build API URL
    const url = `${this.apiUrl}${this.endpoint}`;
    let params = new HttpParams();
    if (projectId) {
      params = params.set('project_id', projectId);
    }

    return this.http.get<WireframeDesignSystemResponse>(url, { params }).pipe(
      retry({
        count: 2,
        delay: (error, retryCount) => {
          this.logger.warn(`🔄 Retrying API call (attempt ${retryCount})`, error);
          return of(null).pipe(delay(1000 * retryCount));
        }
      }),
      map((response: WireframeDesignSystemResponse) => this.processApiResponse(response)),
      catchError(error => this.handleApiError(error)),
      takeUntilDestroyed(this.destroyRef)
    );
  }

  /**
   * Process the API response and convert to internal format
   */
  private processApiResponse(response: WireframeDesignSystemResponse): WireframeDesignToken[] {
    this.logger.info('✅ Processing wireframe design system API response', response);

    try {
      const tokens: WireframeDesignToken[] = [];

      // Process color tokens
      if (response.design_tokens?.colors) {
        tokens.push(...this.mapColorTokens(response.design_tokens.colors));
      }

      // Process typography tokens
      if (response.design_tokens?.typography) {
        tokens.push(...this.mapTypographyTokens(response.design_tokens.typography));
      }

      // Process spacing tokens
      if (response.design_tokens?.spacing) {
        tokens.push(...this.mapSpacingTokens(response.design_tokens.spacing));
      }

      // Update state with successful response
      this.updateState({
        designTokens: tokens,
        isLoading: false,
        error: null,
        lastUpdated: new Date()
      });

      this.logger.info(`✅ Successfully processed ${tokens.length} design tokens`);
      return tokens;

    } catch (error) {
      this.logger.error('❌ Error processing API response', error);
      throw error;
    }
  }

  /**
   * Map color tokens from API format to internal format
   */
  private mapColorTokens(colors: WireframeColorToken[]): WireframeDesignToken[] {
    return colors.map(color => ({
      id: color.id,
      name: color.name,
      value: color.value,
      type: 'color' as const,
      category: color.category || 'Colors',
      editable: color.editable,
      description: `Color token: ${color.name}`,
      usage: ['ui-elements', 'backgrounds', 'text']
    }));
  }

  /**
   * Map typography tokens from API format to internal format
   */
  private mapTypographyTokens(typography: WireframeTypographyToken[]): WireframeDesignToken[] {
    return typography.map(font => ({
      id: font.id,
      name: font.name,
      value: font.value,
      type: 'typography' as const,
      category: font.category || 'Typography',
      editable: font.editable,
      description: `Typography token: ${font.name}`,
      usage: ['headings', 'body-text', 'labels']
    }));
  }

  /**
   * Map spacing tokens from API format to internal format
   */
  private mapSpacingTokens(spacing: WireframeSpacingToken[]): WireframeDesignToken[] {
    return spacing.map(space => ({
      id: space.id,
      name: space.name,
      value: space.value,
      type: 'spacing' as const,
      category: space.category || 'Spacing',
      editable: space.editable,
      description: `Spacing token: ${space.name}`,
      usage: ['margins', 'padding', 'gaps']
    }));
  }

  /**
   * Handle API errors with proper logging and state updates
   */
  private handleApiError(error: HttpErrorResponse): Observable<never> {
    this.logger.error('❌ Wireframe design system API error', error);

    let errorMessage = 'Failed to fetch design tokens';
    
    if (error.status === 0) {
      errorMessage = 'Network error - please check your connection';
    } else if (error.status >= 400 && error.status < 500) {
      errorMessage = 'Invalid request - please try again';
    } else if (error.status >= 500) {
      errorMessage = 'Server error - please try again later';
    }

    // Update state with error
    this.updateState({
      isLoading: false,
      error: errorMessage,
      lastUpdated: new Date()
    });

    return throwError(() => new Error(errorMessage));
  }

  /**
   * Update the internal state
   */
  private updateState(updates: Partial<WireframeDesignSystemState>): void {
    this.state.update(current => ({
      ...current,
      ...updates
    }));
  }

  /**
   * Reset the service state
   */
  reset(): void {
    this.logger.info('🔄 Resetting wireframe design system service state');
    this.state.set({
      designTokens: [],
      isLoading: false,
      error: null,
      lastUpdated: null
    });
  }

  /**
   * Get current design tokens synchronously
   */
  getCurrentTokens(): WireframeDesignToken[] {
    return this.state().designTokens;
  }

  /**
   * Check if service has loaded tokens
   */
  hasTokens(): boolean {
    return this.state().designTokens.length > 0;
  }
}
