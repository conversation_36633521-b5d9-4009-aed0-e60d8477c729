import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Artifact, ArtifactState } from '../models/artifacts.model';

@Injectable({
  providedIn: 'root'
})
export class ArtifactsService {
  private artifactsStateSubject = new BehaviorSubject<ArtifactState>({
    items: [],
    loading: false
  });

  artifactsState$: Observable<ArtifactState> = this.artifactsStateSubject.asObservable();

  addArtifact(artifact: Artifact): void {
    const currentState = this.artifactsStateSubject.value;
    this.artifactsStateSubject.next({
      ...currentState,
      items: [...currentState.items, artifact]
    });
  }

  removeArtifact(id: string): void {
    const currentState = this.artifactsStateSubject.value;
    this.artifactsStateSubject.next({
      ...currentState,
      items: currentState.items.filter(item => item.id !== id)
    });
  }

  setSelectedArtifact(id: string): void {
    this.artifactsStateSubject.next({
      ...this.artifactsStateSubject.value,
      selectedId: id
    });
  }

  setLoading(loading: boolean): void {
    this.artifactsStateSubject.next({
      ...this.artifactsStateSubject.value,
      loading
    });
  }

  setError(error: string): void {
    this.artifactsStateSubject.next({
      ...this.artifactsStateSubject.value,
      error
    });
  }

  clearError(): void {
    this.artifactsStateSubject.next({
      ...this.artifactsStateSubject.value,
      error: undefined
    });
  }

  getSelectedArtifact(): Artifact | undefined {
    const state = this.artifactsStateSubject.value;
    return state.items.find(item => item.id === state.selectedId);
  }

  /**
   * Reset artifacts state to initial clean state
   * Used when navigating back to home screen
   */
  resetState(): void {
    this.artifactsStateSubject.next({
      items: [],
      loading: false,
      selectedId: undefined,
      error: undefined
    });
  }
}
