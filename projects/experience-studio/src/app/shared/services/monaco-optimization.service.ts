import { createLogger } from '../utils/logger';
import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, of, from } from 'rxjs';
import { map, catchError, shareReplay, tap } from 'rxjs/operators';

export interface MonacoOptimizationConfig {
  enableProgressiveLoading: boolean;
  enableFeatureLazyLoading: boolean;
  enableWorkerOptimization: boolean;
  maxConcurrentWorkers: number;
  featureLoadingThreshold: number; // ms
}

export interface MonacoFeatureLoadingState {
  feature: string;
  loaded: boolean;
  loading: boolean;
  error?: string;
  loadTime?: number;
}

export interface MonacoBundleMetrics {
  mainBundleSize: number;
  workerBundleSizes: { [key: string]: number };
  totalSize: number;
  compressionRatio: number;
  loadTime: number;
  featuresEnabled: string[];
  featuresDisabled: string[];
}

@Injectable({
  providedIn: 'root'
})
export class MonacoOptimizationService {
  private readonly logger = createLogger('MonacoOptimizationService');
  private readonly config: MonacoOptimizationConfig = {
    enableProgressiveLoading: true,
    enableFeatureLazyLoading: true,
    enableWorkerOptimization: true,
    maxConcurrentWorkers: 3,
    featureLoadingThreshold: 100
  };

  private readonly featureLoadingState$ = new BehaviorSubject<Map<string, MonacoFeatureLoadingState>>(new Map());
  private readonly _bundleMetrics$ = new BehaviorSubject<MonacoBundleMetrics | null>(null);
  private readonly optimizationEnabled$ = new BehaviorSubject<boolean>(true);

  // Advanced features that can be loaded on-demand
  private readonly advancedFeatures = [
    'suggest', 'parameterHints', 'codeAction', 'codelens', 
    'format', 'documentSymbols', 'quickOutline', 'rename'
  ];

  /**
   * Get current feature loading states
   */
  get featureStates$(): Observable<Map<string, MonacoFeatureLoadingState>> {
    return this.featureLoadingState$.asObservable();
  }

  /**
   * Get bundle metrics
   */
  get bundleMetrics$(): Observable<MonacoBundleMetrics | null> {
    return this._bundleMetrics$.asObservable();
  }

  /**
   * Check if optimization is enabled
   */
  get isOptimizationEnabled$(): Observable<boolean> {
    return this.optimizationEnabled$.asObservable();
  }

  /**
   * Initialize Monaco optimization with performance tracking
   */
  initializeOptimization(): Observable<boolean> {
    const startTime = performance.now();
    
    return from(this.measureBundleSize()).pipe(
      tap(metrics => {
        const loadTime = performance.now() - startTime;
        this._bundleMetrics$.next({
          ...metrics,
          loadTime
        });
      }),
      map(() => true),
      catchError(error => {
        this.logger.error('Monaco optimization initialization failed:', error);
        this.optimizationEnabled$.next(false);
        return of(false);
      }),
      shareReplay(1)
    );
  }

  /**
   * Load advanced Monaco features on-demand
   */
  loadAdvancedFeature(featureName: string): Observable<boolean> {
    if (!this.advancedFeatures.includes(featureName)) {
      return of(false);
    }

    const currentStates = this.featureLoadingState$.value;
    const featureState = currentStates.get(featureName);

    // Return immediately if already loaded or loading
    if (featureState?.loaded) {
      return of(true);
    }
    if (featureState?.loading) {
      return this.waitForFeatureLoad(featureName);
    }

    // Start loading the feature
    this.updateFeatureState(featureName, { 
      feature: featureName, 
      loaded: false, 
      loading: true 
    });

    const startTime = performance.now();
    return from(this.dynamicallyLoadFeature(featureName)).pipe(
      tap(() => {
        const loadTime = performance.now() - startTime;
        this.updateFeatureState(featureName, {
          feature: featureName,
          loaded: true,
          loading: false,
          loadTime
        });
      }),
      map(() => true),
      catchError(error => {
        this.updateFeatureState(featureName, {
          feature: featureName,
          loaded: false,
          loading: false,
          error: error.message
        });
        return of(false);
      })
    );
  }

  /**
   * Get optimization recommendations based on usage patterns
   */
  getOptimizationRecommendations(): Observable<string[]> {
    return this.bundleMetrics$.pipe(
      map(metrics => {
        if (!metrics) return [];

        const recommendations: string[] = [];

        // Bundle size recommendations
        if (metrics.totalSize > 5 * 1024 * 1024) { // 5MB
          recommendations.push('Consider disabling unused language workers');
        }

        if (metrics.compressionRatio < 0.3) {
          recommendations.push('Enable gzip compression for better transfer efficiency');
        }

        // Load time recommendations
        if (metrics.loadTime > 2000) { // 2 seconds
          recommendations.push('Consider implementing progressive loading for better perceived performance');
        }

        // Feature recommendations
        const heavyFeatures = ['suggest', 'parameterHints', 'format'];
        const enabledHeavyFeatures = metrics.featuresEnabled.filter(f => heavyFeatures.includes(f));
        
        if (enabledHeavyFeatures.length > 2) {
          recommendations.push('Consider lazy loading heavy features like IntelliSense and formatting');
        }

        return recommendations;
      })
    );
  }

  /**
   * Measure current bundle size and composition
   */
  private async measureBundleSize(): Promise<MonacoBundleMetrics> {
    // This would typically measure actual bundle sizes from webpack stats
    // For now, we'll return estimated values based on our configuration
    
    const featuresEnabled = [
      'coreCommands', 'clipboard', 'find', 'bracketMatching', 'indentation',
      'linesOperations', 'wordOperations', 'folding', 'hover', 'contextmenu',
      'gotoLine', 'multicursor', 'smartSelect', 'cursorUndo', 'accessibilityHelp',
      'fontZoom', 'links'
    ];

    const featuresDisabled = [
      'suggest', 'parameterHints', 'codeAction', 'codelens', 'format',
      'documentSymbols', 'quickOutline', 'rename', 'snippets', 'colorPicker'
    ];

    return {
      mainBundleSize: 3742400, // Current size from build output
      workerBundleSizes: {
        'ts.worker.js': 6494426,
        'css.worker.js': 1503220,
        'html.worker.js': 1134042,
        'json.worker.js': 821953,
        'editor.worker.js': 664955
      },
      totalSize: 14360996, // Sum of all bundles
      compressionRatio: 0.21, // Estimated compression ratio
      loadTime: 0, // Will be set by caller
      featuresEnabled,
      featuresDisabled
    };
  }

  /**
   * Dynamically load a Monaco feature
   */
  private async dynamicallyLoadFeature(featureName: string): Promise<void> {
    // Simulate dynamic feature loading
    // In a real implementation, this would dynamically import Monaco modules
    await new Promise(resolve => setTimeout(resolve, this.config.featureLoadingThreshold));
    
    this.logger.info(`Dynamically loaded Monaco feature: ${featureName}`);
  }

  /**
   * Update feature loading state
   */
  private updateFeatureState(featureName: string, state: MonacoFeatureLoadingState): void {
    const currentStates = new Map(this.featureLoadingState$.value);
    currentStates.set(featureName, state);
    this.featureLoadingState$.next(currentStates);
  }

  /**
   * Wait for a feature to finish loading
   */
  private waitForFeatureLoad(featureName: string): Observable<boolean> {
    return this.featureStates$.pipe(
      map(states => states.get(featureName)),
      map(state => state?.loaded || false)
    );
  }

  /**
   * Enable or disable optimization
   */
  setOptimizationEnabled(enabled: boolean): void {
    this.optimizationEnabled$.next(enabled);
  }

  /**
   * Get current configuration
   */
  getConfig(): MonacoOptimizationConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<MonacoOptimizationConfig>): void {
    Object.assign(this.config, updates);
  }
}
