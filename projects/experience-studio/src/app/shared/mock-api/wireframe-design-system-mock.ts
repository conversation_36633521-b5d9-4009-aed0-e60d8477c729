import { createLogger } from '../utils/logger';
/**
 * Mock API endpoint for wireframe design system
 * This simulates the /wireframe-generation/design-system endpoint
 *
 * Usage: This can be used with tools like json-server, MSW, or integrated into a backend
 */

import { WireframeDesignSystemResponse } from '../interfaces/wireframe-design-system.interface';

const logger = createLogger('WireframeDesignSystemMock');

/**
 * Mock response data for the wireframe design system API
 * This represents what the real API should return
 */
export const MOCK_WIREFRAME_DESIGN_SYSTEM_RESPONSE: WireframeDesignSystemResponse = {
  design_tokens: {
    colors: [
      {
        id: 'color-primary-50',
        name: 'Primary 50',
        value: '#eff6ff',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-primary-500',
        name: 'Primary 500',
        value: '#3b82f6',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-primary-900',
        name: 'Primary 900',
        value: '#1e3a8a',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-secondary-50',
        name: 'Secondary 50',
        value: '#fdf4ff',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-secondary-500',
        name: 'Secondary 500',
        value: '#a855f7',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-secondary-900',
        name: 'Secondary 900',
        value: '#581c87',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-neutral-50',
        name: 'Neutral 50',
        value: '#f9fafb',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-neutral-500',
        name: 'Neutral 500',
        value: '#6b7280',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-neutral-900',
        name: 'Neutral 900',
        value: '#111827',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-success-500',
        name: 'Success 500',
        value: '#10b981',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-warning-500',
        name: 'Warning 500',
        value: '#f59e0b',
        category: 'Colors',
        editable: true
      },
      {
        id: 'color-error-500',
        name: 'Error 500',
        value: '#ef4444',
        category: 'Colors',
        editable: true
      }
    ],
    typography: [
      {
        id: 'font-heading-xl',
        name: 'Heading XL',
        value: 'Inter, 700, 36px',
        category: 'Typography',
        editable: true
      },
      {
        id: 'font-heading-lg',
        name: 'Heading Large',
        value: 'Inter, 600, 24px',
        category: 'Typography',
        editable: true
      },
      {
        id: 'font-body-lg',
        name: 'Body Large',
        value: 'Inter, 400, 18px',
        category: 'Typography',
        editable: true
      },
      {
        id: 'font-body-md',
        name: 'Body Medium',
        value: 'Inter, 400, 16px',
        category: 'Typography',
        editable: true
      },
      {
        id: 'font-body-sm',
        name: 'Body Small',
        value: 'Inter, 400, 14px',
        category: 'Typography',
        editable: true
      }
    ],
    spacing: [
      {
        id: 'spacing-xs',
        name: 'Extra Small',
        value: '4px',
        category: 'Spacing',
        editable: true
      },
      {
        id: 'spacing-sm',
        name: 'Small',
        value: '8px',
        category: 'Spacing',
        editable: true
      },
      {
        id: 'spacing-md',
        name: 'Medium',
        value: '16px',
        category: 'Spacing',
        editable: true
      },
      {
        id: 'spacing-lg',
        name: 'Large',
        value: '24px',
        category: 'Spacing',
        editable: true
      },
      {
        id: 'spacing-xl',
        name: 'Extra Large',
        value: '32px',
        category: 'Spacing',
        editable: true
      },
      {
        id: 'spacing-2xl',
        name: '2X Large',
        value: '48px',
        category: 'Spacing',
        editable: true
      }
    ]
  },
  status: 'success',
  message: 'Design tokens retrieved successfully'
};

/**
 * Express.js style mock endpoint
 * This can be used with json-server or similar tools
 */
export const wireframeDesignSystemEndpoint = {
  path: '/wireframe-generation/design-system',
  method: 'GET',
  handler: (req: any, res: any) => {
    // Simulate some processing time
    setTimeout(() => {
      const projectId = req.query.project_id;
      
      // Log the request for debugging
      logger.info('🎨 Mock API: Wireframe Design System Request', {
        projectId,
        timestamp: new Date().toISOString(),
        query: req.query
      });

      // Return the mock response
      res.status(200).json(MOCK_WIREFRAME_DESIGN_SYSTEM_RESPONSE);
    }, 500); // 500ms delay to simulate network latency
  }
};

/**
 * JSON Server compatible mock data
 * Save this as db.json for json-server
 */
export const JSON_SERVER_MOCK_DATA = {
  "wireframe-generation": {
    "design-system": MOCK_WIREFRAME_DESIGN_SYSTEM_RESPONSE
  }
};

/**
 * Fetch API compatible mock for testing
 */
export const mockFetch = (url: string, options?: RequestInit): Promise<Response> => {
  if (url.includes('/wireframe-generation/design-system')) {
    logger.info('🎨 Mock Fetch: Wireframe Design System Request', {
      url,
      options,
      timestamp: new Date().toISOString()
    });

    return Promise.resolve(new Response(
      JSON.stringify(MOCK_WIREFRAME_DESIGN_SYSTEM_RESPONSE),
      {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    ));
  }

  // Fallback to actual fetch for other URLs
  return fetch(url, options);
};

/**
 * Angular HttpClient interceptor mock
 */
export const WIREFRAME_DESIGN_SYSTEM_MOCK_INTERCEPTOR = {
  intercept: (req: any, next: any) => {
    if (req.url.includes('/wireframe-generation/design-system')) {
      logger.info('🎨 Mock Interceptor: Wireframe Design System Request', {
        url: req.url,
        params: req.params,
        timestamp: new Date().toISOString()
      });

      // Return mock response
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            status: 200,
            body: MOCK_WIREFRAME_DESIGN_SYSTEM_RESPONSE
          });
        }, 500);
      });
    }

    return next.handle(req);
  }
};
