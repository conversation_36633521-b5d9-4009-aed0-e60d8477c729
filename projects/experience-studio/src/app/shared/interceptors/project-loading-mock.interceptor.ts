import { createLogger } from '../utils/logger';
import { HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { of } from 'rxjs';
import { delay } from 'rxjs/operators';

const logger = createLogger('ProjectLoadingMockInterceptor');

const mockProjectData = {

    "project_details": {
      "project_id": "0abfeb76-fe96-4bfc-91da-cb8d79c42078",
      "project_name": "FitDash",
      "project_description": "A fitness dashboard that allows users to track their workouts, view personal records, and monitor weekly stats in a sleek, modern interface.",
      "created_at": "2025-07-22T08:59:25.528225+00:00",
      "last_modified": "2025-07-22T08:59:31.213717+00:00",
      "created_by": "5d0ddc8d-b1dd-4165-96e9-c1522914948e",
      "project_state": "ACTIVE"
    },
    "project_settings": {
      "project_id": "0abfeb76-fe96-4bfc-91da-cb8d79c42078",
      "device": "web",
      "framework": "react",
      "design_system": "tailwind",
      "generation_type": "app_generation"
    },
    "repository_details": {
      "project_id": "0abfeb76-fe96-4bfc-91da-cb8d79c42078",
      "vcs_provider": "azure_devops",
      "clone_url": "https://<EMAIL>/ascendionava/experience-studio-preview-projects/_git/fitdash-948e-2078",
      "deployment_provider": "azure_static_web_apps",
      "deployed_url": "https://yellow-mushroom-0bbe5cc0f.6.azurestaticapps.net"
    },
    "conversation": [
      {
        "message_id": "556a0703-a3b1-4fc2-901b-4daa798997f9",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "user",
        "content": "add a header bart",
        "ui_metadata": null,
        "created_at": "2025-07-22T10:23:50.034573+00:00"
      },
      {
        "message_id": "311c6f54-454e-4f99-a3a1-f41bc03f684e",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "assistant",
        "content": "Great news! I've successfully regenerated the code as per your request. Let me know if there's anything else you need.",
        "ui_metadata": null,
        "created_at": "2025-07-22T10:23:50.253213+00:00"
      },
      {
        "message_id": "2df285b9-0855-449c-8c0b-375108a391ea",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "capsule",
        "content": "486c6c5c70afc9dc99e814df49963248c41e3c20",
        "ui_metadata": "[{\"data\": [{\"content\": \"import React from 'react';\\nimport { Search } from 'lucide-react';\\nimport { Input } from '@/components/ui/input';\\n\\nconst TopHeader: React.FC = () => {\\n  return (\\n    <header className=\\\"py-6 flex items-center justify-between w-full\\\">\\n      <div>\\n        <h1 className=\\\"text-3xl md:text-4xl font-bold text-foreground\\\">Welcome back, Alex</h1>\\n        <p className=\\\"text-muted-foreground mt-1\\\">Ready to crush your goals today? Let's get it.</p>\\n      </div>\\n      <div className=\\\"relative ml-4 hidden sm:block\\\">\\n        <Search className=\\\"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground\\\" />\\n        <Input\\n          type=\\\"search\\\"\\n          placeholder=\\\"Search workouts...\\\"\\n          className=\\\"w-full rounded-lg bg-background pl-10 md:w-[200px] lg:w-[320px]\\\"\\n        />\\n      </div>\\n    </header>\\n  );\\n};\\n\\nexport default TopHeader;\", \"fileName\": \"src/components/Dashboard/TopHeader.tsx\"}, {\"content\": \"import type { Config } from \\\"tailwindcss\\\";\\n\\nexport default {\\n\\tdarkMode: [\\\"class\\\"],\\n\\tcontent: [\\n\\t\\t\\\"./pages/**/*.{ts,tsx}\\\",\\n\\t\\t\\\"./components/**/*.{ts,tsx}\\\",\\n\\t\\t\\\"./app/**/*.{ts,tsx}\\\",\\n\\t\\t\\\"./src/**/*.{ts,tsx}\\\",\\n\\t],\\n\\tprefix: \\\"\\\",\\n\\ttheme: {\\n\\t\\tcontainer: {\\n\\t\\t\\tcenter: true,\\n\\t\\t\\tpadding: '2rem',\\n\\t\\t\\tscreens: {\\n\\t\\t\\t\\t'2xl': '1400px'\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\textend: {\\n      fontFamily: {\\n        sans: [\\\"Inter\\\", \\\"system-ui\\\", \\\"sans-serif\\\"],\\n      },\\n\\t\\t\\tcolors: {\\n\\t\\t\\t\\tborder: 'hsl(var(--border))',\\n\\t\\t\\t\\tinput: 'hsl(var(--input))',\\n\\t\\t\\t\\tring: 'hsl(var(--ring))',\\n\\t\\t\\t\\tbackground: 'hsl(var(--background))',\\n\\t\\t\\t\\tforeground: 'hsl(var(--foreground))',\\n        surface: 'hsl(var(--card))', // from PRD\\n\\t\\t\\t\\tprimary: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--primary))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--primary-foreground))'\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\tsecondary: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--secondary))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--secondary-foreground))'\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\tdestructive: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--destructive))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--destructive-foreground))'\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\tmuted: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--muted))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--muted-foreground))'\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\taccent: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--accent))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--accent-foreground))'\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\tpopover: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--popover))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--popover-foreground))'\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\tcard: {\\n\\t\\t\\t\\t\\tDEFAULT: 'hsl(var(--card))',\\n\\t\\t\\t\\t\\tforeground: 'hsl(var(--card-foreground))'\\n\\t\\t\\t\\t},\\n        accentSecondary: '#5BB29D',\\n        success: '#10B981',\\n\\t\\t\\t},\\n\\t\\t\\tborderRadius: {\\n\\t\\t\\t\\tlg: 'var(--radius)',\\n\\t\\t\\t\\tmd: 'calc(var(--radius) - 2px)',\\n\\t\\t\\t\\tsm: 'calc(var(--radius) - 4px)'\\n\\t\\t\\t},\\n\\t\\t\\tkeyframes: {\\n\\t\\t\\t\\t'accordion-down': {\\n\\t\\t\\t\\t\\tfrom: {\\n\\t\\t\\t\\t\\t\\theight: '0'\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\tto: {\\n\\t\\t\\t\\t\\t\\theight: 'var(--radix-accordion-content-height)'\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t},\\n\\t\\t\\t\\t'accordion-up': {\\n\\t\\t\\t\\t\\tfrom: {\\n\\t\\t\\t\\t\\t\\theight: 'var(--radix-accordion-content-height)'\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\tto: {\\n\\t\\t\\t\\t\\t\\theight: '0'\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n\\t\\t\\t},\\n\\t\\t\\tanimation: {\\n\\t\\t\\t\\t'accordion-down': 'accordion-down 0.2s ease-out',\\n\\t\\t\\t\\t'accordion-up': 'accordion-up 0.2s ease-out'\\n\\t\\t\\t}\\n\\t\\t}\\n\\t},\\n\\tplugins: [require(\\\"tailwindcss-animate\\\")],\\n} satisfies Config;\\n\", \"fileName\": \"tailwind.config.ts\"}], \"type\": \"files\"}]",
        "created_at": "2025-07-22T10:23:50.476225+00:00"
      },
      {
        "message_id": "2b9e9d09-5a2d-451c-97e0-a3fb837bfb76",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "user",
        "content": "change it into dark mode",
        "ui_metadata": null,
        "created_at": "2025-07-22T10:28:55.505671+00:00"
      },
      {
        "message_id": "8c54bbd6-5b69-4fcd-be4f-956433e6d138",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "assistant",
        "content": "Great news! I've successfully regenerated the code as per your request. Let me know if there's anything else you need.",
        "ui_metadata": null,
        "created_at": "2025-07-22T10:28:55.715169+00:00"
      },
      {
        "message_id": "4008933b-0233-4601-956b-d1a774bcbc93",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "capsule",
        "content": "37815f3472b1e605956445602c04d6d9acc5da94",
        "ui_metadata": "[{\"data\": [{\"content\": \"import React from 'react';\", \"fileName\": \"src/pages/Index.tsx\"}, {\"content\": \"import type { Config } from \\\"tailwindcss\\\";\", \"fileName\": \"tailwind.config.ts\"}], \"type\": \"files\"}]",
        "created_at": "2025-07-22T10:28:55.932267+00:00"
      },
      {
        "message_id": "6f280b5f-5057-4855-aae9-07ae24a87f0f",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "user",
        "content": "i need a login page",
        "ui_metadata": null,
        "created_at": "2025-07-22T10:52:49.3"
      },
      {
        "message_id": "59970db4-acfb-4f7f-af07-981c353abc60",
        "conversation_id": "dfb1f3a9-2809-4039-92d5-e8f72421818c",
        "message_type": "capsule",
        "content": "da68273beb124489fe4d9323b1dd0b5b95212543",
        "ui_metadata": "[{\"data\": [{\"content\": \"import React from 'react';\", \"fileName\": \"src/pages/Login.tsx\"}, {\"content\": \"import type { Config } from \\\"tailwindcss\\\";\", \"fileName\": \"tailwind.config.ts\"}], \"type\": \"files\"}]",
        "created_at": "2025-07-22T10:52:49.5"
      }
    ]
  };

export const ProjectLoadingMockInterceptor: HttpInterceptorFn = (req, next) => {
  // Only intercept project loading requests for our test project
  if (req.url.includes('/common/project/0abfeb76-fe96-4bfc-91da-cb8d79c42078')) {
    logger.info('[MOCK INTERCEPTOR] Intercepting project loading request:', req.url);

    const response = new HttpResponse({
      status: 200,
      body: mockProjectData
    });

    // Add a small delay to simulate network request
    return of(response).pipe(delay(500));
  }

  // For all other requests, pass through normally
  return next(req);
};
