.container-fluid {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  -ms-overflow-style: none;
  scrollbar-width: none;
  .experience-nav-header {
    background:none !important;
  }
}

:host ::ng-deep {
  shared-app-header {
    padding-top: 0 !important;
  }

  .outer-box .container {
    background: none !important;
  }
  .outer-box .center-content-wrapper{
    margin-top:2rem !important;
  }

  .header-shadow {
    background: none !important;
  }

  .nav-menu .nav-items {
    stroke-width: 1px !important;
    transition: all 0.3s ease !important;
    background: var(--nav-items-fill) !important;
    stroke: var(--nav-items-stroke) !important;
    // opacity:50% !important;
  }
  .header-wrapper .header-shadow{
    background:none !important;
  }
  .item-label{
    color:var(--body-text-color) !important;
  }
  // Generic nav-items styles if needed
  .nav-items {
    stroke-width: 1px !important;
    transition: all 0.3s ease !important;
    background: var(--nav-items-fill) !important;
    stroke: var(--nav-items-stroke) !important;
  }

  .header-wrapper{

    // background: var(--nav-items-fill) !important;
    background: transparent !important;
    stroke-width: 1px !important;
    stroke: var(--nav-items-stroke) !important;
  }

  .ava-dropdown .dropdown-toggle{
    background: transparent !important;
    border: 0.5px solid var(--prompt-bar-border-color) !important;
  }



.org-path-dropdown-container {
  .org-path-trigger {

    background:transparent !important;

  }

  .org-path-dropdown-container{

    background:transparent !important;
  }

  .org-path-popover{

    background:transparent !important;

  }
}
}

::ng-deep .experience-nav-header awe-header [center-content] {
  margin-bottom: 0 !important;
}

::ng-deep .experience-nav-header .outer-box .center-content-wrapper {
  margin-top: 0 !important
}
