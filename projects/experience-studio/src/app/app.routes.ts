import { Routes } from '@angular/router';
// import { AuthGuard } from '@shared/auth/guards/auth.guard';
// import { CallbackComponent } from '@shared/auth/components/callback/callback.component';
import { PromptSubmissionGuard } from './shared/guards/prompt-submission.guard';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';
import { authGuard, CallbackComponent } from '@shared';
import { LoginComponent } from '@shared';


export const routes: Routes = [
  { path: 'callback', component: CallbackComponent },

  // Main landing page route
  {
    path: '',
    canActivate: [authGuard],
    loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
    data: { preload: true },
  },
  // Legacy route - keep for backward compatibility
  {
    path: 'prompt',
    canActivate: [authGuard],
    loadComponent: () =>
      import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
        m => m.PromptContentComponent
      ),
  },
  // Legacy route - keep for backward compatibility
  {
    path: 'code-preview',
    canActivate: [authGuard, PromptSubmissionGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
  },
  // Generate Application route - handles both prompt and code-preview states
  {
    path: 'generate-application',
    canActivate: [authGuard, CardSelectionGuard],
    loadComponent: () =>
      import('./shared/components/application-flow/application-flow.component').then(
        m => m.ApplicationFlowComponent
      ),
    data: { cardType: 'Generate Application' },
  },
  // Generate Application project loading route
  {
    path: 'generate-application/projects/:projectId',
    canActivate: [authGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Generate Application',
      isProjectLoading: true,
      skipGuards: true,
    },
  },
  // New route for project loading
  {
    path: 'project/:projectId',
    canActivate: [authGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Project Loading',
      isProjectLoading: true,
      skipGuards: true,
    },
  },
  // Generate Wireframes route - handles both prompt and code-preview states
  {
    path: 'generate-ui-design',
    canActivate: [authGuard, CardSelectionGuard],
    loadComponent: () =>
      import('./shared/components/ui-design-flow/ui-design-flow.component').then(
        m => m.UIDesignFlowComponent
      ),
    data: { cardType: 'Generate Wireframes' },
  },
  // Generate Wireframes project loading route
  {
    path: 'generate-ui-design/projects/:projectId',
    canActivate: [authGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Generate Wireframes',
      isProjectLoading: true,
      skipGuards: true,
    },
  },
  // Catch-all route - redirect to login
  {
    path: '**',
    redirectTo: '/login',
  },
];
