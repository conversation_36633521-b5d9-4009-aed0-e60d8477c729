// Helper function to safely get environment variables from window.env
const getRequiredEnv = (key: string): string => {
  interface EnvWindow extends Window { env?: Record<string, string>; }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(`Environment variable '${key}' is not defined in window.env.`);
  }
  return String(value);
};

// const dynamicBaseUrl: string = getRequiredEnv('baseUrl');

export const environment = {
  production: false,
  elderWandUrl: getRequiredEnv('elderWandUrl'),
  apiUrl: getRequiredEnv('baseUrl'),
  experienceApiUrl: getRequiredEnv('experienceApiUrl'),
  experianceApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  experianceRedirectUrl: getRequiredEnv('experienceStudioUrl'),
  // Studio app URLs
  productStudioUrl: getRequiredEnv('productStudioUrl'),
  consoleUrl: getRequiredEnv('consoleUrl'),
  getApiUrl: (endpoint: string) => {
    const baseUrl = getRequiredEnv('baseUrl');
    return `${baseUrl}${endpoint}`;
  }
};

