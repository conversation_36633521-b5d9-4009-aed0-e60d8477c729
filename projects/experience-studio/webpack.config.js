const { ModuleFederationPlugin } = require('webpack').container;

module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'experience-studio',
    globalObject: 'self',
  },
  optimization: {
    runtimeChunk: false,
  },
  experiments: {
    outputModule: true,
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(ttf|woff|woff2|eot)$/,
        use: ['file-loader'],
        type: 'asset/resource',
        generator: {
          filename: 'assets/fonts/[name][ext]',
        },
      },
    ],
  },
  resolve: {
    fallback: {
      path: false,
      fs: false,
    },
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'experienceStudio',
      library: { type: 'module' },
      filename: 'remoteEntry.js',
      exposes: {
        './AppComponent': './projects/experience-studio/src/app/app.component.ts',
      },
      shared: {
        '@angular/core': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/common': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/router': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/animations': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
      },
    }),
  ],
};
