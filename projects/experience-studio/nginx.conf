# =============================================================================
# Nginx Configuration for Experience Studio Angular Application
# =============================================================================
#
# This configuration provides:
# - Security headers and rate limiting
# - Gzip compression and caching
# - SPA routing support
# - Module Federation support
# - Health check endpoint
# =============================================================================

# =============================================================================
# Global Configuration
# =============================================================================
# Set PID file to writable location for non-root user
pid /tmp/nginx.pid;

# =============================================================================
# Events Configuration
# =============================================================================
events {
    worker_connections 1024;
}

# =============================================================================
# HTTP Configuration
# =============================================================================
http {
    # =====================================================================
    # Basic Settings
    # =====================================================================
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # =====================================================================
    # Logging Configuration
    # =====================================================================
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # =====================================================================
    # Performance Settings
    # =====================================================================
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;

    # =====================================================================
    # Gzip Compression
    # =====================================================================
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # =====================================================================
    # Security Headers
    # =====================================================================
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' https: http: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: data: blob: cdn.jsdelivr.net unpkg.com cdnjs.cloudflare.com cdn.tailwindcss.com *.unpkg.com *.jsdelivr.net *.cdnjs.cloudflare.com; script-src-elem 'self' 'unsafe-inline' https: http: data: blob: cdn.jsdelivr.net unpkg.com cdnjs.cloudflare.com cdn.tailwindcss.com *.unpkg.com *.jsdelivr.net *.cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https: http: data: fonts.googleapis.com cdn.jsdelivr.net cdnjs.cloudflare.com cdn.tailwindcss.com *.unpkg.com *.jsdelivr.net *.cdnjs.cloudflare.com fonts.gstatic.com; img-src 'self' https: http: data: blob: cdn.brandfetch.io pixabay.com *.pixabay.com *.unsplash.com *.pexels.com; font-src 'self' https: http: data: blob: fonts.gstatic.com fonts.googleapis.com cdn.jsdelivr.net cdnjs.cloudflare.com unpkg.com *.unpkg.com *.jsdelivr.net *.cdnjs.cloudflare.com; connect-src 'self' https: http: ws: wss:; frame-src 'self' https: http: data: blob:; worker-src 'self' blob: data:; frame-ancestors 'self'; object-src 'none'; base-uri 'self';" always;

    # =====================================================================
    # Rate Limiting
    # =====================================================================
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # =====================================================================
    # Server Configuration
    # =====================================================================
    server {
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Hide nginx version for security
        server_tokens off;

        # =================================================================
        # Redirect Rules for Missing Trailing Slash
        # =================================================================
        # Handle case where nginx routes /experience to this container
        location = /experience {
            return 301 /experience/;
        }

        # =================================================================
        # Environment Configuration Script
        # =================================================================
        location = /assets/env.js {
            add_header Content-Type application/javascript;
            try_files $uri =404;
        }

        # =================================================================
        # Angular SPA Routing
        # =================================================================
        location / {
            try_files $uri $uri/ /index.html;
        }

        # =================================================================
        # Base URL Support (for /experience path)
        # =================================================================
        # This allows the app to work when served from /experience base path
        location ~ ^/(experience/)?(.*)$ {
            try_files /$2 /$2/ /index.html;
        }

        # =================================================================
        # Environment Configuration Script
        # =================================================================
        location /env-config.js {
            add_header Content-Type "application/javascript";
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            try_files $uri =404;
        }

        # =================================================================
        # Module Federation Support
        # =================================================================
        location /remoteEntry.js {
            add_header Cache-Control "public, max-age=31536000, immutable";
            try_files $uri =404;
        }

        # =================================================================
        # Static Assets Caching
        # =================================================================
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
        }

        # =================================================================
        # HTML Files (No Cache)
        # =================================================================
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }

        # =================================================================
        # API Proxy (Commented out - uncomment when backend is available)
        # =================================================================
        # location /api/ {
        #     limit_req zone=api burst=20 nodelay;
        #     proxy_pass http://backend:3000;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        # }

        # =================================================================
        # Health Check Endpoint
        # =================================================================
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # =================================================================
        # Security: Deny Access to Hidden Files
        # =================================================================
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # =================================================================
        # Security: Deny Access to Backup Files
        # =================================================================
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}
