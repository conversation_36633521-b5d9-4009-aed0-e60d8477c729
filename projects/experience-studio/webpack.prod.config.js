const baseConfig = require('./webpack.config');
// const webpack = require('webpack'); // Unused - removed for linting
const { ModuleFederationPlugin } = require('webpack').container;

module.exports = {
  ...baseConfig,
  mode: 'production',
  output: {
    publicPath: 'auto',
    uniqueName: 'experience-studio',
    globalObject: 'self',
  },
  optimization: {
    runtimeChunk: false,
  },
  experiments: {
    outputModule: true,
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.ttf$/,
        use: ['file-loader'],
      },
    ],
  },
  resolve: {
    fallback: {
      path: false,
      fs: false,
    },
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'experienceStudio',
      library: { type: 'module' },
      filename: 'remoteEntry.js',
      exposes: {
        // './ExperienceComponent': './projects/experience-studio/src/app/pages/experience.component.ts',
        './AppComponent': './projects/experience-studio/src/app/app.component.ts',
      },
      shared: {
        '@angular/core': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/common': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/router': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
        '@angular/animations': { singleton: true, strictVersion: true, requiredVersion: '^19.0.0' },
      },
    }),
  ],
};
