{"name": "chrome-umbrella", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:all": "concurrently --names \"marketing,launchpad,console,exp-studio,prod-studio\" --prefix-colors \"blue,green,yellow,magenta,cyan\" \"ng serve marketing -o --port 4200\" \"ng serve launchpad -o --port 4201\" \"ng serve console  -o --port 4202\" \"ng serve experienceStudio  -o --port 4203\" \"ng serve product-studio  -o --port 4204\"", "build": "ng build", "build:console": "ng build console --configuration production", "build:launchpad": "ng build launchpad --configuration production", "build:experience-studio": "ng build experienceStudio --configuration production", "build:product-studio": "ng build product-studio --configuration production", "build:marketing": "ng build marketing --configuration production", "build:all": "ng build console --configuration production && ng build launchpad --configuration production && ng build experienceStudio --configuration production && ng build product-studio --configuration production && ng build marketing --configuration production", "test": "ng test", "test:coverage": "ng test --watch=false --code-coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "reports": "npm run reports:generate && npm run reports:index && npm run serve:reports", "reports:update": "npm run reports:clean && npm run reports:generate && npm run reports:index", "reports:update:serve": "npm run reports:update && npm run serve:reports", "reports:generate": "node scripts/generate-eslint-html.js && npm run perf:lighthouse:html && npm run test:report:html", "reports:clean": "rm -rf reports/* && mkdir -p reports/linting reports/performance reports/coverage", "reports:index": "node scripts/generate-reports-index.js", "serve:reports": "cd reports && python3 -m http.server 8080 || npx serve . -l 8080", "eslint:report": "eslint . --format json --output-file reports/linting/eslint-report.json && node scripts/generate-eslint-html.js", "perf:lighthouse:html": "lighthouse http://localhost:4200 --output=html --output-path=reports/performance/lighthouse-report.html --chrome-flags='--headless --no-sandbox --disable-gpu --disable-dev-shm-usage'", "test:report:html": "node scripts/generate-test-html.js", "clean": "rm -rf dist reports coverage", "clean:all": "npm run clean && rm -rf node_modules package-lock.json", "docker:start": "./scripts/start-docker.sh start", "docker:stop": "./scripts/start-docker.sh stop", "docker:status": "./scripts/start-docker.sh status", "docker:rebuild": "./scripts/start-docker.sh rebuild"}, "private": true, "dependencies": {"@aava/play-core": "file:aava-play-core-1.2.3.tgz", "@angular/animations": "^19.1.0", "@angular/cdk": "19.2.1", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/material": "19.2.1", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/platform-server": "^19.1.0", "@angular/router": "^19.1.0", "@angular/ssr": "^19.2.0", "@awe/play-comp-library": "file:awe-play-comp-library-1.0.31.tgz", "@foblex/flow": "^17.7.1", "@foblex/mutator": "^1.0.7", "event-source-polyfill": "^1.0.31", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "lucide-angular": "^0.525.0", "monaco-editor": "^0.52.2", "ngx-markdown": "^19.1.1", "ngx-mask": "^19.0.0", "ngx-monaco-editor-v2": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.1", "@angular-devkit/build-angular": "^19.2.15", "@angular-eslint/builder": "^19.0.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/schematics": "^19.0.0", "@angular-eslint/template-parser": "^19.0.0", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.1.0", "@angular/language-service": "^19.1.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "compression-webpack-plugin": "^11.1.0", "concurrently": "^8.2.2", "css-loader": "^7.1.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.1.0", "event-source-polyfill": "^1.0.31", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lighthouse": "^11.6.0", "ngx-build-plus": "^19.0.0", "prettier": "^3.5.3", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.14", "typescript": "~5.7.2", "webpack": "^5.101.3", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1"}, "overrides": {"webpack": "^5.101.3"}}