# Elder Wand Multi-Project Application Suite

A comprehensive Angular monorepo containing multiple applications with streamlined development workflow and automated reporting system.

## 🏗️ Architecture

The Elder Wand suite consists of 5 main applications:

| Application       | Port | Description                  | URL                   |
| ----------------- | ---- | ---------------------------- | --------------------- |
| Marketing         | 4200 | Marketing and landing pages  | http://localhost:4200 |
| Launchpad         | 4201 | Main application launcher    | http://localhost:4201 |
| Console           | 4202 | Admin management interface   | http://localhost:4202 |
| Experience Studio | 4203 | Design and development tools | http://localhost:4203 |
| Product Studio    | 4204 | Product management tools     | http://localhost:4204 |

## 🚀 Quick Start

### Prerequisites

- **Node.js** (v18+ recommended)
- **npm** (v9+ recommended)
- **Git** for cloning the repository

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd elderwand

# Install dependencies
npm install

# Start all applications
npm run start:all
```

## 📋 Available Scripts

### 🚀 Development Scripts

```bash
# Start individual applications
npm run start                    # Start default application
npm run start:all               # Start all 5 applications simultaneously

# Build applications
npm run build                   # Build default application
npm run build:all              # Build all applications for production
```

### 🧪 Testing Scripts

```bash
# Run tests
npm run test                    # Run tests once
npm run test:coverage          # Run tests with coverage report
```

### 🔍 Code Quality Scripts

```bash
# Linting and formatting
npm run lint                    # Run ESLint
npm run lint:fix               # Fix linting issues automatically
npm run format                 # Format code with Prettier
npm run format:check           # Check code formatting
```

### 📊 Reporting System

```bash
# Generate and view all reports
npm run reports                # Generate reports + serve dashboard

# Individual report commands
npm run reports:generate       # Generate all reports (linting, performance, test coverage)
npm run reports:update         # Clean + generate + index reports
npm run reports:clean          # Clean all reports
npm run serve:reports          # Serve reports dashboard
```

### 🧹 Cleanup Scripts

```bash
npm run clean                  # Clean build artifacts
npm run clean:all             # Clean everything including node_modules
```

### 🐳 Docker Scripts

```bash
npm run docker:start          # Start all Docker containers
npm run docker:stop           # Stop all Docker containers
npm run docker:status         # Check Docker container status
npm run docker:rebuild        # Rebuild and restart all containers
```

## 📊 Reports Dashboard

The Elder Wand project includes a comprehensive reporting system that provides insights into code quality, performance, and test coverage.

### 🎯 One-Command Report Generation

```bash
# Generate all reports and serve dashboard
npm run reports
```

This single command will:

1. ✅ Generate ESLint code quality report
2. ✅ Generate Lighthouse performance report
3. ✅ Generate test coverage report
4. ✅ Create comprehensive dashboard
5. ✅ Serve reports at http://localhost:8080

### 📈 Available Reports

| Report Type           | Description                          | Location                                     |
| --------------------- | ------------------------------------ | -------------------------------------------- |
| **Code Quality**      | ESLint analysis with detailed issues | `reports/linting/eslint-report.html`         |
| **Performance**       | Lighthouse audit with scores         | `reports/performance/lighthouse-report.html` |
| **Test Coverage**     | Coverage summary and details         | `reports/coverage/test-report.html`          |
| **Detailed Coverage** | Line-by-line coverage analysis       | `reports/coverage/lcov-report/index.html`    |

### 🔄 Report Management

```bash
# Generate fresh reports
npm run reports:generate

# Clean and regenerate all reports
npm run reports:update

# Clean all reports
npm run reports:clean

# Serve reports dashboard
npm run serve:reports
```

## 🏗️ Project Structure

```
elderwand/
├── projects/                  # Angular applications
│   ├── marketing/            # Marketing and landing pages
│   ├── launchpad/            # Main application launcher
│   ├── console/              # Admin management interface
│   ├── experience-studio/    # Design and development tools
│   ├── product-studio/       # Product management tools
│   └── shared/               # Shared components and services
├── scripts/                  # Utility scripts
│   ├── generate-eslint-html.js
│   ├── generate-test-html.js
│   └── generate-reports-index.js
├── reports/                  # Generated reports
│   ├── index.html           # Main dashboard
│   ├── linting/             # Code quality reports
│   ├── performance/         # Performance reports
│   └── coverage/            # Test coverage reports
├── angular.json             # Angular workspace configuration
├── package.json             # Dependencies and scripts
└── README.md               # This documentation
```

## 🔧 Development Workflow

### 1. Start Development

```bash
# Start all applications for development
npm run start:all
```

### 2. Code Quality

```bash
# Check and fix code quality issues
npm run lint:fix
npm run format
```

### 3. Testing

```bash
# Run tests with coverage
npm run test:coverage
```

### 4. Generate Reports

```bash
# Generate comprehensive reports
npm run reports
```

### 5. Build for Production

```bash
# Build all applications
npm run build:all
```

## 🌍 Environment Configuration

Each application uses environment-specific configuration files:

- `projects/*/src/environments/environment.ts` - Development environment
- `projects/*/src/environments/environment.prod.ts` - Production environment

### Local Environment Setup

Each project includes a `localEnvSetup.js` file in the `public/` directory for local development configuration.

## 🐳 Docker Deployment

### Quick Docker Setup

```bash
# Start all services with Docker
npm run docker:start

# Check service status
npm run docker:status

# Stop all services
npm run docker:stop

# Rebuild and restart
npm run docker:rebuild
```

### Docker Architecture

The Docker setup uses individual containers for each application with Nginx reverse proxy:

| Service           | Container Port | Proxy Route  |
| ----------------- | -------------- | ------------ |
| Marketing         | 8080           | /            |
| Console           | 8081           | /console/    |
| Launchpad         | 8082           | /launchpad/  |
| Experience Studio | 8083           | /experience/ |
| Product Studio    | 8084           | /product/    |

## 🔍 Troubleshooting

### Common Issues

**Port Already in Use:**

```bash
# Check what's using the ports
lsof -i :4200-4204

# Kill processes using the ports
kill -9 $(lsof -t -i:4200-4204)
```

**Reports Not Generating:**

```bash
# Clean and regenerate reports
npm run reports:update

# Check if reports directory exists
ls -la reports/
```

**Build Failures:**

```bash
# Clean everything and reinstall
npm run clean:all
npm install
npm run build:all
```

### Getting Help

1. **Check the logs** - Look for error messages in the terminal
2. **Verify dependencies** - Run `npm install` to ensure all packages are installed
3. **Check ports** - Ensure no other services are using the required ports
4. **Generate reports** - Use `npm run reports` to get detailed analysis

## 📚 Additional Resources

- **Angular Documentation**: https://angular.io/docs
- **ESLint Configuration**: See `.eslintrc.js` for code quality rules
- **Prettier Configuration**: See `.prettierrc` for formatting rules
- **Docker Documentation**: https://docs.docker.com/

## 🎯 Summary

The Elder Wand project provides:

✅ **Streamlined Development** - One command to start all applications  
✅ **Comprehensive Reporting** - Automated code quality, performance, and test coverage reports  
✅ **Clean Scripts** - Simplified npm scripts for common tasks  
✅ **Docker Support** - Easy containerized deployment  
✅ **Code Quality** - ESLint and Prettier integration  
✅ **Testing** - Comprehensive test coverage reporting

### Quick Reference

| Command                 | Description                   |
| ----------------------- | ----------------------------- |
| `npm run start:all`     | Start all applications        |
| `npm run reports`       | Generate and view all reports |
| `npm run build:all`     | Build all applications        |
| `npm run test:coverage` | Run tests with coverage       |
| `npm run lint:fix`      | Fix code quality issues       |
| `npm run format`        | Format code                   |

---

**🚀 Elder Wand is ready for development and production deployment!**
